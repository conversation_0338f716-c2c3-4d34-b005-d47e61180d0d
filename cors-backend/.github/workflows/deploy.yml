name: Deploy to EC2

on:
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Build application
        run: pnpm build

      - name: Create .env file
        run: |
          cat > .env << EOL
          NODE_ENV=${{ vars.NODE_ENV }}
          PORT=${{ vars.PORT }}
          DATABASE_HOST=${{ secrets.DATABASE_HOST }}
          DATABASE_PORT=${{ secrets.DATABASE_PORT }}
          DATABASE_USERNAME=${{ secrets.DATABASE_USERNAME }}
          DATABASE_PASSWORD=${{ secrets.DATABASE_PASSWORD }}
          DATABASE_NAME=${{ secrets.DATABASE_NAME }}
          DATABASE_SSL_ENABLED=${{ secrets.DATABASE_SSL_ENABLED }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          JWT_ACCESS_EXPIRES_IN=${{ vars.JWT_ACCESS_EXPIRES_IN }}
          JWT_REFRESH_EXPIRES_IN=${{ vars.JWT_REFRESH_EXPIRES_IN }}
          SWAGGER_USERNAME=${{ secrets.SWAGGER_USERNAME }}
          SWAGGER_PASSWORD=${{ secrets.SWAGGER_PASSWORD }}
          CLOUDINARY_CLOUD_NAME=${{ secrets.CLOUDINARY_CLOUD_NAME }}
          CLOUDINARY_API_KEY=${{ secrets.CLOUDINARY_API_KEY }}
          CLOUDINARY_API_SECRET=${{ secrets.CLOUDINARY_API_SECRET }}
          STORAGE_DRIVER=${{ secrets.STORAGE_DRIVER }}
          SHOPIFY_ACCESS_TOKEN=${{ secrets.SHOPIFY_ACCESS_TOKEN }}
          SHOPIFY_STORE=${{ vars.SHOPIFY_STORE }}
          SHOPIFY_API_VERSION=${{ vars.SHOPIFY_API_VERSION }}
          SHOPIFY_WEBHOOK_SECRET=${{ secrets.SHOPIFY_WEBHOOK_SECRET }}
          SHOPIFY_STORE_URL=${{ vars.SHOPIFY_STORE_URL }}
          SHOPIFY_API_SECRET=${{ secrets.SHOPIFY_API_SECRET }}
          SHOPIFY_STORE_NAME=${{ vars.SHOPIFY_STORE_NAME }}
          NGROK_URL=${{ vars.NGROK_URL }}
          RESET_ACCESS_EXPIRES_IN=${{ vars.RESET_ACCESS_EXPIRES_IN }}
          SMTP_HOST=${{ vars.SMTP_HOST }}
          SMTP_PORT=${{ vars.SMTP_PORT }}
          SMTP_USERNAME=${{ vars.SMTP_USERNAME }}
          SMTP_PASSWORD=${{ secrets.SMTP_PASSWORD }}
          SMTP_FROM_EMAIL=${{ vars.SMTP_FROM_EMAIL }}
          FRONTEND_URL=${{ vars.FRONTEND_URL }}
          SKU_TRANSFORMER=${{ vars.SKU_TRANSFORMER }}
          BULLMQ_USERNAME=${{ vars.BULLMQ_USERNAME }}
          BULLMQ_PASSWORD=${{ vars.BULLMQ_PASSWORD }}
          REDIS_HOST=${{ vars.REDIS_HOST }}
          REDIS_PORT=${{ vars.REDIS_PORT }}
          REDIS_PASSWORD=${{ secrets.REDIS_PASSWORD }}
          REDIS_SSL_ENABLED=${{ vars.REDIS_SSL_ENABLED }}
          BASE_URL=${{ vars.BASE_URL }}
          CUTOUT_PRO_API_KEY=${{ secrets.CUTOUT_PRO_API_KEY }}
          CUTOUT_PRO_API_URL=${{ secrets.CUTOUT_PRO_API_URL }}
          EOL

      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh/
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/deploy_key.pem
          chmod 600 ~/.ssh/deploy_key.pem
          cat >>~/.ssh/config <<END
          Host ec2
            HostName ${{ secrets.EC2_HOST }}
            User ${{ secrets.EC2_USER }}
            IdentityFile ~/.ssh/deploy_key.pem
            StrictHostKeyChecking no
          END

      - name: Deploy to EC2
        run: |
          # Create deployment directory if it doesn't exist
          ssh ec2 "mkdir -p /home/<USER>/app"

          # Copy built files and env to EC2
          scp -r dist/* ec2:/home/<USER>/app/
          scp package.json pnpm-lock.yaml .env ec2:/home/<USER>/app/

          # Source NVM and set up Node environment before running commands
          ssh ec2 "cd /home/<USER>/app && \
            source ~/.nvm/nvm.sh && \
            nvm use 22 && \
            pnpm install --prod && \
            pm2 restart cors-backend || pm2 start src/main.js --name cors-backend"
