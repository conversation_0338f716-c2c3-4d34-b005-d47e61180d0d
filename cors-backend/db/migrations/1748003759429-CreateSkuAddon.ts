import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSkuAddon1748003759429 implements MigrationInterface {
    name = 'CreateSkuAddon1748003759429'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_368e146b785b574f42ae9e53d5e"`);
        await queryRunner.query(`CREATE TYPE "public"."sku_addon_relationtype_enum" AS ENUM('parent', 'child', 'addon', 'upsell')`);
        await queryRunner.query(`CREATE TABLE "sku_addon" ("productSkuId" uuid NOT NULL, "relationSkuId" uuid NOT NULL, "relationType" "public"."sku_addon_relationtype_enum", CONSTRAINT "PK_c098db54eb5891a1429f76f400e" PRIMARY KEY ("productSkuId", "relationSkuId"))`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "roleId"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "isAddon" boolean`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_addonlevel_enum" AS ENUM('line_item', 'order')`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "addonLevel" "public"."product_sku_addonlevel_enum"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "variantId" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "variantId"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "addonLevel"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_addonlevel_enum"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "isAddon"`);
        await queryRunner.query(`ALTER TABLE "users" ADD "roleId" uuid`);
        await queryRunner.query(`DROP TABLE "sku_addon"`);
        await queryRunner.query(`DROP TYPE "public"."sku_addon_relationtype_enum"`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_368e146b785b574f42ae9e53d5e" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
