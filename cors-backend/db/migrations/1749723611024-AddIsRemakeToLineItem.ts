import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsRemakeToLineItem1749723611024 implements MigrationInterface {
  name = 'AddIsRemakeToLineItem1749723611024';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "isRemake" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "line_items" DROP COLUMN "isRemake"`);
  }
}
