import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateQueues1750749844760 implements MigrationInterface {
  name = 'CreateQueues1750749844760';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "queues" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "maxItemsToAssign" integer NOT NULL DEFAULT '5', CONSTRAINT "UQ_a290d70c28ba7f1c5d2600da849" UNIQUE ("name"), CONSTRAINT "PK_d966f9eb39a9396658387071bb3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a290d70c28ba7f1c5d2600da84" ON "queues" ("name") `,
    );
    await queryRunner.query(`ALTER TABLE "line_items" ADD "queueId" uuid`);
    await queryRunner.query(`ALTER TABLE "line_items" ADD "assignedToId" uuid`);
    await queryRunner.query(`ALTER TABLE "attachments" ADD "queueId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "assignedToId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD CONSTRAINT "FK_7ecf26a116517cf87c4e6d5890d" FOREIGN KEY ("queueId") REFERENCES "queues"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD CONSTRAINT "FK_70a1a1fc392daeba854b7792fb3" FOREIGN KEY ("assignedToId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD CONSTRAINT "FK_24f03c1bf1c8053779f0fa8bbc8" FOREIGN KEY ("queueId") REFERENCES "queues"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD CONSTRAINT "FK_627c666c8cd4d72217c5d4bdffe" FOREIGN KEY ("assignedToId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP CONSTRAINT "FK_627c666c8cd4d72217c5d4bdffe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP CONSTRAINT "FK_24f03c1bf1c8053779f0fa8bbc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP CONSTRAINT "FK_70a1a1fc392daeba854b7792fb3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP CONSTRAINT "FK_7ecf26a116517cf87c4e6d5890d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP COLUMN "assignedToId"`,
    );
    await queryRunner.query(`ALTER TABLE "attachments" DROP COLUMN "queueId"`);
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "assignedToId"`,
    );
    await queryRunner.query(`ALTER TABLE "line_items" DROP COLUMN "queueId"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a290d70c28ba7f1c5d2600da84"`,
    );
    await queryRunner.query(`DROP TABLE "queues"`);
  }
}
