import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveAttachableColumnFromAttachments1750413968926
  implements MigrationInterface
{
  name = 'RemoveAttachableColumnFromAttachments1750413968926';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9a92db6a1aba51ef2ff35e13fa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP COLUMN "attachableType"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP COLUMN "attachableId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "cutoutProImageUrl" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "attachments" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."attachments_status_enum"`);
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "status" character varying NOT NULL DEFAULT 'pending'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "attachments" DROP COLUMN "status"`);
    await queryRunner.query(
      `CREATE TYPE "public"."attachments_status_enum" AS ENUM('pending', 'approved', 'rejected')`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "status" "public"."attachments_status_enum" NOT NULL DEFAULT 'pending'`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP COLUMN "cutoutProImageUrl"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "attachableId" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "attachableType" character varying`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9a92db6a1aba51ef2ff35e13fa" ON "attachments" ("attachableType", "attachableId") `,
    );
  }
}
