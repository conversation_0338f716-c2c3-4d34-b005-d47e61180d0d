import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompletedArtFileUrlToAttachments1751024704951
  implements MigrationInterface
{
  name = 'AddCompletedArtFileUrlToAttachments1751024704951';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "attachments" ADD "completedArtFileUrl" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "attachments" DROP COLUMN "completedArtFileUrl"`,
    );
  }
}
