import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPermissionCount1746015148297 implements MigrationInterface {
    name = 'AddPermissionCount1746015148297'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" ADD "permissionCount" integer NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "permissionCount"`);
    }

}
