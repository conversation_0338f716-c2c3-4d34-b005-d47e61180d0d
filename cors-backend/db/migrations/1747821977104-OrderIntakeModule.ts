import { MigrationInterface, QueryRunner } from "typeorm";

export class OrderIntakeModule1747821977104 implements MigrationInterface {
    name = 'OrderIntakeModule1747821977104'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DO $$
            BEGIN
              IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'orders_orderstatus_enum') THEN
                CREATE TYPE "orders_orderstatus_enum" AS ENUM ('unfulfilled', 'partially fulfilled', 'fulfilled');
              END IF;
            END
            $$;
          `);
        await queryRunner.query(`CREATE TABLE "orders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "shopifyOrderNumber" bigint, "shopifyOrderId" bigint, "orderDate" TIMESTAMP NOT NULL, "orderStatus" "public"."orders_orderstatus_enum" NOT NULL DEFAULT 'unfulfilled', "statusUpdatedAt" TIMESTAMP, "flagged" boolean NOT NULL DEFAULT false, "flaggedAt" TIMESTAMP, "customerFirstName" character varying NOT NULL, "customerLastName" character varying NOT NULL, "customerEmail" character varying NOT NULL, "customerPhoneNumber" character varying NOT NULL, "shopifyCustomerId" bigint, "paymentInformation" jsonb, "billingAddress" jsonb NOT NULL, "shippingAddress" jsonb NOT NULL, "itemCount" integer NOT NULL, "priorities" text array NOT NULL DEFAULT '{}', CONSTRAINT "UQ_8747ba913ab15ad2a498f6802d8" UNIQUE ("shopifyOrderId"), CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_2d0e8209f085fbf45153cd1639" ON "orders" ("shopifyOrderNumber") `);
        await queryRunner.query(`CREATE TYPE "public"."line_item_requests_type_enum" AS ENUM('remake', 'artwork', 'customer_contact')`);
        await queryRunner.query(`CREATE TABLE "line_item_requests" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "type" "public"."line_item_requests_type_enum" NOT NULL, "notes" character varying, "lineItemId" uuid NOT NULL, CONSTRAINT "PK_05cd81d697959bfa5e8e13cb943" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_48dbeb706f3c895ad802c89a7b" ON "line_item_requests" ("lineItemId", "type") `);
        await queryRunner.query(`CREATE TYPE "public"."attachments_status_enum" AS ENUM('pending', 'approved', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "attachments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "filename" character varying NOT NULL, "url" character varying NOT NULL, "mimetype" character varying NOT NULL, "size" integer NOT NULL, "attachableType" character varying NOT NULL, "attachableId" character varying NOT NULL, "status" "public"."attachments_status_enum" NOT NULL DEFAULT 'pending', "lineItemId" uuid, "requestId" uuid, CONSTRAINT "PK_5e1f050bcff31e3084a1d662412" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_9a92db6a1aba51ef2ff35e13fa" ON "attachments" ("attachableType", "attachableId") `);
        await queryRunner.query(`CREATE TABLE "line_items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "shopifyItemId" bigint, "status" character varying, "itemNumber" character varying, "flagged" boolean NOT NULL DEFAULT false, "flagReason" character varying, "cancelReason" character varying, "quantity" integer, "priority" character varying, "metadata" jsonb, "productSkuId" uuid, "productId" uuid, "orderId" uuid, CONSTRAINT "PK_6d227c876e374542dc9bb44dfb4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "workflowCategory" character varying`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "price" numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "shippingWeight"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "shippingWeight" numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "productLength"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "productLength" numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "productWidth"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "productWidth" numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "productHeight"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "productHeight" numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "line_item_requests" ADD CONSTRAINT "FK_db3bfa750306e609450b49e8865" FOREIGN KEY ("lineItemId") REFERENCES "line_items"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "attachments" ADD CONSTRAINT "FK_3514ac706f1da285334e24d99d7" FOREIGN KEY ("lineItemId") REFERENCES "line_items"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "attachments" ADD CONSTRAINT "FK_82feb2de2c111200f53330066e2" FOREIGN KEY ("requestId") REFERENCES "line_item_requests"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "line_items" ADD CONSTRAINT "FK_94264119ca7539f84a313655ebe" FOREIGN KEY ("productSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "line_items" ADD CONSTRAINT "FK_d88f16288d23557bf034fdf53e2" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "line_items" ADD CONSTRAINT "FK_e08154b23e1e1654b170142b0c1" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "line_items" DROP CONSTRAINT "FK_e08154b23e1e1654b170142b0c1"`);
        await queryRunner.query(`ALTER TABLE "line_items" DROP CONSTRAINT "FK_d88f16288d23557bf034fdf53e2"`);
        await queryRunner.query(`ALTER TABLE "line_items" DROP CONSTRAINT "FK_94264119ca7539f84a313655ebe"`);
        await queryRunner.query(`ALTER TABLE "attachments" DROP CONSTRAINT "FK_82feb2de2c111200f53330066e2"`);
        await queryRunner.query(`ALTER TABLE "attachments" DROP CONSTRAINT "FK_3514ac706f1da285334e24d99d7"`);
        await queryRunner.query(`ALTER TABLE "line_item_requests" DROP CONSTRAINT "FK_db3bfa750306e609450b49e8865"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "productHeight"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "productHeight" integer`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "productWidth"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "productWidth" integer`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "productLength"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "productLength" integer`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "shippingWeight"`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD "shippingWeight" integer`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "price"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "workflowCategory"`);
        await queryRunner.query(`DROP TABLE "line_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9a92db6a1aba51ef2ff35e13fa"`);
        await queryRunner.query(`DROP TABLE "attachments"`);
        await queryRunner.query(`DROP TYPE "public"."attachments_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_48dbeb706f3c895ad802c89a7b"`);
        await queryRunner.query(`DROP TABLE "line_item_requests"`);
        await queryRunner.query(`DROP TYPE "public"."line_item_requests_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2d0e8209f085fbf45153cd1639"`);
        await queryRunner.query(`DROP TABLE "orders"`);
        await queryRunner.query(`DROP TYPE "public"."orders_orderstatus_enum"`);
    }

}
