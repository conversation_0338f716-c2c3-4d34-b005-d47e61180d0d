import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateVendorFields1747825733721 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add contact name column
    await queryRunner.query(`
            ALTER TABLE vendors
            ADD COLUMN IF NOT EXISTS "contactName" varchar;
        `);

    // Add rush production time days column
    await queryRunner.query(`
            ALTER TABLE vendor_supported_skus
            ADD COLUMN IF NOT EXISTS "rushProductionTimeDays" bigint;
        `);

    // Add precision and scale to fulfillment and defect rates
    await queryRunner.query(`
            ALTER TABLE vendors 
            ALTER COLUMN "fulfillmentRate" TYPE DECIMAL(5,2),
            ALTER COLUMN "defectRate" TYPE DECIMAL(5,2),
            ALTER COLUMN "performanceGap" TYPE DECIMAL(10,2)
        `);

    // Add new columns for preferred SKUs and rush production options
    await queryRunner.query(`
            ALTER TABLE vendors
            ADD COLUMN IF NOT EXISTS "preferredSkusOverCompetitors" BOOLEAN DEFAULT false,
            ADD COLUMN IF NOT EXISTS "rushProductionOptions" VARCHAR(50) DEFAULT '3,5,7,14,28,42'
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove added columns from vendors table
    await queryRunner.query(`
            ALTER TABLE vendors
            DROP COLUMN IF EXISTS "contactName",
            DROP COLUMN IF EXISTS "preferredSkusOverCompetitors",
            DROP COLUMN IF EXISTS "rushProductionOptions"
        `);

    // Remove added column from vendor_supported_skus table
    await queryRunner.query(`
            ALTER TABLE vendor_supported_skus
            DROP COLUMN IF EXISTS "rushProductionTimeDays"
        `);

    // Revert decimal columns back to decimal without precision/scale
    await queryRunner.query(`
            ALTER TABLE vendors 
            ALTER COLUMN "fulfillmentRate" TYPE DECIMAL,
            ALTER COLUMN "defectRate" TYPE DECIMAL,
            ALTER COLUMN "performanceGap" TYPE DECIMAL
        `);
  }
}
