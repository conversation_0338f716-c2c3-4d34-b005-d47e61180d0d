import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProductsAddonToLineItem1749548033487
  implements MigrationInterface
{
  name = 'AddProductsAddonToLineItem1749548033487';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "hasGift" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ALTER COLUMN "shopifyOrderNumber" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ALTER COLUMN "shopifyOrderId" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "zipperPouch" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "heartbeatBox" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "cuddleCrate" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "customBandana" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "customBandanaValue" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "remakeReason" text array NOT NULL DEFAULT '{}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "detailedRemakeReason" text array NOT NULL DEFAULT '{}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "vendors" DROP COLUMN "rushProductionOptions"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vendors" ADD "rushProductionOptions" character varying DEFAULT '3,5,7,14,28,42'`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ALTER COLUMN "notes" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" ADD "digitalDownload" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "digitalDownload"`,
    );

    await queryRunner.query(
      `ALTER TABLE "line_items" ALTER COLUMN "notes" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vendors" DROP COLUMN "rushProductionOptions"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vendors" ADD "rushProductionOptions" character varying(50) DEFAULT '3,5,7,14,28,42'`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "detailedRemakeReason"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "remakeReason"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "customBandanaValue"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "customBandana"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "cuddleCrate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "heartbeatBox"`,
    );
    await queryRunner.query(
      `ALTER TABLE "line_items" DROP COLUMN "zipperPouch"`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ALTER COLUMN "shopifyOrderId" TYPE bigint`,
    );
    await queryRunner.query(
      `ALTER TABLE "orders" ALTER COLUMN "shopifyOrderNumber" TYPE bigint`,
    );
    await queryRunner.query(`ALTER TABLE "line_items" DROP COLUMN "hasGift"`);
  }
}
