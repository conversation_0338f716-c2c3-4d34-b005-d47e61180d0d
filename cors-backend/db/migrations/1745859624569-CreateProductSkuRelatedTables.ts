import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateProductSkuRelatedTables1745859624569 implements MigrationInterface {
    name = 'CreateProductSkuRelatedTables1745859624569'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "artwork_types" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, CONSTRAINT "PK_318a5d68e341901486166d9958d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "vendors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "sku" character varying NOT NULL, "maxCapacityPerDay" integer NOT NULL, "productionTime" integer NOT NULL, CONSTRAINT "PK_9c956c9797edfae5c6ddacc4e6e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "sku_relationships" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "parentSkuId" uuid, "childSkuId" uuid, "upSellParentSkuId" uuid, CONSTRAINT "PK_7137ebd775bbbbeae887c4b2275" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_croppingmethod_enum" AS ENUM('MANUAL', 'CUTOUT_PRO')`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_croptype_enum" AS ENUM('FACE_CUTOUT', 'BACKGROUND_REMOVAL')`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_fileuploadformat_enum" AS ENUM('.bmp', '.png', '.jpeg')`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_imageinheritrule_enum" AS ENUM('AUTO_INHERIT', 'REQUIRES_REVIEW')`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_exceptionhandlingrule_enum" AS ENUM('Send to Image Needed Queue', 'FLAuto-Assign from First Image on OrderAG')`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_shippingmethod_enum" AS ENUM('STANDARD', 'EXPEDITED')`);
        await queryRunner.query(`CREATE TYPE "public"."product_sku_routingmethod_enum" AS ENUM('OMS', 'CORS')`);
        await queryRunner.query(`CREATE TABLE "product_sku" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "sku" character varying NOT NULL, "hasRush" boolean, "rushDays" integer, "imageNamingConvention" character varying, "imageInheritancePriority" integer, "followupTiming" integer, "shipStationStore" character varying, "shippingWeight" numeric, "productLength" integer, "productWidth" integer, "productHeight" integer, "chinaWOFEPrice" numeric, "canUpSold" boolean, "canCrossSold" boolean, "requireImageUpload" boolean, "requireCropping" boolean, "croppingMethod" "public"."product_sku_croppingmethod_enum", "cropType" "public"."product_sku_croptype_enum", "croppingReviewRequired" boolean, "artworkRequired" boolean, "requireCustomerArtworkApproval" boolean, "requireTemplate" boolean, "fileUploadFormat" "public"."product_sku_fileuploadformat_enum", "canInheritImage" boolean, "imageInheritRule" "public"."product_sku_imageinheritrule_enum", "canManualOverride" boolean, "exceptionHandlingRule" "public"."product_sku_exceptionhandlingrule_enum", "customerFollowupEnabled" boolean, "requirePreprocessing" boolean, "shippingMethod" "public"."product_sku_shippingmethod_enum", "hasWorkPaper" boolean, "routingMethod" "public"."product_sku_routingmethod_enum", "isActive" boolean, "artworkTypesId" uuid, "primaryVendorId" uuid, CONSTRAINT "UQ_25b5fb74c3e38f8688b61db7cd2" UNIQUE ("sku"), CONSTRAINT "PK_4edea686db1e12af5b8eaa9ec31" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_25b5fb74c3e38f8688b61db7cd" ON "product_sku" ("sku") `);
        await queryRunner.query(`CREATE TYPE "public"."products_category_enum" AS ENUM('Rush & Add Ons', 'Pajamas', 'Plush', 'Jewellery', 'Socks', 'Sweaters', 'Apparel', 'Portraits', 'Accessories')`);
        await queryRunner.query(`CREATE TABLE "products" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying NOT NULL, "category" "public"."products_category_enum" NOT NULL, "description" text, "shopifyProductId" character varying, "metadata" jsonb, CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_sku_product_pivot" ("product_id" uuid NOT NULL, "sku_id" uuid NOT NULL, CONSTRAINT "PK_c947a1c4df275732606c30c6896" PRIMARY KEY ("product_id", "sku_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_183ef5cba614b4194dfe25bbbf" ON "product_sku_product_pivot" ("product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_141489d22d1f67a58108a38d3f" ON "product_sku_product_pivot" ("sku_id") `);
        await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_8d8a7a58e45866b9f30336ab0f6" FOREIGN KEY ("parentSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_5c5f06898204ed602c273db7b47" FOREIGN KEY ("childSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_5fa6c0a83b25414b8dde8100e6a" FOREIGN KEY ("upSellParentSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD CONSTRAINT "FK_ee7508b9ca39a29fc278d65abf5" FOREIGN KEY ("artworkTypesId") REFERENCES "artwork_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_sku" ADD CONSTRAINT "FK_8667ba49865cbb3a4fb4c7a5404" FOREIGN KEY ("primaryVendorId") REFERENCES "vendors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_sku_product_pivot" ADD CONSTRAINT "FK_183ef5cba614b4194dfe25bbbf3" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "product_sku_product_pivot" ADD CONSTRAINT "FK_141489d22d1f67a58108a38d3fb" FOREIGN KEY ("sku_id") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_sku_product_pivot" DROP CONSTRAINT "FK_141489d22d1f67a58108a38d3fb"`);
        await queryRunner.query(`ALTER TABLE "product_sku_product_pivot" DROP CONSTRAINT "FK_183ef5cba614b4194dfe25bbbf3"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP CONSTRAINT "FK_8667ba49865cbb3a4fb4c7a5404"`);
        await queryRunner.query(`ALTER TABLE "product_sku" DROP CONSTRAINT "FK_ee7508b9ca39a29fc278d65abf5"`);
        await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_5fa6c0a83b25414b8dde8100e6a"`);
        await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_5c5f06898204ed602c273db7b47"`);
        await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_8d8a7a58e45866b9f30336ab0f6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_141489d22d1f67a58108a38d3f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_183ef5cba614b4194dfe25bbbf"`);
        await queryRunner.query(`DROP TABLE "product_sku_product_pivot"`);
        await queryRunner.query(`DROP TABLE "products"`);
        await queryRunner.query(`DROP TYPE "public"."products_category_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_25b5fb74c3e38f8688b61db7cd"`);
        await queryRunner.query(`DROP TABLE "product_sku"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_routingmethod_enum"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_shippingmethod_enum"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_exceptionhandlingrule_enum"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_imageinheritrule_enum"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_fileuploadformat_enum"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_croptype_enum"`);
        await queryRunner.query(`DROP TYPE "public"."product_sku_croppingmethod_enum"`);
        await queryRunner.query(`DROP TABLE "sku_relationships"`);
        await queryRunner.query(`DROP TABLE "vendors"`);
        await queryRunner.query(`DROP TABLE "artwork_types"`);
    }

}
