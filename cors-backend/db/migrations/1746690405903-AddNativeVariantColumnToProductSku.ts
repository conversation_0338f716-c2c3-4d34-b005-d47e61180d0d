import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNativeVariantColumnToProductSku1746690405903 implements MigrationInterface {
  name = 'AddNativeVariantColumnToProductSku1746690405903'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "product_sku" ADD "shopifyNativeVariant" character varying`);
    await queryRunner.query(`ALTER TABLE "roles" ALTER COLUMN "name" TYPE citext`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "firstName" TYPE citext`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "lastName" TYPE citext`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "email" TYPE citext`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "email" TYPE character varying`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "lastName" TYPE character varying`);
    await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "firstName" TYPE character varying`);
    await queryRunner.query(`ALTER TABLE "roles" ALTER COLUMN "name" TYPE character varying`);
    await queryRunner.query(`ALTER TABLE "product_sku" DROP COLUMN "shopifyNativeVariant"`);
  }
}
