import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  SoftRemoveEvent,
  RecoverEvent,
  ObjectLiteral,
} from 'typeorm';
import { Injectable, Logger } from '@nestjs/common';
import { BaseEntity } from './base.entity';
import { HistoryService } from './history.service';
import { ClsService } from 'nestjs-cls';

@Injectable()
@EventSubscriber()
export class HistorySubscriber implements EntitySubscriberInterface {
  private readonly logger = new Logger(HistorySubscriber.name);

  constructor(
    dataSource: DataSource,
    private readonly historyService: HistoryService,
    private readonly clsService: ClsService,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return BaseEntity;
  }

  private getEntityName(target: any): string {
    return target.constructor.name;
  }

  private isObject(item: unknown): item is Record<string, unknown> {
    return Boolean(item && typeof item === 'object' && !Array.isArray(item));
  }

  private isEqual(value1: unknown, value2: unknown): boolean {
    // Handle null/undefined
    if (!value1 && !value2) return true;
    if (!value1 || !value2) return false;

    // Handle dates
    if (value1 instanceof Date && value2 instanceof Date) {
      return value1.getTime() === value2.getTime();
    }

    // Handle arrays
    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (value1.length !== value2.length) return false;
      return value1.every((item, index) => this.isEqual(item, value2[index]));
    }

    // Handle primitive types
    if (!this.isObject(value1) || !this.isObject(value2)) {
      return value1 === value2;
    }

    // For objects, compare their stringified versions
    // This helps handle TypeORM entities and circular references
    try {
      const str1 = JSON.stringify(this.sanitizeEntity(value1));
      const str2 = JSON.stringify(this.sanitizeEntity(value2));
      return str1 === str2;
    } catch (e) {
      // If JSON stringify fails (e.g., circular references), consider them equal
      return true;
    }
  }

  private sanitizeEntity(entity: unknown): unknown {
    if (!this.isObject(entity)) return entity;

    // Create a new object to avoid modifying the original
    const sanitized: ObjectLiteral = {};
    const entityObject = entity as ObjectLiteral;

    // Map of relation fields to their name properties
    const relationNameFields = new Map([
      ['artworkType', 'name'],
      ['primaryVendor', 'name'],
      ['products', 'name'],
      ['parentSku', 'sku'],
      ['childSku', 'sku'],
    ]);

    for (const [key, value] of Object.entries(entityObject)) {
      // Skip TypeORM internal properties and functions
      if (key.startsWith('__') || typeof value === 'function') {
        continue;
      }

      // Skip common TypeORM relation metadata
      if (['metadata', 'manager', 'queryRunner'].includes(key)) {
        continue;
      }

      // For relations, keep both id and name/sku
      if (this.isObject(value) && 'id' in value) {
        const nameField = relationNameFields.get(key);
        if (nameField && nameField in value) {
          sanitized[key] = {
            id: value.id,
            [nameField]: value[nameField]
          };
        } else {
          sanitized[key] = { id: value.id };
        }
        continue;
      }

      // Handle arrays (including relation arrays)
      if (Array.isArray(value)) {
        sanitized[key] = value.map(item => {
          if (this.isObject(item) && 'id' in item) {
            const nameField = relationNameFields.get(key);
            if (nameField && nameField in item) {
              return {
                id: item.id,
                [nameField]: item[nameField]
              };
            }
            return { id: item.id };
          }
          return item;
        });
        continue;
      }

      sanitized[key] = value;
    }

    return sanitized;
  }

  private calculateChanges(
    oldValue: unknown,
    newValue: unknown,
  ): { field: string; oldValue: any; newValue: any }[] {
    if (!oldValue || !newValue) return [];

    const changes: { field: string; oldValue: any; newValue: any }[] = [];
    const sanitizedOld = this.sanitizeEntity(oldValue) as ObjectLiteral;
    const sanitizedNew = this.sanitizeEntity(newValue) as ObjectLiteral;
    const allKeys = new Set([
      ...Object.keys(sanitizedOld),
      ...Object.keys(sanitizedNew),
    ]);

    // Fields to ignore in history
    const ignoredFields = new Set([
      'createdAt',
      'updatedAt',
      'deletedAt',
      '__entity',
      '__hasRelations',
      '__metadata',
    ]);

    // Map of relation fields to their name properties
    const relationNameFields = new Map([
      ['artworkType', 'name'],
      ['primaryVendor', 'name'],
      ['products', 'name'],
      ['parentSku', 'sku'],
      ['childSku', 'sku'],
    ]);

    for (const key of allKeys) {
      // Skip internal TypeORM properties, functions, and ignored fields
      if (
        key.startsWith('__') ||
        typeof sanitizedOld[key] === 'function' ||
        typeof sanitizedNew[key] === 'function' ||
        ignoredFields.has(key)
      ) {
        continue;
      }

      // Skip if both values are null/undefined
      if (!sanitizedOld[key] && !sanitizedNew[key]) {
        continue;
      }

      // Skip if values are equal
      if (this.isEqual(sanitizedOld[key], sanitizedNew[key])) {
        continue;
      }

      // Handle relation fields
      if (this.isObject(sanitizedOld[key]) || this.isObject(sanitizedNew[key])) {
        const nameField = relationNameFields.get(key);
        if (nameField) {
          const oldName = sanitizedOld[key]?.[nameField] || null;
          const newName = sanitizedNew[key]?.[nameField] || null;
          
          // Only add the change if the names are different
          if (oldName !== newName) {
            changes.push({
              field: key,
              oldValue: oldName,
              newValue: newName,
            });
          }
          continue;
        }
      }

      changes.push({
        field: key,
        oldValue: sanitizedOld[key],
        newValue: sanitizedNew[key],
      });
    }

    return changes;
  }

  private getUserContext(): { userId: string | null } {
    try {
      const userId = this.clsService.get('userId');
      if (!userId) {
        this.logger.debug('No userId found in CLS context');
      }
      return { userId: userId || null };
    } catch (error) {
      this.logger.warn('Error getting user context:', error);
      return { userId: null };
    }
  }

  async afterInsert(event: InsertEvent<BaseEntity>): Promise<void> {
    const queryRunner = event.queryRunner;
    const changes = this.calculateChanges(null, event.entity);
    const { userId } = this.getUserContext();

    if (queryRunner?.isTransactionActive) {
      await this.historyService.recordHistory(
        this.getEntityName(event.entity),
        event.entity.id,
        'CREATE',
        null,
        event.entity,
        changes,
        { userId },
        queryRunner,
      );
    } else {
      const newQueryRunner = event.connection.createQueryRunner();
      await newQueryRunner.connect();
      await newQueryRunner.startTransaction();

      try {
        await this.historyService.recordHistory(
          this.getEntityName(event.entity),
          event.entity.id,
          'CREATE',
          null,
          event.entity,
          changes,
          { userId },
          newQueryRunner,
        );
        await newQueryRunner.commitTransaction();
      } catch (err) {
        await newQueryRunner.rollbackTransaction();
        throw err;
      } finally {
        await newQueryRunner.release();
      }
    }
  }

  async beforeUpdate(event: UpdateEvent<BaseEntity>): Promise<void> {
    if (!event.entity || !event.databaseEntity) return;

    const queryRunner = event.queryRunner;
    const newValue = { ...event.databaseEntity, ...event.entity };
    const changes = this.calculateChanges(event.databaseEntity, newValue);
    const { userId } = this.getUserContext();

    if (changes.length === 0) {
      return;
    }

    if (queryRunner?.isTransactionActive) {
      await this.historyService.recordHistory(
        this.getEntityName(event.entity),
        event.entity.id,
        'UPDATE',
        event.databaseEntity,
        newValue,
        changes,
        { userId },
        queryRunner,
      );
    } else {
      const newQueryRunner = event.connection.createQueryRunner();
      await newQueryRunner.connect();
      await newQueryRunner.startTransaction();

      try {
        await this.historyService.recordHistory(
          this.getEntityName(event.entity),
          event.entity.id,
          'UPDATE',
          event.databaseEntity,
          newValue,
          changes,
          { userId },
          newQueryRunner,
        );
        await newQueryRunner.commitTransaction();
      } catch (err) {
        await newQueryRunner.rollbackTransaction();
        throw err;
      } finally {
        await newQueryRunner.release();
      }
    }
  }

  async beforeSoftRemove(event: SoftRemoveEvent<BaseEntity>): Promise<void> {
    if (!event.entity) return;

    const queryRunner = event.queryRunner;
    const changes = this.calculateChanges(event.entity, {
      deletedAt: new Date(),
    });
    const { userId } = this.getUserContext();

    if (queryRunner?.isTransactionActive) {
      await this.historyService.recordHistory(
        this.getEntityName(event.entity),
        event.entity.id,
        'DELETE',
        event.entity,
        null,
        changes,
        { userId },
        queryRunner,
      );
    } else {
      const newQueryRunner = event.connection.createQueryRunner();
      await newQueryRunner.connect();
      await newQueryRunner.startTransaction();

      try {
        await this.historyService.recordHistory(
          this.getEntityName(event.entity),
          event.entity.id,
          'DELETE',
          event.entity,
          null,
          changes,
          { userId },
          newQueryRunner,
        );
        await newQueryRunner.commitTransaction();
      } catch (err) {
        await newQueryRunner.rollbackTransaction();
        throw err;
      } finally {
        await newQueryRunner.release();
      }
    }
  }

  async beforeRecover(event: RecoverEvent<BaseEntity>): Promise<void> {
    if (!event.entity) return;

    const queryRunner = event.queryRunner;
    const changes = this.calculateChanges(
      { deletedAt: event.entity.deletedAt },
      { deletedAt: null },
    );
    const { userId } = this.getUserContext();

    if (queryRunner?.isTransactionActive) {
      await this.historyService.recordHistory(
        this.getEntityName(event.entity),
        event.entity.id,
        'RESTORE',
        null,
        event.entity,
        changes,
        { userId },
        queryRunner,
      );
    } else {
      const newQueryRunner = event.connection.createQueryRunner();
      await newQueryRunner.connect();
      await newQueryRunner.startTransaction();

      try {
        await this.historyService.recordHistory(
          this.getEntityName(event.entity),
          event.entity.id,
          'RESTORE',
          null,
          event.entity,
          changes,
          { userId },
          newQueryRunner,
        );
        await newQueryRunner.commitTransaction();
      } catch (err) {
        await newQueryRunner.rollbackTransaction();
        throw err;
      } finally {
        await newQueryRunner.release();
      }
    }
  }
}
