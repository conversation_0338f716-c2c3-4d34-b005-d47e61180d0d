import { Repository, DeepPartial, FindOptionsWhere } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DBHelper, IOptions } from 'src/helpers/db.helpers';
import { FilterHelper } from 'src/helpers/filter.helper';
import { SortHelper } from 'src/helpers/sort.helper';

@Injectable()
export abstract class BaseService<T extends BaseEntity> {
  constructor(
    @InjectRepository(Repository<T>)
    protected readonly repository: Repository<T>,
  ) {}

  async create(createDto: DeepPartial<T>): Promise<T> {
    const entity = this.repository.create(createDto as DeepPartial<T>);
    return await this.repository.save(entity);
  }

  async findAll(sOptions?: IOptions<T>): Promise<{
    data: T[];
    count: number;
    page: number;
    limit: number;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('entity');

    if (sOptions?.q) {
      const orConditions = sOptions.q.split(';');
      orConditions.forEach(condition => {
        const [field, op, value] = condition.split(':');

        if (field.includes('.')) {
          // Handle relation field search
          const [relationName, relationField] = field.split('.');
          queryBuilder.orWhere(
            `${relationName}.${relationField} ILIKE :${relationName}${relationField}`,
            {
              [`${relationName}${relationField}`]: `%${value}%`,
            },
          );
        }
      });
    }

    // Handle AND conditions (fq parameter)
    if (sOptions?.fq) {
      const andConditions = sOptions.fq.split(';');
      andConditions.forEach(condition => {
        const [field, op, value] = condition.split(':');

        if (field.includes('.')) {
          // Handle relation field search
          const [relationName, relationField] = field.split('.');
          queryBuilder.andWhere(
            `${relationName}.${relationField} ILIKE :${relationName}${relationField}`,
            {
              [`${relationName}${relationField}`]: `%${value}%`,
            },
          );
        }
      });
    }

    const filters =
      FilterHelper.parseCombinedFilters(
        sOptions?.q
          ?.split(';')
          .filter(q => !q.split(':')[0].includes('.'))
          .join(';'),
        sOptions?.fq
          ?.split(';')
          .filter(q => !q.split(':')[0].includes('.'))
          .join(';'),
        this.repository,
      ) || {};

    if (sOptions?.relations?.length) {
      sOptions.relations.forEach(relation => {
        if (relation.includes('.')) {
          const parts = relation.split('.');
          let alias = 'entity';
          let path = '';

          for (let i = 0; i < parts.length; i++) {
            const part = parts[i];
            path = path ? `${path}.${part}` : part;
            const nextAlias = `${part}_${i}`;
            queryBuilder.leftJoinAndSelect(`${alias}.${part}`, nextAlias);
            alias = nextAlias;
          }
        } else {
          queryBuilder.leftJoinAndSelect(`entity.${relation}`, relation);
        }
      });
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (key === '$or') {
        (value as FindOptionsWhere<T>[]).forEach(orFilter => {
          queryBuilder.orWhere(orFilter);
        });
      } else {
        queryBuilder.andWhere({ [key]: value });
      }
    });

    if (sOptions?.where) {
      queryBuilder.andWhere(sOptions.where as FindOptionsWhere<T>);
    }

    SortHelper.applySorting(
      queryBuilder,
      this.repository,
      sOptions?.sort,
      'entity',
    );

    const page = sOptions?.page ?? 1;
    const limit = sOptions?.limit ?? 25;

    const [data, count] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data,
      count,
      page,
      limit,
    };
  }

  async findAllWithAdvancedFilters(sOptions: {
    filters: any[][];
    page?: number;
    limit?: number;
    relations?: string[];
  }): Promise<{ data: T[]; count: number; page: number; limit: number }> {
    const filters = FilterHelper.parseAdvancedFilters(
      sOptions.filters,
      this.repository,
    );
    const queryBuilder = this.repository.createQueryBuilder('entity');
    const usedRelations = new Set<string>();
    filters['$or'].forEach(andGroup => {
      andGroup.forEach(condition => {
        const key = Object.keys(condition)[0];
        if (key.includes('.')) {
          const relationName = key.split('.')[0];
          usedRelations.add(relationName);
        }
      });
    });

    sOptions?.relations?.forEach(relation => {
      usedRelations.add(relation);
    });

    usedRelations.forEach(relation => {
      if (relation.includes('.')) {
        const parts = relation.split('.');
        let alias = 'entity';
        let path = '';

        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          path = path ? `${path}.${part}` : part;
          const nextAlias = `${part}_${i}`;
          queryBuilder.leftJoinAndSelect(`${alias}.${part}`, nextAlias);
          alias = nextAlias;
        }
      } else {
        queryBuilder.leftJoinAndSelect(`entity.${relation}`, relation);
      }
    });

    const orGroups = filters['$or'];
    queryBuilder.where('entity.deletedAt IS NULL');

    orGroups.forEach((andGroup, groupIndex) => {
      const params: Record<string, any> = {};

      const whereClause = andGroup
        .map((condition, i) => {
          const key = Object.keys(condition)[0];
          const { _type = 'eq', _value } = condition[key] ?? {
            _type: 'eq',
            _value: condition[key],
          };

          let columnAlias = 'entity';
          let fieldName = key;

          if (key.includes('.')) {
            [columnAlias, fieldName] = key.split('.');
          }

          const paramKey = `value${groupIndex}_${i}`;
          let effectiveType = _type;

          const isStringBoolean =
            typeof _value === 'string' &&
            (_value === 'true' || _value === 'false');
          const normalizedValue = isStringBoolean ? _value === 'true' : _value;

          if (
            _type === 'like' &&
            (typeof normalizedValue === 'boolean' ||
              typeof normalizedValue === 'number')
          ) {
            effectiveType = 'eq';
          }

          // ✅ Store param correctly
          if (effectiveType === 'like') {
            if (Array.isArray(normalizedValue)) {
              normalizedValue.forEach((val, j) => {
                params[`${paramKey}_${j}`] = `%${val}%`;
              });
            } else {
              params[paramKey] = `%${normalizedValue}%`;
            }
          } else {
            params[paramKey] = normalizedValue;
          }

          switch (effectiveType) {
            case 'like':
              return Array.isArray(normalizedValue)
                ? `(${normalizedValue
                    .map(
                      (_, j) =>
                        `${columnAlias}.${fieldName} ILIKE :${paramKey}_${j}`,
                    )
                    .join(' OR ')})`
                : `${columnAlias}.${fieldName} ILIKE :${paramKey}`;
            case 'json-eq':
              if (
                typeof normalizedValue === 'object' &&
                normalizedValue.key &&
                normalizedValue.value
              ) {
                const jsonKey = normalizedValue.key;
                const jsonValue = normalizedValue.value.toLowerCase();
                const keyField = condition[key].keyField;
                params[`${paramKey}_key`] = jsonKey;
                params[`${paramKey}_value`] = jsonValue;

                return `EXISTS (
      SELECT 1 FROM jsonb_array_elements(${columnAlias}.${fieldName}) AS elem
      WHERE LOWER(elem->>'${keyField}') = LOWER(:${paramKey}_key) 
      AND LOWER(elem->>'value') = LOWER(:${paramKey}_value)
    )`;
              }
              throw new Error(
                `Invalid json-eq value. Expected object with { key, value }`,
              );

            case 'json-ne':
              if (
                typeof normalizedValue === 'object' &&
                normalizedValue.key &&
                normalizedValue.value
              ) {
                const jsonKey = normalizedValue.key;
                const jsonValue = normalizedValue.value.toLowerCase();
                const keyField = condition[key].keyField || 'name';
                params[`${paramKey}_key`] = jsonKey;
                params[`${paramKey}_value`] = jsonValue;
                return `NOT EXISTS (
      SELECT 1 FROM jsonb_array_elements(${columnAlias}.${fieldName}) AS elem
      WHERE elem->>'${keyField}' = :${paramKey}_key AND LOWER(elem->>'value') = :${paramKey}_value
    )`;
              }
              throw new Error(
                `Invalid json-ne value. Expected object with { key, value }`,
              );

            case 'json-like':
              if (
                typeof normalizedValue === 'object' &&
                normalizedValue.key &&
                normalizedValue.value
              ) {
                const jsonKey = normalizedValue.key;
                const jsonValue = `%${normalizedValue.value.toLowerCase()}%`;
                const keyField = condition[key].keyField || 'name';
                params[`${paramKey}_key`] = jsonKey;
                params[`${paramKey}_value`] = jsonValue;
                return `EXISTS (
      SELECT 1 FROM jsonb_array_elements(${columnAlias}.${fieldName}) AS elem
      WHERE elem->>'${keyField}' = :${paramKey}_key AND LOWER(elem->>'value') LIKE :${paramKey}_value
    )`;
              }
              throw new Error(
                `Invalid json-like value. Expected object with { key, value }`,
              );

            case 'ne':
              if (Array.isArray(normalizedValue)) {
                // Check if the field is an array type in the database
                const isArrayField = this.repository.metadata.columns.find(
                  col => col.propertyName === fieldName,
                )?.isArray;

                if (isArrayField) {
                  // For array fields, use NOT && operator for PostgreSQL array types
                  params[paramKey] = normalizedValue;
                  const isNumericArray = normalizedValue.every(
                    val => typeof val === 'number',
                  );
                  const arrayType = isNumericArray ? 'integer[]' : 'text[]';
                  return `NOT (${columnAlias}.${fieldName} && :${paramKey}::${arrayType})`;
                } else {
                  // For non-array fields, use NOT IN operator
                  params[paramKey] = normalizedValue;
                  return `${columnAlias}.${fieldName} NOT IN (:...${paramKey})`;
                }
              }

              if (fieldName === 'statusUpdatedAt') {
                // Convert string to number if needed
                const days =
                  typeof normalizedValue === 'string'
                    ? parseInt(normalizedValue, 10)
                    : normalizedValue;
                if (isNaN(days)) {
                  throw new Error(
                    'statusUpdatedAt value must be a valid number',
                  );
                }
                params[paramKey] = days;
                const query = `DATE_TRUNC('day', CURRENT_DATE) - DATE_TRUNC('day', ${columnAlias}.${fieldName}) != :${paramKey} * INTERVAL '1 day'`;
                return query;
              }
              return `${columnAlias}.${fieldName} != :${paramKey}`;
            case 'eq':
              if (Array.isArray(normalizedValue)) {
                const isArrayField = this.repository.metadata.columns.find(
                  col => col.propertyName === fieldName,
                )?.isArray;

                if (isArrayField) {
                  params[paramKey] = normalizedValue;
                  const isNumericArray = normalizedValue.every(
                    val => typeof val === 'number',
                  );
                  const arrayType = isNumericArray ? 'integer[]' : 'text[]';
                  return `${columnAlias}.${fieldName} && :${paramKey}::${arrayType}`;
                } else {
                  // For non-array fields, use IN operator
                  params[paramKey] = normalizedValue;
                  return `${columnAlias}.${fieldName} IN (:...${paramKey})`;
                }
              }
              if (fieldName === 'statusUpdatedAt') {
                // Convert string to number if needed
                const days =
                  typeof normalizedValue === 'string'
                    ? parseInt(normalizedValue, 10)
                    : normalizedValue;
                if (isNaN(days)) {
                  throw new Error(
                    'statusUpdatedAt value must be a valid number',
                  );
                }
                params[paramKey] = days;
                const query = `DATE_TRUNC('day', CURRENT_DATE) - DATE_TRUNC('day', ${columnAlias}.${fieldName}) = :${paramKey} * INTERVAL '1 day'`;
                return query;
              }
              return `${columnAlias}.${fieldName} = :${paramKey}`;
            case 'between':
              const startDate = normalizedValue.start;
              const endDate = normalizedValue.end;
              params[`${paramKey}_start`] = startDate;
              params[`${paramKey}_end`] = `${endDate} 23:59:59`;
              return `${columnAlias}.${fieldName} >= :${paramKey}_start AND ${columnAlias}.${fieldName} <= :${paramKey}_end`;

            default:
              throw new Error(`Unsupported filter type: ${effectiveType}`);
          }
        })
        .join(' AND ');

      if (groupIndex === 0) {
        queryBuilder.andWhere(`(${whereClause})`, params);
      } else {
        queryBuilder.orWhere(`(${whereClause})`, params);
      }
    });

    const page = sOptions?.page ?? 1;
    const limit = sOptions?.limit ?? 25;

    const [data, count] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data,
      count,
      page,
      limit,
    };
  }

  async findByIdOrThrow(
    id: string,
    relations?: string[],
    selectParam?: string,
  ): Promise<T> {
    const select = selectParam ? DBHelper.parseSelect(selectParam) : undefined;
    return (await DBHelper.findByIdOrThrow(
      this.repository as Repository<BaseEntity>,
      id,
      relations,
      select,
    )) as T;
  }

  async findOne(options: IOptions<T>): Promise<T | null> {
    return await DBHelper.findOne(this.repository, options);
  }

  async updateById(id: string, updateDto: DeepPartial<T>): Promise<T> {
    const entity = await this.findByIdOrThrow(id);
    Object.assign(entity, updateDto);
    return await this.repository.save(entity);
  }

  async deleteById(id: string, hardDelete = false): Promise<void> {
    const entity = await this.findByIdOrThrow(id);
    if (hardDelete) {
      await this.repository.delete(entity.id);
    } else {
      await this.repository.softDelete(entity.id);
    }
  }

  async restoreById(id: string): Promise<void> {
    await this.repository.restore(id);
  }

  // async findWithRelations(relations: string[]): Promise<T[]> {
  //   return await this.repository.find({ relations });
  // }

  // async findOneWithRelations(id: string, relations: string[]): Promise<T> {
  //   const entity = await this.repository.findOne({
  //     where: { id } as FindOptionsWhere<T>,
  //     relations,
  //   });
  //   if (!entity) {
  //     throw new NotFoundException(`Entity with ID ${id} not found`);
  //   }
  //   return entity;
  // }
}
