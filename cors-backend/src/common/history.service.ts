import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, QueryRunner, DeepPartial } from 'typeorm';
import { DatabaseHistory } from './history.entity';
import { ClsService } from 'nestjs-cls';

@Injectable()
export class HistoryService {
  private readonly logger = new Logger(HistoryService.name);

  constructor(
    @InjectRepository(DatabaseHistory)
    private readonly historyRepository: Repository<DatabaseHistory>,
    private readonly clsService: ClsService,
  ) {}

  async recordHistory<T>(
    entityType: string,
    entityId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'RESTORE',
    oldValue?: T,
    newValue?: T,
    changes?: { field: string; oldValue: any; newValue: any }[],
    metadata?: { userId: string | null },
    queryRunner?: QueryRunner,
  ): Promise<void> {
    // Try to get userId from metadata first, then fallback to CLS context
    let userId = metadata?.userId;

    if (!userId) {
      try {
        userId = this.clsService.get('userId');
      } catch (error) {
        this.logger.warn('Error getting userId from CLS:', error);
      }
    }

    if (!userId) {
      this.logger.debug(
        `No userId found for ${action} operation on ${entityType}:${entityId}`,
      );
    }

    const historyData: DeepPartial<DatabaseHistory> = {
      entityType,
      entityId,
      action,
      oldValue,
      newValue,
      changes,
      userId: userId || undefined,
    };

    const history = this.historyRepository.create(historyData);

    if (queryRunner) {
      await queryRunner.manager.save(DatabaseHistory, history);
    } else {
      await this.historyRepository.save(history);
    }
  }

  async getHistoryForEntity(
    entityType: string,
    entityId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: DatabaseHistory[];
    total: number;
    page: number;
    pages: number;
  }> {
    const [data, total] = await this.historyRepository.findAndCount({
      where: { entityType, entityId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Map of relation fields to their name properties
    const relationNameFields = new Map([
      ['artworkType', 'name'],
      ['primaryVendor', 'name'],
      ['products', 'name'],
      ['parentSku', 'sku'],
      ['childSku', 'sku'],
    ]);

    // Filter out unwanted changes from each history record
    const filteredData = data.map(record => {
      if (record.changes) {
        const ignoredFields = new Set([
          'createdAt',
          'updatedAt',
          'deletedAt',
          '__entity',
          '__hasRelations',
          '__metadata',
        ]);

        record.changes = record.changes.filter(change => {
          // Skip ignored fields
          if (ignoredFields.has(change.field)) {
            return false;
          }

          // Skip null-to-null changes
          if (!change.oldValue && !change.newValue) {
            return false;
          }

          // Transform relation changes to show names instead of IDs
          const nameField = relationNameFields.get(change.field);
          if (nameField) {
            if (change.oldValue && typeof change.oldValue === 'object') {
              change.oldValue = change.oldValue[nameField] || null;
            }
            if (change.newValue && typeof change.newValue === 'object') {
              change.newValue = change.newValue[nameField] || null;
            }
          }

          return true;
        });
      }
      return record;
    });

    return {
      data: filteredData,
      total,
      page,
      pages: Math.ceil(total / limit),
    };
  }

  async createWorkNotes(
    entityType: string,
    entityId: string,
    notes: string,
    userId: string,
  ): Promise<void> {
    const historyData: DeepPartial<DatabaseHistory> = {
      entityType,
      entityId,
      action: 'WORK_NOTES',
      notes,
      userId,
    };

    const history = this.historyRepository.create(historyData);
    await this.historyRepository.save(history);
  }
}
