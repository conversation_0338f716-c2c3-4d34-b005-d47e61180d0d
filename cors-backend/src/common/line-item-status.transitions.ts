import { LineItemStatus } from 'src/orders/enums/line-item-status.enum';

export const STATUS_TRANSITIONS: Partial<
  Record<LineItemStatus, LineItemStatus[]>
> = {
  [LineItemStatus.LINE_ITEM_RECEIVED]: [
    LineItemStatus.CUTOUT_PRO_REQUESTED,
    LineItemStatus.CROP_NEEDED,
    LineItemStatus.ARTWORK_NEEDED,
  ],

  [LineItemStatus.CUTOUT_PRO_REQUESTED]: [
    LineItemStatus.CROP_REVIEW,
    LineItemStatus.TEMPLATE_PLACEMENT,
  ],

  [LineItemStatus.CROP_REVIEW]: [
    LineItemStatus.CROP_NEEDED,
    LineItemStatus.AWAITING_CUSTOMER_RESPONSE,
    LineItemStatus.TEMPLATE_PLACEMENT,
    LineItemStatus.READY_FOR_VENDOR,
  ],

  [LineItemStatus.TEMPLATE_PLACEMENT]: [
    LineItemStatus.CROP_NEEDED,
    LineItemStatus.AWAITING_CUSTOMER_RESPONSE,
    LineItemStatus.READY_FOR_VENDOR,
  ],

  [LineItemStatus.CROP_NEEDED]: [
    LineItemStatus.AWAITING_CUSTOMER_RESPONSE,
    LineItemStatus.READY_FOR_VENDOR,
    LineItemStatus.TEMPLATE_PLACEMENT,
  ],

  [LineItemStatus.ARTWORK_NEEDED]: [
    LineItemStatus.AWAITING_CUSTOMER_RESPONSE,
    LineItemStatus.READY_FOR_VENDOR,
  ],
};
