import { Injectable } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { CutoutProService } from './cutout-pro.service';
import { EmailUtil } from 'src/utils/email.util';
import {
  CroppingMethod,
  WorkflowCategory,
  CropType,
} from 'src/product-sku/enums/product-sku.enums';
import { checkStatusTransition } from 'src/utils/workflow.utils';
import { DBHelper } from 'src/helpers/db.helpers';
interface WorkflowRule {
  name: string;
  condition: (sku: ProductSku) => boolean;
  action: (
    lineItem: LineItem,
    sku: ProductSku,
    services: WorkflowServices,
  ) => Promise<void>;
}

interface WorkflowServices {
  lineItemRepo: Repository<LineItem>;
  queueRepo: Repository<Queue>;
  attachmentRepo: Repository<Attachment>;
  cutoutProApiService: CutoutProService;
}

@Injectable()
export class WorkflowService {
  constructor(
    @InjectRepository(LineItem)
    private lineItemRepo: Repository<LineItem>,
    @InjectRepository(Queue)
    private queueRepo: Repository<Queue>,
    @InjectRepository(Attachment)
    private attachmentRepo: Repository<Attachment>,
    private cutoutProApiService: CutoutProService,
    private emailUtil: EmailUtil,
  ) {}

  private workflows: WorkflowRule[] = [
    // Workflow 1: Crop Image Only or Crop Image and Template Placement
    {
      name: 'Crop Image Only or Crop Image and Template Placement',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_ONLY ||
        sku.workflowCategory === WorkflowCategory.CROP_IMAGE_AND_TEMPLATE,
      action: async (
        lineItem,
        sku,
        { lineItemRepo, queueRepo, attachmentRepo, cutoutProApiService },
      ) => {
        if (sku.croppingMethod === CroppingMethod.CUTOUT_PRO) {
          await checkStatusTransition(lineItem.status, 'Cutout Pro Requested');
          lineItem.status = 'Cutout Pro Requested';
          await lineItemRepo.save(lineItem);
          const attachments = lineItem.attachments;
          const cropType = sku.cropType === CropType.FACE_CUTOUT ? '3' : '6';
          await cutoutProApiService.process(lineItem, attachments, cropType);
        } else if (sku.croppingMethod === CroppingMethod.MANUAL) {
          let cropNeededQueue = await DBHelper.findOne(queueRepo, {
            where: { name: 'Crop Needed' },
          });
          if (!cropNeededQueue) {
            cropNeededQueue = queueRepo.create({
              name: 'Crop Needed',
            });
            cropNeededQueue = await queueRepo.save(cropNeededQueue);
          }
          const attachments = lineItem.attachments;
          for (const attachment of attachments) {
            attachment.status = 'Manual Crop Needed';
            attachment.queue = cropNeededQueue;
          }
          await checkStatusTransition(lineItem.status, 'Crop Needed');
          await attachmentRepo.save(attachments);
          lineItem.status = 'Crop Needed';
          await lineItemRepo.save(lineItem);
        }
      },
    },
    // Workflow 2: Art Only or Art and Template Placement
    {
      name: 'Art Only or Art and Template Placement',
      condition: sku =>
        (sku.workflowCategory === WorkflowCategory.ART_ONLY &&
          sku.artworkRequired &&
          !sku.requireCustomerArtworkApproval) ||
        (sku.workflowCategory === WorkflowCategory.ART_AND_TEMPLATE &&
          sku.artworkRequired &&
          sku.requireTemplate),
      action: async (lineItem, sku, { lineItemRepo }) => {
        await checkStatusTransition(lineItem.status, 'Artwork Needed');
        lineItem.status = 'Artwork Needed';
        const artworkQueueName = `Ready for Artwork - ${sku.artworkType?.name}`;
        let artworkQueue = await DBHelper.findOne(this.queueRepo, {
          where: { name: artworkQueueName },
        });
        if (!artworkQueue) {
          artworkQueue = this.queueRepo.create({ name: artworkQueueName });
          artworkQueue = await this.queueRepo.save(artworkQueue);
          await this.emailUtil.sendQueueCreationEmail(artworkQueueName);
        }
        lineItem.queue = artworkQueue;
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 3: Customer Image Only
    {
      name: 'Customer Image Only',
      condition: sku =>
        sku.workflowCategory === WorkflowCategory.CUSTOMER_IMAGE_ONLY,
      action: async (lineItem, sku, { lineItemRepo }) => {
        await checkStatusTransition(lineItem.status, 'Ready for Vendor');
        lineItem.status = 'Ready for Vendor';
        await lineItemRepo.save(lineItem);
      },
    },
    // Workflow 4: Art + Template Placement
    // {
    //   name: 'Art + Template Placement',
    //   condition: sku =>
    //     sku.workflowCategory === WorkflowCategory.ART_AND_TEMPLATE,
    //   action: async (lineItem, sku, { lineItemRepo }) => {
    //     lineItem.status = 'Artwork Needed';
    //     const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
    //     await lineItemRepo.save(lineItem);
    //     // await queueRepo.save({
    //     //   queueName,
    //     //   lineItem,
    //     // });
    //   },
    // },
    // Workflow 5: Generic Artwork Required
    {
      name: 'Generic Artwork Required',
      condition: sku => sku.artworkRequired,
      action: async (lineItem, sku, { lineItemRepo }) => {
        if (lineItem.status === 'Line Item Received') {
          lineItem.status = 'Artwork Needed';
          const queueName = `Ready for Artwork - ${sku.artworkType?.name}`;
          await lineItemRepo.save(lineItem);
          // await queueRepo.save({
          //   queueName,
          //   lineItem,
          // });
        }
      },
    },
  ];

  async processLineItem(lineItem: LineItem, productSku: ProductSku) {
    const services: WorkflowServices = {
      lineItemRepo: this.lineItemRepo,
      queueRepo: this.queueRepo,
      attachmentRepo: this.attachmentRepo,
      cutoutProApiService: this.cutoutProApiService,
    };

    // Set initial status
    if (!lineItem.status) {
      lineItem.status = 'Line Item Received';
      await this.lineItemRepo.save(lineItem);
    }

    // Remove existing queue assignments
    // await this.queueRepo.delete({ lineItem: { id: lineItem.id } });
    // if (lineItem.cutoutProImages) {
    //   await this.queueRepo.delete({
    //     cutoutProImage: { id: In(lineItem.cutoutProImages.map(i => i.id)) },
    //   });
    // }
    // if (lineItem.attachments) {
    //   await this.queueRepo.delete({
    //     attachment: { id: In(lineItem.attachments.map(a => a.id)) },
    //   });
    // }

    // Apply workflows
    for (const workflow of this.workflows) {
      if (workflow.condition(productSku)) {
        await workflow.action(lineItem, productSku, services);
      }
    }
  }
}
