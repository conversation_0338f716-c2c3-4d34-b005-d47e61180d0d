import { plainToInstance } from 'class-transformer';
import { IsBoolean, IsNumber, IsString, validateSync } from 'class-validator';

class EnvironmentVariables {
  @IsString()
  DATABASE_HOST: string;

  @IsNumber()
  PORT: number;

  @IsNumber()
  DATABASE_PORT: number;

  @IsString()
  DATABASE_USERNAME: string;

  @IsString()
  DATABASE_PASSWORD: string;

  @IsString()
  DATABASE_NAME: string;

  @IsString()
  JWT_SECRET: string;

  @IsString()
  JWT_ACCESS_EXPIRES_IN: string;

  @IsString()
  JWT_REFRESH_EXPIRES_IN: string;

  @IsString()
  SWAGGER_USERNAME: string;

  @IsString()
  SWAGGER_PASSWORD: string;

  @IsBoolean()
  DATABASE_SSL_ENABLED: boolean;

  @IsString()
  BULLMQ_USERNAME: string;

  @IsString()
  BULLMQ_PASSWORD: string;

  @IsString()
  REDIS_HOST: string;

  @IsNumber()
  REDIS_PORT: number;

  @IsString()
  SHOPIFY_ACCESS_TOKEN: string;

  @IsString()
  SHOPIFY_STORE: string;

  @IsString()
  SHOPIFY_API_VERSION: string;

  @IsString()
  SHOPIFY_WEBHOOK_SECRET: string;

  @IsString()
  SHOPIFY_STORE_URL: string;

  @IsString()
  SHOPIFY_API_SECRET: string;

  @IsString()
  SHOPIFY_STORE_NAME: string;

  @IsString()
  NGROK_URL: string;

  @IsString()
  CLOUDINARY_CLOUD_NAME: string;

  @IsString()
  CLOUDINARY_API_KEY: string;

  @IsString()
  CLOUDINARY_API_SECRET: string;

  @IsString()
  STORAGE_DRIVER: string;

  @IsString()
  SMTP_HOST: string;

  @IsNumber()
  SMTP_PORT: number;

  @IsString()
  SMTP_USERNAME: string;

  @IsString()
  SMTP_PASSWORD: string;

  @IsString()
  SMTP_FROM_EMAIL: string;

  @IsBoolean()
  SKU_TRANSFORMER: boolean;

  @IsString()
  RESET_ACCESS_EXPIRES_IN: string;

  @IsString()
  NODE_ENV: string;

  @IsString()
  BASE_URL: string;

  @IsString()
  REDIS_PASSWORD: string;

  @IsBoolean()
  REDIS_SSL_ENABLED: boolean;

  @IsString()
  CUTOUT_PRO_API_URL: string;

  @IsString()
  CUTOUT_PRO_API_KEY: string;

  @IsString()
  JWT_FIXED_SECRET: string;
}

export function validate(config: Record<string, unknown>) {
  const validatedConfig = plainToInstance(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });
  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    throw new Error(errors.toString());
  }
  return validatedConfig;
}

let cleanVariablesCache: Record<string, any> = {};
export function accessEnv(envKey: keyof EnvironmentVariables) {
  if (!(envKey in cleanVariablesCache)) {
    cleanVariablesCache[envKey] =
      process.env[envKey as keyof typeof process.env];
  }
  return cleanVariablesCache[envKey];
}
