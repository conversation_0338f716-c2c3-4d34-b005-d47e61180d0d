import {
  IsArray,
  IsInt,
  IsOptional,
  IsString,
  ValidateNested,
  IsPositive,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

function IsArrayOfArraysContainingObjects(
  objectType: any,
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsArrayOfArraysContainingObjects',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!Array.isArray(value)) {
            return false;
          }

          return value.every(item => {
            return (
              Array.isArray(item) &&
              item.every(
                subItem =>
                  typeof subItem === 'object' &&
                  subItem.hasOwnProperty('attribute') &&
                  subItem.hasOwnProperty('operator') &&
                  subItem.hasOwnProperty('value'),
              )
            );
          });
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} should be an array of arrays, with each inner array containing valid FilterItem objects`;
        },
      },
    });
  };
}

class JsonFilterValue {
  @IsString()
  key: string;

  @IsString()
  value: string;
}

function IsValidFilterValue(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsValidFilterValue',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          // Check if it's a primitive type (string, boolean, number)
          if (typeof value === 'string' || typeof value === 'boolean' || typeof value === 'number' || Array.isArray(value)) {
            return true;
          }

          // Check if it's a JsonFilterValue object
          if (typeof value === 'object' && value !== null) {
            return value.hasOwnProperty('key') && 
                   value.hasOwnProperty('value') && 
                   typeof value.key === 'string' && 
                   typeof value.value === 'string';
          }

          return false;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a string, boolean, number, array, or a valid JSON filter object with key and value properties`;
        },
      },
    });
  };
}

class FilterItem {
  @IsString()
  attribute: string;

  @IsString()
  operator: string;

  @IsValidFilterValue()
  value: string | boolean | number | string[] | JsonFilterValue;
}

export class AdvancedFilterDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterItem)
  @IsArrayOfArraysContainingObjects(FilterItem, {
    message:
      'Filters should be an array of arrays, with each inner array containing valid FilterItem objects',
  })
  filters: FilterItem[][];

  @IsOptional()
  @IsInt()
  @IsPositive()
  page: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  limit: number;
}
