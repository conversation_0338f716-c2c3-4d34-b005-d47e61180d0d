import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpStatus,
  Req,
  Headers,
  HttpException,
  Put,
  Query,
  DefaultValuePipe,
  ParseIntPipe,
} from '@nestjs/common';
import { ProductSkuService } from './product-sku.service';
import { UpdateProductSkuDto } from './dto/update-product-sku.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiExcludeEndpoint,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import { ShopifyWebhookGuard } from 'src/auth/guards/shopify-webhook-guard';
import { HistoryService } from 'src/common/history.service';
import { UsersService } from 'src/users/users.service';
import { AdvancedFilterDto } from './dto/product-sku-advance-filter-dto';
import { ProductSku } from './entities/product-sku.entity';
import { ProductCategory } from './enums/product.enums';
import {
  AddonLevel,
  CroppingMethod,
  ExceptionHandlingRule,
  FileUploadFormat,
  ImageInheritRule,
  RoutingMethod,
  VendorAssignmentRule,
  WorkflowCategory,
} from './enums/product-sku.enums';
import { In, Repository } from 'typeorm';
import { SkuAddon } from './entities/sku-addon.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SkipAuth } from 'src/auth/decorators/public.decorator';
import { Product } from './entities/product.entity';
import { shopifyWebhookProductTransformClient } from './shopify-etl/shopify-webhook-product.transform';
import { FieldPermissionsGuard } from '../permissions/field-permissions.guard';
import { FieldPermissions } from '../permissions/field-permissions.decorator';
import { PermissionResources } from '../constants/permission-resources';
import { getFieldPermissions } from 'src/permissions/field-permissions.config';
import { PermissionsGuard } from 'src/permissions/permissions.guards';
import { Permissions } from 'src/permissions/permissions.decorator';

export interface IField {
  key:
    | keyof Pick<
        ProductSku,
        | 'sku'
        | 'workflowCategory'
        | 'hasRush'
        | 'rushDays'
        | 'requireImageUpload'
        | 'requireCropping'
        | 'routingMethod'
        | 'croppingMethod'
        | 'croppingReviewRequired'
        | 'artworkRequired'
        | 'requireCustomerArtworkApproval'
        | 'imageNamingConvention'
        | 'requireTemplate'
        | 'fileUploadFormat'
        | 'canInheritImage'
        | 'imageInheritRule'
        | 'imageInheritancePriority'
        | 'canManualOverride'
        | 'exceptionHandlingRule'
        | 'customerFollowupEnabled'
        | 'requirePreprocessing'
        | 'shipStationStore'
        | 'isActive'
        | 'processingPriority'
        | 'vendorAssignmentRule'
        | 'shopifyNativeVariant'
      >
    | 'parentSkuIds'
    | 'childSkuIds'
    | 'products'
    | 'products.category'
    | 'products.description'
    | 'parentSku'
    | 'childSku'
    | 'name'
    | 'artworkTypeId';
  label: string;
  type: string | Number;
  secondary_key?:
    | 'name'
    | 'products'
    | 'product.category'
    | 'product.description'
    | 'parentSku'
    | 'childSku';
  fetch_db?: boolean;
  endpoint?: string;
  filter_key?: string;
  backend_key?: string;
  options?: { value: string | boolean | number; key: string }[];
  usage: 'bulk_update' | 'filter' | 'both' | undefined;
  json_fields: boolean;
  sortable: boolean;
  show_in_list: boolean;
  category?: string;
}

interface TransformedArtworkType {
  id: string;
  name: string;
}

@UseGuards(JwtGuard, PermissionsGuard, FieldPermissionsGuard)
@ApiBearerAuth()
@ApiTags('Product SKUs')
@Controller('product-sku')
export class ProductSkuController {
  constructor(
    private readonly productSkuService: ProductSkuService,
    private readonly historyService: HistoryService,
    private readonly userService: UsersService,
    @InjectRepository(SkuAddon)
    private readonly skuAddonRepository: Repository<SkuAddon>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductSku)
    private readonly productSkuRepository: Repository<ProductSku>,
  ) {}

  @Patch('bulk-update')
  @ApiOperation({ summary: 'Bulk update product SKUs' })
  @ApiResponse({ status: 200, description: 'Returns the updated product SKUs' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        ids: {
          type: 'array',
          items: {
            type: 'string',
          },
          example: [
            '04c35346-2238-459d-945b-9ba1e2b65978',
            '0adb162c-d919-4acb-982a-d08ba84b1ee9',
          ],
        },
        attributes: {
          type: 'object',
          example: {
            hasRush: false,
            rushDays: 0,
          },
        },
      },
    },
  })
  async bulkUpdate(
    @Body('ids') ids: string[],
    @Body('attributes') attributes: Partial<UpdateProductSkuDto>,
  ) {
    const updateDto = {
      ids,
      attributes,
    };
    console.log('updateDto', updateDto);
    return this.productSkuService.bulkUpdate(updateDto);
  }

  private formatLabel(key: string): string {
    return key
      .split(/(?=[A-Z])/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  @Get('fields')
  @ApiOperation({ summary: 'Get all available fields for product SKU updates' })
  @ApiResponse({ status: 200, description: 'Returns array of field names' })
  async getAvailableFields(): Promise<{ fields: IField[] }> {
    const fields: IField[] = [
      {
        key: 'sku',
        label: 'SKU',
        type: 'multi_select',
        fetch_db: true,
        usage: 'filter',
        show_in_list: true,
        sortable: false,
        json_fields: false,
      },

      {
        key: 'products',
        secondary_key: 'name',
        label: this.formatLabel('productName'),
        type: 'multi_select',
        endpoint: 'product-sku/products',
        fetch_db: true,
        usage: 'filter',
        show_in_list: true,
        sortable: false,
        json_fields: false,
      },
      {
        key: 'products.category',
        label: this.formatLabel('productCategory'),
        type: 'multi_select',
        options: Object.values(ProductCategory).map(value => ({
          value,
          key: value,
        })),
        usage: 'both',
        show_in_list: true,
        sortable: false,
        json_fields: false,
        category: 'General Product Identifications',
      },
      {
        key: 'rushDays',
        label: this.formatLabel('rushDays'),
        type: 'number',
        usage: 'both',
        show_in_list: true,
        sortable: false,
        json_fields: false,
        category: 'General Product Identifications',
      },
      {
        key: 'hasRush',
        label: this.formatLabel('Rush Available'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'both',
        show_in_list: true,
        sortable: false,
        json_fields: false,
        category: 'General Product Identifications',
      },
      {
        key: 'products.description',
        label: this.formatLabel('Product description'),
        type: 'text',
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'General Product Identifications',
      },
      {
        key: 'workflowCategory',
        label: this.formatLabel('workflowCategory'),
        type: 'select',
        options: Object.values(WorkflowCategory).map(value => ({
          value,
          key: value,
        })),
        usage: 'both',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'General Product Identifications',
      },
      {
        key: 'requireCropping',
        label: this.formatLabel('requireCropping'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'croppingMethod',
        label: this.formatLabel('croppingMethod'),
        type: 'select',
        options: Object.values(CroppingMethod).map(value => ({
          value,
          key: value,
        })),
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'croppingReviewRequired',
        label: this.formatLabel('croppingReviewRequired'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'artworkRequired',
        label: this.formatLabel('artworkRequired'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'requireCustomerArtworkApproval',
        label: this.formatLabel('requireCustomerArtworkApproval'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'requireTemplate',
        label: this.formatLabel('requireTemplate'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'fileUploadFormat',
        label: this.formatLabel('templateUploadFormat'),
        type: 'select',
        options: Object.values(FileUploadFormat).map(value => ({
          value,
          key: value,
        })),
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'canInheritImage',
        label: this.formatLabel('canInheritImage'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'SKU Mapping & Image Inheritance Attributes',
      },
      {
        key: 'imageInheritRule',
        label: this.formatLabel('imageInheritRule'),
        type: 'select',
        options: Object.values(ImageInheritRule).map(value => ({
          value,
          key: value,
        })),
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'SKU Mapping & Image Inheritance Attributes',
      },
      {
        key: 'canManualOverride',
        label: this.formatLabel('canManualOverride'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'SKU Mapping & Image Inheritance Attributes',
      },
      {
        key: 'customerFollowupEnabled',
        label: this.formatLabel('customerFollowupEnabled'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'SKU Mapping & Image Inheritance Attributes',
      },
      {
        key: 'requirePreprocessing',
        label: this.formatLabel('requirePreprocessing'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Order Processing & Routing',
      },
      {
        key: 'routingMethod',
        label: this.formatLabel('productRoutingMethod'),
        type: 'select',
        options: Object.values(RoutingMethod).map(value => ({
          value,
          key: value,
        })),
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Order Processing & Routing',
      },
      {
        key: 'parentSku',
        filter_key: 'parentSku',
        backend_key: 'parentSku',
        label: 'Parent SKUs',
        type: 'multi_select',
        fetch_db: false,
        usage: undefined,
        sortable: false,
        json_fields: false,
        show_in_list: true,
      },
      {
        key: 'childSku',
        filter_key: 'childSku',
        backend_key: 'childSku',
        label: 'Child SKUs',
        type: 'multi_select',
        fetch_db: false,
        usage: undefined,
        sortable: false,
        json_fields: false,
        show_in_list: true,
      },
      {
        key: 'parentSkuIds',
        filter_key: 'sku',
        backend_key: 'id',
        label: 'Parent SKUs',
        type: 'multi_select',
        fetch_db: true,
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'General Product Identifications',
      },
      {
        key: 'childSkuIds',
        filter_key: 'sku',
        backend_key: 'id',
        label: 'Child SKUs',
        type: 'multi_select',
        fetch_db: true,
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'General Product Identifications',
      },
      {
        key: 'isActive',
        label: this.formatLabel('status'),
        type: 'select',
        options: [
          { value: true, key: 'Active' },
          { value: false, key: 'Inactive' },
        ],
        usage: 'filter',
        show_in_list: true,
        sortable: false,
        json_fields: false,
      },
      {
        key: 'requireImageUpload',
        label: this.formatLabel('requireImageUpload'),
        type: 'select',
        options: [
          { value: true, key: 'Yes' },
          { value: false, key: 'No' },
        ],
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'General Product Identifications',
      },
      {
        key: 'exceptionHandlingRule',
        label: this.formatLabel('exceptionHandlingRule'),
        type: 'select',
        options: Object.values(ExceptionHandlingRule).map(value => ({
          value,
          key: value,
        })),
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'SKU Mapping & Image Inheritance Attributes',
      },
      {
        key: 'processingPriority',
        label: this.formatLabel('processingPriority'),
        type: 'select',
        usage: 'bulk_update',
        options: [
          { value: 1, key: '1 - Highest' },
          { value: 2, key: '2' },
          { value: 3, key: '3' },
          { value: 4, key: '4' },
          { value: 5, key: '5' },
          { value: 6, key: '6' },
          { value: 7, key: '7' },
          { value: 8, key: '8' },
          { value: 9, key: '9' },
          { value: 10, key: '10 - Lowest' },
        ],
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Order Processing & Routing',
      },
      {
        key: 'parentSku',
        secondary_key: 'parentSku',
        filter_key: 'sku',
        backend_key: 'id',
        label: 'Parent SKUs',
        type: 'multi_select',
        fetch_db: true,
        usage: 'filter',
        sortable: false,
        json_fields: false,
        show_in_list: false,
      },
      {
        key: 'childSku',
        secondary_key: 'childSku',
        filter_key: 'sku',
        backend_key: 'id',
        label: 'Child SKUs',
        type: 'multi_select',
        fetch_db: true,
        usage: 'filter',
        sortable: false,
        json_fields: false,
        show_in_list: false,
      },
      {
        key: 'vendorAssignmentRule',
        label: this.formatLabel('vendorAssignmentRule'),
        type: 'select',
        options: Object.values(VendorAssignmentRule).map(value => ({
          value,
          key: value,
        })),
        usage: 'bulk_update',
        sortable: false,
        json_fields: false,
        show_in_list: false,
        category: 'Manufacturing & Vendor Assignment',
      },
      {
        key: 'shopifyNativeVariant',
        label: this.formatLabel('shopifyNativeVariant'),
        type: 'select',
        usage: 'filter',
        sortable: false,
        json_fields: true,
        show_in_list: true,
      },
      {
        key: 'artworkTypeId',
        label: this.formatLabel('artworkType'),
        type: 'select',
        endpoint: 'artwork-types',
        fetch_db: true,
        usage: 'bulk_update',
        show_in_list: false,
        sortable: false,
        json_fields: false,
        category: 'Image Processing & Artwork Attributes',
      },
      {
        key: 'imageInheritancePriority',
        label: this.formatLabel('imageInheritancePriority'),
        type: 'number',
        usage: 'bulk_update',
        show_in_list: false,
        sortable: false,
        json_fields: false,
        category: 'SKU Mapping & Image Inheritance Attributes',
      },
    ];

    return { fields };
  }

  @Permissions(PermissionResources.PIMS, 'View PIMS Listing Page')
  @Get('products')
  @ApiOperation({ summary: 'Get all products' })
  @ApiResponse({ status: 200, description: 'Returns all products' })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'pajama',
  })
  async getProducts(@Query('q') q?: string) {
    return await this.productSkuService.getProducts(q);
  }

  @Get('shopify-etl')
  syncShopifyProductSku() {
    return this.productSkuService.syncShopifyProductInformation();
  }

  @Permissions(PermissionResources.PIMS, 'View PIMS Listing Page')
  @Get()
  @ApiOperation({ summary: 'Get all product SKUs' })
  @ApiResponse({ status: 200, description: 'Returns all product SKUs' })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'sku:like:Paw',
  })
  @ApiQuery({
    name: 'fq',
    required: false,
    type: String,
    example: 'isActive:eq:true',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  async findAll(
    @Query('q') q?: string,
    @Query('fq') fq?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const relations = [
      'artworkType',
      'primaryVendor',
      'products',
      'parentSku.parentSku',
      'childSku.childSku',
    ];

    const result = await this.productSkuService.findAll({
      q,
      fq,
      page,
      limit,
      relations,
    });

    const addonSku = await this.skuAddonRepository.find({
      where: {
        productSkuId: In(result.data.map(i => i.id)),
      },
    });
    const addonSkuIds = addonSku.map(i => i.relationSkuId);
    const addonSkuData = await this.productSkuService.findAll({
      where: {
        id: In(addonSkuIds),
      },
      relations: ['products'],
    });
    const addonData = addonSkuData.data.map(sku => ({
      id: sku.id,
      sku: sku.sku,
      isAddon: sku.isAddon,
      addonLevel: sku.addonLevel,
      variantId: sku.variantId,
      products: sku.products ? sku.products.map(product => product.name) : null,
    }));

    const newResult = result.data.map(i => ({
      ...i,
      products: i?.products?.map(i => i.name) ?? [],
      products_category: i?.products?.map(i => i.category) ?? [],
      parentSku: i?.parentSku
        ? i?.parentSku?.filter(i => i.parentSku?.sku).map(i => i.parentSku.sku)
        : [],
      childSku: i?.childSku
        ? i?.childSku?.filter(i => i.childSku?.sku).map(i => i.childSku.sku)
        : [],
      addonData,
    }));

    return {
      data: newResult,
      count: result.count,
      page: result.page,
      limit: result.limit,
    };
  }

  @Permissions(PermissionResources.PIMS, 'View PIMS Listing Page')
  @Post('advanced-filter')
  @ApiOperation({ summary: 'Filter product SKUs with advanced conditions' })
  @ApiBody({
    type: AdvancedFilterDto,
    schema: {
      example: {
        filters: [
          [
            { attribute: 'sku', operator: 'eq', value: 'SSP1-L' },
            {
              attribute: 'productName',
              operator: 'eq',
              value: 'Custom Pajamas',
            },
          ],
          [{ attribute: 'childSku', operator: 'eq', value: 'SPS1' }],
        ],
        page: 1,
        limit: 10,
      },
    },
  })
  async filterWithAdvancedConditions(@Body() body: AdvancedFilterDto) {
    const { filters, page, limit } = body;

    const processedFilters = filters.map(filterGroup => {
      return filterGroup.map(filter => {
        if (filter.attribute === 'shopifyNativeVariant') {
          return {
            attribute: filter.attribute,
            operator: filter.operator,
            effectiveValue: filter.value,
            keyField: 'name',
          };
        }
        return {
          attribute: filter.attribute,
          operator: filter.operator,
          effectiveValue: filter.value,
          keyField: filter.value,
        };
      });
    });

    const result = await this.productSkuService.findAllWithAdvancedFilters({
      filters: processedFilters,
      page,
      limit,
      relations: [
        'artworkType',
        'primaryVendor',
        'products',
        'parentSku.parentSku',
        'childSku.childSku',
      ],
    });

    const newResult = result.data.map(i => ({
      ...i,
      products: i?.products?.map(i => i.name) ?? [],
      parentSku: i?.parentSku
        ? i?.parentSku?.filter(i => i.parentSku?.sku).map(i => i.parentSku.sku)
        : [],
      childSku: i?.childSku
        ? i?.childSku?.filter(i => i.childSku?.sku).map(i => i.childSku.sku)
        : [],
    }));

    return {
      data: newResult,
      count: result.count,
      page: result.page,
      limit: result.limit,
    };
  }

  @Permissions(PermissionResources.PIMS, 'Edit Detail Page')
  @FieldPermissions(getFieldPermissions('productSku', 'updateProductSku'))
  @Patch(':id')
  @ApiOperation({ summary: 'Update product SKU by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated product SKU' })
  async patch(@Param('id') id: string, @Body() updateDto: UpdateProductSkuDto) {
    const result = await this.productSkuService.update(id, updateDto);
    const newResult = {
      ...result,
      products: result?.products?.map(i => i.name) ?? [],
      parentSku: result?.parentSku
        ? result?.parentSku
            ?.filter(i => i.parentSku?.sku)
            .map(i => i.parentSku.sku)
        : [],
      childSku: result?.childSku
        ? result?.childSku
            ?.filter(i => i.childSku?.sku)
            .map(i => i.childSku.sku)
        : [],
    };
    return newResult;
  }

  @Permissions(PermissionResources.PIMS, 'Edit Detail Page')
  @FieldPermissions(getFieldPermissions('productSku', 'updateProductSku'))
  @Put(':id')
  @ApiOperation({ summary: 'Update product SKU by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated product SKU' })
  async put(@Param('id') id: string, @Body() updateDto: UpdateProductSkuDto) {
    const result = await this.productSkuService.update(id, updateDto);
    const newResult = {
      ...result,
      products: result?.products?.map(i => i.name) ?? [],
      parentSku: result?.parentSku
        ? result?.parentSku
            ?.filter(i => i.parentSku?.sku)
            .map(i => i.parentSku.sku)
        : [],
      childSku: result?.childSku
        ? result?.childSku
            ?.filter(i => i.childSku?.sku)
            .map(i => i.childSku.sku)
        : [],
    };
    return newResult;
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete product SKU by ID' })
  @ApiResponse({ status: 200, description: 'Returns the deleted product SKU' })
  remove(@Param('id') id: string) {
    return this.productSkuService.deleteById(id);
  }

  @SkipAuth()
  @UseGuards(ShopifyWebhookGuard)
  @ApiExcludeEndpoint()
  @Post('webhook/update')
  async syncExistingProduct(@Req() req: Request) {
    try {
      if (!req.body) {
        throw new HttpException('No body', HttpStatus.BAD_REQUEST);
      }
      const payload = JSON.parse(req.body.toString());
      const products =
        await shopifyWebhookProductTransformClient.transform(payload);
      await this.productSkuService.syncExistingProduct(products);
    } catch (error) {
      console.error('Webhook Error:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('shopify-native-variants')
  @ApiOperation({
    summary: 'Get unique keys and values from shopifyNativeVariant field',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns unique keys and their values from shopifyNativeVariant field',
  })
  async getShopifyNativeVariantKeys() {
    return await this.productSkuService.getShopifyNativeVariantKeys();
  }

  @Permissions(PermissionResources.PIMS, 'View Detail Page')
  @Get(':id')
  @ApiOperation({ summary: 'Get product SKU by ID' })
  @ApiResponse({ status: 200, description: 'Returns the product SKU' })
  async findOne(@Param('id') id: string, @Query('select') select?: string) {
    const relations = [
      'artworkType',
      'primaryVendor',
      'supportedSkus.vendor',
      'products',
      'parentSku.parentSku',
      'childSku.childSku',
    ];
    const result = await this.productSkuService.findByIdOrThrow(
      id,
      relations,
      select,
    );

    if (result.artworkType) {
      const transformedArtworkType: TransformedArtworkType = {
        id: result.artworkType.id,
        name: result.artworkType.name,
      };
      result.artworkType = transformedArtworkType as any;
    }

    const addonSku = await this.skuAddonRepository.find({
      where: {
        productSkuId: result.id,
      },
    });
    const addonSkuIds = addonSku.map(i => i.relationSkuId);
    const addonSkuData = await this.productSkuService.findAll({
      where: {
        id: In(addonSkuIds),
      },
      relations: ['products'],
    });
    const addonData = addonSkuData.data.map(sku => ({
      id: sku.id,
      sku: sku.sku,
      isAddon: sku.isAddon,
      addonLevel: sku.addonLevel,
      variantId: sku.variantId,
      products: sku.products ? sku.products.map(product => product.name) : null,
    }));
    const finalResult = {
      ...result,
      addonData,
    };

    return finalResult;
  }

  @Permissions(PermissionResources.PIMS, 'View Detail Page')
  @Post(':id/notes')
  @ApiOperation({ summary: 'Add work notes to a product SKU' })
  @ApiResponse({ status: 201, description: 'Work notes added successfully' })
  async addWorkNotes(
    @Param('id') id: string,
    @Body('notes') notes: string,
    @Req() req: any,
  ) {
    await this.productSkuService.findByIdOrThrow(id);
    const userId = req.user?.user;
    await this.historyService.createWorkNotes('ProductSku', id, notes, userId);

    return { message: 'Work notes added successfully' };
  }

  @Permissions(PermissionResources.PIMS, 'View Detail Page')
  @Get(':id/history')
  @ApiOperation({ summary: 'Get paginated history records for a product SKU' })
  @ApiResponse({
    status: 200,
    description:
      'Returns paginated history records including work notes for the product SKU',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of records per page (default: 10)',
  })
  async getHistory(
    @Param('id') id: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    await this.productSkuService.findByIdOrThrow(id);

    const history = await this.historyService.getHistoryForEntity(
      'ProductSku',
      id,
      page,
      limit,
    );
    return {
      data: await Promise.all(
        history.data.map(async record => ({
          changes: record.changes,
          user: record.userId
            ? await this.userService.findOne({ where: { id: record.userId } })
            : null,
          createdAt: record.createdAt,
        })),
      ),
      total: history.total,
      page: history.page,
      pages: history.pages,
    };
  }
}
