import { NativeProduct } from './types';
import { htmlToText } from 'html-to-text';
import { accessEnv } from '../../env.validation';

export interface TransformedProduct {
  id: string;
  sku: string;
  title: string;
  handle: string;
  status: string;
  productType: string;
  description: string;
  metafields: any;
  variantNativeOptions: Record<string, any>;
  variantCustomOptions: Record<string, any>;
  variantId?: string;
}

enum ProductType {
  PDP_CUSTOMIZER = 'pdp_customizer',
  PRODUCT_CUSTOMIZER = 'product_customizer',
  CUSTOM_OPTIONS = 'custom_options',
  PRODUCT_KEY_TYPE = 'product_key_type',
}

export abstract class ShopifyProductType {
  abstract transformProduct(product: NativeProduct): TransformedProduct[];
}

class ShopifyTransformUtils {
  constructor() {}
  generateCombinationsFromAllVariants(
    allOptions: (string[] | { heading: string; options: string[] })[],
    nativeVariantsWithOptions: {
      [key: string]: {
        id: string;
        selectedOptions: { name: string; value: string }[];
      };
    },
  ) {
    const combinations: {
      sku: string;
      nativeOptions: { name: string; value: string }[];
      nativeVariantId: string;
      customOptions: { name: string; value: string }[];
    }[] = [];

    const generateCombinations = (
      baseVariant: string,
      remainingArrays: (string[] | { heading: string; options: string[] })[],
      currentOptions: { name: string; value: string }[] = [],
    ) => {
      if (remainingArrays.length === 0) {
        if (baseVariant && nativeVariantsWithOptions[baseVariant]) {
          // Create combination with just native variant if no custom options
          combinations.push({
            sku: baseVariant,
            nativeOptions:
              nativeVariantsWithOptions[baseVariant].selectedOptions,
            nativeVariantId: nativeVariantsWithOptions[baseVariant].id,
            customOptions: currentOptions,
          });
        }
        return;
      }

      const currentArray = remainingArrays[0];
      const rest = remainingArrays.slice(1);

      if (Array.isArray(currentArray)) {
        // Handle native variant options
        currentArray.forEach(variantSku => {
          if (nativeVariantsWithOptions[variantSku]) {
            generateCombinations(variantSku, rest, currentOptions);
          }
        });
      } else {
        // Handle custom options
        currentArray.options.forEach(option => {
          const newOptions = [
            ...currentOptions,
            { name: currentArray.heading, value: option },
          ];

          if (rest.length === 0 && baseVariant) {
            // At the end of options, create the combination
            const customOptionsSuffix = newOptions
              .map(opt => opt.value)
              .join(' - ');

            combinations.push({
              sku: `${baseVariant} - ${customOptionsSuffix}`,
              nativeOptions:
                nativeVariantsWithOptions[baseVariant].selectedOptions,
              nativeVariantId: nativeVariantsWithOptions[baseVariant].id,
              customOptions: newOptions,
            });
          } else {
            generateCombinations(baseVariant, rest, newOptions);
          }
        });
      }
    };

    generateCombinations('', allOptions);

    return combinations;
  }

  categorizeProduct(product: NativeProduct): ProductType {
    const CUSTOMIZER_TYPES = [
      ProductType.CUSTOM_OPTIONS,
      ProductType.PDP_CUSTOMIZER,
      ProductType.PRODUCT_CUSTOMIZER,
    ];

    const customizerMetafield = product.metafields.find(edge =>
      CUSTOMIZER_TYPES.includes(edge.key as ProductType),
    );

    return customizerMetafield
      ? (customizerMetafield.key as ProductType)
      : ProductType.PRODUCT_KEY_TYPE;
  }

  parseProductVariantsAndOptions(variants: any[], options: any[]) {
    const skuMap = {};
    variants.forEach(variant => {
      const selectedOptions = options.map(option => {
        const variantOptionValue = variant[`option${option.position}`];
        return {
          name: option.name,
          value: variantOptionValue,
        };
      });
      skuMap[variant.sku] = {
        id: variant.admin_graphql_api_id,
        selectedOptions,
      };
    });

    return skuMap;
  }
}

export class ShopifyPdpCustomizerSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: NativeProduct): TransformedProduct[] {
    const customOptionsObject = product?.metafields.find(
      field => field.key === 'pdp_customizer',
    );

    const nativeVariants = product.variants.map(nv => nv?.sku);
    const nativeVariantsWithOptions = this.parseProductVariantsAndOptions(
      product.variants,
      product.options,
    );

    const uniqueNativeVariants = [...new Set(nativeVariants)].filter(
      (v): v is string => v !== null,
    );

    const customOptions = JSON.parse(customOptionsObject?.value ?? '');

    const customOptionsSteps = customOptions.steps.filter(
      co => co.native === false,
    );

    let allCustomOptions: { heading: string; options: string[] }[] = [];
    if (customOptionsSteps.length) {
      allCustomOptions = customOptionsSteps.flatMap(co =>
        co.swatches.map(swatch => ({
          heading: co.step_name,
          options: swatch?.options?.map(opt => opt.label) || [],
        })),
      );
    }

    const skuList = this.generateCombinationsFromAllVariants(
      [uniqueNativeVariants],
      nativeVariantsWithOptions,
    );

    const transformedProducts = skuList
      .filter(sku => sku != null)
      .map(sku => ({
        id: product.admin_graphql_api_id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields,
        variantId: sku.nativeVariantId,
        variantNativeOptions: sku.nativeOptions,
        variantCustomOptions: sku.customOptions,
      }));

    return transformedProducts;
  }
}

export class ShopifyCustomOptionsSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: NativeProduct): TransformedProduct[] {
    const customOptionsObject = product?.metafields.find(
      field => field.key === 'custom_options',
    );

    const nativeVariants = product.variants.map(nv => nv?.sku);
    const nativeVariantsWithOptions = this.parseProductVariantsAndOptions(
      product.variants,
      product.options,
    );
    const uniqueNativeVariants = [
      ...new Set(nativeVariants.filter((v): v is string => v !== null)),
    ] as string[];

    const customOptions = JSON.parse(customOptionsObject?.value ?? '');
    const allCustomOptions = customOptions.map(co => {
      return {
        heading: co.heading,
        options: co?.options?.map(opt => opt.name) || [],
      };
    });

    const skuList = this.generateCombinationsFromAllVariants(
      [uniqueNativeVariants as string[], ...allCustomOptions],
      nativeVariantsWithOptions,
    );

    const transformedProducts = skuList
      .filter(sku => sku != null)
      .map(sku => ({
        id: product.admin_graphql_api_id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields,
        variantId: sku.nativeVariantId,
        variantNativeOptions: sku.nativeOptions,
        variantCustomOptions: sku.customOptions,
      }));

    return transformedProducts;
  }
}

export class ShopifyProductCustomizerSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: NativeProduct): TransformedProduct[] {
    const skuValue = product?.variants
      ?.filter(v => v?.sku)
      .map(v => ({ sku: v.sku!, id: v.id }));

    const transformedProducts = skuValue
      .filter(sku => sku.sku != null)
      .map(sku => ({
        id: product.admin_graphql_api_id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields,
        variantNativeOptions: [],
        variantCustomOptions: [],
      }));

    return transformedProducts;
  }
}

export class ShopifyProductKeyTypeSku
  extends ShopifyTransformUtils
  implements ShopifyProductType
{
  transformProduct(product: NativeProduct): TransformedProduct[] {
    const skuValue = product?.variants
      ?.filter(v => v?.sku)
      .map(v => ({ sku: v.sku!, id: v.admin_graphql_api_id }));

    const transformedProducts = skuValue
      .filter(sku => sku.sku != null)
      .map(sku => ({
        id: product.admin_graphql_api_id,
        sku: sku.sku,
        title: product.title,
        handle: product.handle,
        status: product.status,
        description: product.description,
        productType: product.productType,
        metafields: product.metafields,
        variantId: sku.id,
        variantNativeOptions: [],
        variantCustomOptions: [],
      }));

    return transformedProducts;
  }
}

class ShopifyWebhookProductTransformService extends ShopifyTransformUtils {
  private transformer = new Map<ProductType, ShopifyProductType>();

  constructor() {
    super();
    this.transformer.set(
      ProductType.PDP_CUSTOMIZER,
      new ShopifyPdpCustomizerSku(),
    );
    this.transformer.set(
      ProductType.CUSTOM_OPTIONS,
      new ShopifyCustomOptionsSku(),
    );
    this.transformer.set(
      ProductType.PRODUCT_CUSTOMIZER,
      new ShopifyProductCustomizerSku(),
    );

    this.transformer.set(
      ProductType.PRODUCT_KEY_TYPE,
      new ShopifyProductKeyTypeSku(),
    );
  }

  transform(product: NativeProduct) {
    if (JSON.parse(accessEnv('SKU_TRANSFORMER'))) {
      const nativeVariants = product.variants.map(nv => nv?.sku);
      const uniqueNativeVariants = [...new Set(nativeVariants)].filter(
        (v): v is string => v !== null,
      );
      const nativeVariantsWithOptions = this.parseProductVariantsAndOptions(
        product.variants,
        product.options,
      );

      const skuList = this.generateCombinationsFromAllVariants(
        [uniqueNativeVariants],
        nativeVariantsWithOptions,
      );

      const description = product.description;

      const transformedProduct = skuList
        .filter(sku => sku != null)
        .map(sku => ({
          id: product.admin_graphql_api_id,
          sku: sku.sku,
          title: product.title,
          handle: product.handle,
          status: product.status,
          description: description,
          productType: product.productType,
          metafields: product.metafields,
          variantId: sku.nativeVariantId,
          variantNativeOptions: sku.nativeOptions,
          variantCustomOptions: [],
        }));

      return transformedProduct;
    } else {
      const productType = this.categorizeProduct(product);

      if (!productType) {
        return [];
      }

      const transformer = this.transformer.get(productType);

      if (!transformer) {
        throw new Error(
          `No transformer found for product type: ${productType}`,
        );
      }

      return transformer.transformProduct(product);
    }
  }
}

export const shopifyWebhookProductTransformClient =
  new ShopifyWebhookProductTransformService();
