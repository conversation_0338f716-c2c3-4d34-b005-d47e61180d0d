import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductSkuService } from './product-sku.service';
import { ProductSkuController } from './product-sku.controller';
import { ProductSku } from './entities/product-sku.entity';
import { Product } from './entities/product.entity';
import { Vendor } from 'src/vendors/entities/vendor.entity';
import { ArtworkType } from 'src/artwork-types/entities/artwork-type.entity';
import { SkuRelationship } from './entities/sku-relationship.entity';
import { VendorSupportedSku } from 'src/vendors/entities/vendor-supported-sku.entity';
import { HistoryModule } from 'src/common/history.module';
import { UsersModule } from 'src/users/users.module';
import { SkuAddon } from './entities/sku-addon.entity';
import { ShopifyWebhookGuard } from 'src/auth/guards/shopify-webhook-guard';
import { ShopifyWebhookStrategy } from 'src/auth/strategies/shopify-webhook-strategy';
import { PermissionsModule } from '../permissions/permissions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProductSku,
      Product,
      Vendor,
      ArtworkType,
      SkuRelationship,
      VendorSupportedSku,
      SkuAddon,
    ]),
    HistoryModule,
    UsersModule,
    PermissionsModule,
  ],
  controllers: [ProductSkuController],
  providers: [ProductSkuService, ShopifyWebhookStrategy, ShopifyWebhookGuard],
})
export class ProductSkuModule {}
