import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON>ping<PERSON>ethod,
  CropType,
  ExceptionHandlingRule,
  FileUploadFormat,
  ImageInheritRule,
  RoutingMethod,
  ShippingMethod,
  VendorAssignmentRule,
  WorkflowCategory,
} from '../enums/product-sku.enums';
import { ArtworkType } from 'src/artwork-types/entities/artwork-type.entity';
import { Vendor } from 'src/vendors/entities/vendor.entity';
import {
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
  Entity,
  Column,
  ManyToMany,
} from 'typeorm';
import { BaseEntity } from 'src/common/base.entity';
import { SkuRelationship } from './sku-relationship.entity';
import { Product } from './product.entity';
import { VendorSupportedSku } from 'src/vendors/entities/vendor-supported-sku.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { AddonLevel } from '../enums/product-sku.enums';
import { SkuAddon } from './sku-addon.entity';
@Entity('product_sku')
export class ProductSku extends BaseEntity {
  @Column({ unique: true })
  @Index()
  @ApiProperty({ description: 'The SKU identifier', example: 'SKU-1234' })
  sku: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Can has rush', example: true })
  hasRush: boolean;

  @Column('int', { nullable: true })
  @ApiProperty({ description: 'Rush processing days', required: false })
  rushDays: number;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'The image naming convention',
    example: 'orderNum-itemNum-variant',
  })
  imageNamingConvention: string;

  @Column({ type: 'enum', enum: WorkflowCategory, nullable: true })
  @ApiProperty({
    description: 'The Workflow Category',
    enum: WorkflowCategory,
  })
  workflowCategory: WorkflowCategory;

  @Column({ type: 'int', nullable: true })
  @ApiProperty({ description: 'Image inheritance priority', example: 1 })
  imageInheritancePriority: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Ship station store name', example: 'MyStore' })
  shipStationStore: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @ApiProperty({ description: 'Shipping weight in lbs', example: 1.5 })
  shippingWeight: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @ApiProperty({ description: 'Product length (inches)', example: 12 })
  productLength: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @ApiProperty({ description: 'Product width (inches)', example: 8 })
  productWidth: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @ApiProperty({ description: 'Product height (inches)', example: 4 })
  productHeight: number;

  @Column({ type: 'decimal', nullable: true })
  @ApiProperty({ description: 'China WOFE Price', example: 12.99 })
  chinaWOFEPrice: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Can be up sold', example: true })
  canUpSold: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Can be cross-sold', example: true })
  canCrossSold: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Requires image upload', example: true })
  requireImageUpload: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Requires cropping', example: false })
  requireCropping: boolean;

  @Column({ type: 'enum', enum: CroppingMethod, nullable: true })
  @ApiProperty({ description: 'Cropping method', enum: CroppingMethod })
  croppingMethod: CroppingMethod;

  @Column({ type: 'enum', enum: VendorAssignmentRule, nullable: true })
  @ApiProperty({
    description: 'Vendor Assignment Rule',
    enum: VendorAssignmentRule,
  })
  vendorAssignmentRule: VendorAssignmentRule;

  @Column({ type: 'enum', enum: CropType, nullable: true })
  @ApiProperty({ description: 'Crop type', enum: CropType })
  cropType: CropType;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Cropping review required', example: false })
  croppingReviewRequired: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Artwork required', example: false })
  artworkRequired: boolean;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Requires customer artwork approval',
    example: false,
  })
  requireCustomerArtworkApproval: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Requires template', example: false })
  requireTemplate: boolean;

  @Column({ type: 'enum', enum: FileUploadFormat, nullable: true })
  @ApiProperty({
    description: 'Allowed file upload format',
    enum: FileUploadFormat,
  })
  fileUploadFormat: FileUploadFormat;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Can inherit image', example: false })
  canInheritImage: boolean;

  @Column({ type: 'enum', enum: ImageInheritRule, nullable: true })
  @ApiProperty({
    description: 'Image inheritance rule',
    enum: ImageInheritRule,
  })
  imageInheritRule: ImageInheritRule;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Can manually override', example: false })
  canManualOverride: boolean;

  @Column({ type: 'enum', enum: ExceptionHandlingRule, nullable: true })
  @ApiProperty({
    description: 'Exception handling rule',
    enum: ExceptionHandlingRule,
  })
  exceptionHandlingRule: ExceptionHandlingRule;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Customer follow-up enabled', example: false })
  customerFollowupEnabled: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Requires preprocessing', example: false })
  requirePreprocessing: boolean;

  @Column({ type: 'int', nullable: true })
  @ApiProperty({ description: 'Priority ranking for production', example: 1 })
  processingPriority: number;

  @Column({ type: 'enum', enum: ShippingMethod, nullable: true })
  @ApiProperty({ description: 'Shipping method', enum: ShippingMethod })
  shippingMethod: ShippingMethod;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Has work paper', example: false })
  hasWorkPaper: boolean;

  @Column({ type: 'enum', enum: RoutingMethod, nullable: true })
  @ApiProperty({ description: 'Routing method', enum: RoutingMethod })
  routingMethod: RoutingMethod;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: 'Shopify native variant',
    example: 'Small / Pajama Set / 1 Pet',
  })
  shopifyNativeVariant: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: 'Shopify native variant',
    example: 'Small / Pajama Set / 1 Pet',
  })
  shopifyCustomVariant: Record<string, any>;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @ApiProperty({ description: 'Price', example: 12.5 })
  price: number;

  @ManyToOne(() => ArtworkType, artworkType => artworkType.productSku, {
    nullable: true,
  })
  @JoinColumn({ name: 'artworkTypesId' })
  @ApiProperty({ description: 'The artwork type associated with the SKU' })
  artworkType: ArtworkType;

  @ManyToOne(() => Vendor, vendor => vendor.productSku, { nullable: true })
  @JoinColumn({ name: 'primaryVendorId' })
  @ApiProperty({ description: 'Primary vendor for the SKU' })
  primaryVendor: Vendor;

  @OneToMany(() => VendorSupportedSku, sku => sku.productSku)
  supportedSkus: VendorSupportedSku[];

  @ManyToMany(() => Product, product => product.productSku)
  @ApiProperty({ description: 'List of products associated with this SKU' })
  products: Product[];

  @OneToMany(
    () => SkuRelationship,
    skuRelationship => skuRelationship.parentSku,
    { nullable: true },
  )
  @ApiProperty({ description: 'Child SKUs' })
  childSku: SkuRelationship[];

  @OneToMany(
    () => SkuRelationship,
    skuRelationship => skuRelationship.childSku,
    { nullable: true },
  )
  @ApiProperty({ description: 'Parent SKUs' })
  parentSku: SkuRelationship[];

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Whether the product SKU is active',
    nullable: true,
  })
  isActive: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'is addon', example: false })
  isAddon: boolean;

  @Column({ type: 'enum', enum: AddonLevel, nullable: true })
  @ApiProperty({ description: 'addon level', enum: AddonLevel })
  addonLevel: AddonLevel;

  @Column({ nullable: true })
  @ApiProperty({ description: 'variant id', example: '123456789' })
  variantId: string;

  @OneToMany(() => LineItem, lineItem => lineItem.productSku, {
    nullable: true,
  })
  @ApiProperty({
    description: 'List of Line Items having this ProductSku',
  })
  lineItems: LineItem[];
}
