import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class OrderTrackingRequestDto {
  @ApiProperty({
    description: 'The shopify order number',
    example: '12345',
  })
  @IsString()
  @IsNotEmpty()
  orderNumber: string;

  @ApiProperty({
    description: 'Customer email address for validation',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Form type for specific data requirements',
    example: 'newImageRequest',
    required: false,
  })
  @IsOptional()
  @IsString()
  formType?: string;
}
