import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderTrackingController } from './order-tracking.controller';
import { OrderTrackingService } from './order-tracking.service';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { EmailService } from '../utils/email.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      LineItem,
      ProductSku,
      Attachment,
    ]),
  ],
  controllers: [OrderTrackingController],
  providers: [OrderTrackingService, EmailService],
  exports: [OrderTrackingService],
})
export class OrderTrackingModule {} 