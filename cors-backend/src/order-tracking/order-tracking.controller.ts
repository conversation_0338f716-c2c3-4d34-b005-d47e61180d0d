import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UsePipes,
  ValidationPipe,
  HttpException,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { OrderTrackingService } from './order-tracking.service';
import { FixedTokenGuard } from '../auth/guards/fixed-token.guard';
import { OrderTrackingRequestDto } from './dto/order-tracking-request.dto';

@UseGuards(FixedTokenGuard)
@ApiTags('Order Tracking')
@Controller('order-tracking')
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }),
)
export class OrderTrackingController {
  constructor(private readonly orderTrackingService: OrderTrackingService) {}

  @Post()
  @ApiOperation({
    summary: 'Get order details by order number and customer email',
    description:
      'API to retrieve order details using the shopify order number and customer email for validation. Requires fixed customer access token.',
  })
  @ApiBody({
    type: OrderTrackingRequestDto,
    description: 'Order tracking request with order number and email',
    examples: {
      example1: {
        value: {
          orderNumber: 'CC-12345',
          email: '<EMAIL>',
        },
        summary: 'Example order tracking request',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Order details retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        shopifyOrderNumber: { type: 'string' },
        orderDate: { type: 'string', format: 'date-time' },
        orderStatus: { type: 'string' },
        statusUpdatedAt: { type: 'string', format: 'date-time' },
        customerFirstName: { type: 'string' },
        customerLastName: { type: 'string' },
        customerEmail: { type: 'string' },
        customerPhoneNumber: { type: 'string' },
        itemCount: { type: 'number' },
        shippingAddress: { type: 'object' },
        billingAddress: { type: 'object' },
        paymentInformation: { type: 'object' },
        lineItems: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              itemNumber: { type: 'string' },
              quantity: { type: 'number' },
              status: { type: 'string' },
              priority: { type: 'string' },
            },
          },
        },
        orderStatusUrl: { type: 'string', description: 'URL to check order status' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request - missing required fields',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        message: { type: 'string' },
        error: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing customer access token',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        message: { type: 'string' },
        error: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid order number or email combination',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        message: { type: 'string' },
        error: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        message: { type: 'string' },
        error: { type: 'string' },
      },
    },
  })
  async getOrderDetails(@Body() request: OrderTrackingRequestDto) {
    console.log('getOrderDetails', request);
    return this.handleOrderDetailsRequest(request.orderNumber, request.email, request.formType);
  }

  @Get()
  @ApiOperation({
    summary: 'Get order details by order number and customer email (GET)',
    description:
      'API to retrieve order details using the shopify order number and customer email as query parameters. Requires fixed customer access token.',
  })
  @ApiQuery({
    name: 'orderNumber',
    description: 'The shopify order number (e.g., CC-12345)',
    example: 'CC-12345',
    required: true,
  })
  @ApiQuery({
    name: 'email',
    description: 'Customer email address for validation',
    example: '<EMAIL>',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Order details retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        shopifyOrderNumber: { type: 'string' },
        orderDate: { type: 'string', format: 'date-time' },
        orderStatus: { type: 'string' },
        statusUpdatedAt: { type: 'string', format: 'date-time' },
        customerFirstName: { type: 'string' },
        customerLastName: { type: 'string' },
        customerEmail: { type: 'string' },
        customerPhoneNumber: { type: 'string' },
        itemCount: { type: 'number' },
        shippingAddress: { type: 'object' },
        billingAddress: { type: 'object' },
        paymentInformation: { type: 'object' },
        lineItems: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              itemNumber: { type: 'string' },
              quantity: { type: 'number' },
              status: { type: 'string' },
              priority: { type: 'string' },
            },
          },
        },
        orderStatusUrl: { type: 'string', description: 'URL to check order status' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request - missing required fields',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing customer access token',
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid order number or email combination',
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  async getOrderDetailsByQuery(
    @Query('orderNumber') orderNumber: string,
    @Query('email') email: string,
  ) {
    return this.handleOrderDetailsRequest(orderNumber, email);
  }

  @Get('rejected-image')
  @ApiOperation({
    summary: 'Get rejected image data for new image request',
    description:
      'API to retrieve rejected image data for a specific order and item number. Used for new image request forms.',
  })
  @ApiQuery({
    name: 'orderNumber',
    description: 'The shopify order number (e.g., CC-12345)',
    example: 'CC-12345',
    required: true,
  })
  @ApiQuery({
    name: 'email',
    description: 'Customer email address for validation',
    example: '<EMAIL>',
    required: true,
  })
  @ApiQuery({
    name: 'itemNumber',
    description: 'Specific item number to get rejected image for',
    example: 'ITEM-001',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Rejected image data retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        url: { type: 'string' },
        rejectionReason: { type: 'string' },
        itemNumber: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request - missing required fields',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing customer access token',
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid order number or email combination',
  })
  @ApiResponse({
    status: 404,
    description: 'Rejected image not found',
  })
  async getRejectedImageData(
    @Query('orderNumber') orderNumber: string,
    @Query('email') email: string,
    @Query('itemNumber') itemNumber?: string,
  ) {
    try {
      if (!orderNumber || !email) {
        throw new HttpException(
          'Order number and email are required',
          HttpStatus.BAD_REQUEST,
        );
      }
      return await this.orderTrackingService.getRejectedImageData(
        orderNumber,
        email,
        itemNumber,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
          error: 'Internal Server Error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async handleOrderDetailsRequest(orderNumber: string, email: string, formType?: string) {
    try {
      if (!orderNumber || !email) {
        throw new HttpException(
          'Order number and email are required',
          HttpStatus.BAD_REQUEST,
        );
      }
      return await this.orderTrackingService.getOrderDetails(
        orderNumber,
        email,
        formType,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
          error: 'Internal Server Error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
