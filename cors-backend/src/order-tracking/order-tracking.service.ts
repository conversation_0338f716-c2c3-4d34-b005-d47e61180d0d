import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { EmailService } from '../utils/email.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class OrderTrackingService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(LineItem)
    private readonly lineItemRepository: Repository<LineItem>,
    @InjectRepository(ProductSku)
    private readonly productSkuRepository: Repository<ProductSku>,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
  ) {}

  async getOrderDetails(
    orderNumber: string,
    customerEmail: string,
    formType?: string,
  ): Promise<any> {
    const order = await this.orderRepository.findOne({
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: customerEmail,
      },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    // Transform the data to match the expected response format
    const transformedOrder = {
      id: order.id,
      shopifyOrderNumber: order.shopifyOrderNumber,
      orderDate: order.orderDate,
      orderStatus: order.orderStatus,
      statusUpdatedAt: order.statusUpdatedAt,
      customerFirstName: order.customerFirstName,
      customerLastName: order.customerLastName,
      customerEmail: order.customerEmail,
      customerPhoneNumber: order.customerPhoneNumber,
      itemCount: order.itemCount,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      paymentInformation: order.paymentInformation,
      lineItems: order.lineItems?.map((item) => ({
        id: item.id,
        itemNumber: item.itemNumber,
        quantity: item.quantity,
        status: item.status,
        priority: item.priority,
      })),
      orderStatusUrl: `${this.configService.get('FRONTEND_URL')}/order-status?shopifyOrderNumber=${order.shopifyOrderNumber}&customerEmail=${encodeURIComponent(order.customerEmail)}`,
    };

    return transformedOrder;
  }

  async getOrderByShopifyOrderId(shopifyOrderId: string, customerEmail: string) {
    const order = await this.orderRepository.findOne({
      where: { shopifyOrderId },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
    }

    return this.getOrderDetails(order.shopifyOrderNumber, customerEmail);
  }

  async getRejectedImageData(
    orderNumber: string,
    customerEmail: string,
    itemNumber?: string,
  ): Promise<any> {
    // First validate the order exists and belongs to the customer
    const order = await this.orderRepository.findOne({
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: customerEmail,
      },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    // Find line items that have rejected images
    let targetLineItems = order.lineItems;

    if (itemNumber) {
      targetLineItems = order.lineItems.filter(item => item.itemNumber === itemNumber);
      if (targetLineItems.length === 0) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Item not found',
            error: 'Not Found',
          },
          HttpStatus.NOT_FOUND,
        );
      }
    }

    // Look for rejected images in attachments
    // This is a placeholder implementation - you'll need to adjust based on your actual data structure
    for (const lineItem of targetLineItems) {
      const rejectedAttachment = await this.attachmentRepository.findOne({
        where: {
          lineItemId: lineItem.id,
          // Add conditions to identify rejected images based on your schema
          // For example: attachmentType: 'rejected_image' or similar
        },
      });

      if (rejectedAttachment) {
        return {
          url: rejectedAttachment.url || rejectedAttachment.filePath,
          rejectionReason: rejectedAttachment.description || 'Image rejected',
          itemNumber: lineItem.itemNumber,
        };
      }
    }

    // If no rejected image found
    throw new HttpException(
      {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'No rejected image found for this order',
        error: 'Not Found',
      },
      HttpStatus.NOT_FOUND,
    );
  }
}