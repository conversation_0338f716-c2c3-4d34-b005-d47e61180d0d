import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Attachment } from './entities/attachment.entity';
import { Repository } from 'typeorm';
import { StorageService } from './services/storage.service';
import { Multer } from 'multer';

@Injectable()
export class AttachmentService {
  constructor(
    @InjectRepository(Attachment)
    private attachmentRepository: Repository<Attachment>,
    private storageService: StorageService,
  ) {}

  async upload(file: Multer['File']) {
    const uploadResult = await this.storageService.uploadFile(file);

    const attachment = this.attachmentRepository.create({
      ...uploadResult,
    });

    return this.attachmentRepository.save(attachment);
  }

  async getAttachments() {
    return this.attachmentRepository.find();
  }

  async delete(id: string) {
    const attachment = await this.attachmentRepository.findOne({
      where: { id },
    });
    if (attachment) {
      await this.storageService.deleteFile(attachment.filename);
      await this.attachmentRepository.remove(attachment);
    }
  }

  async uploadImage(file: string) {
    const uploadResult = await this.storageService.uploadFile(file);
    return uploadResult;
  }
}
