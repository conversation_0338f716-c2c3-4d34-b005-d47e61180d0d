import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Get,
  Param,
  Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Multer } from 'multer';
import { AttachmentService } from './attachment.service';

const heicConvert = require('heic-convert');
@Controller('attachments')
export class AttachmentController {
  constructor(private readonly attachmentService: AttachmentService) {}

  @Post('upload-image')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: Multer.File) {
    const processedFile = await this.convertHeicToJpgIfNeeded(file);
    return this.attachmentService.uploadImage(processedFile);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async upload(@UploadedFile() file: Multer.File) {
    const processedFile = await this.convertHeicToJpgIfNeeded(file);
    return this.attachmentService.upload(processedFile);
  }

  @Get()
  async getAttachments() {
    return this.attachmentService.getAttachments();
  }

  @Delete(':id')
  async deleteAttachment(@Param('id') id: string) {
    return this.attachmentService.delete(id);
  }

  private async convertHeicToJpgIfNeeded(
    file: Multer.File,
  ): Promise<Multer.File> {
    if (!file.mimetype.includes('heic') && !file.mimetype.includes('heif')) {
      return file;
    }

    const outputBuffer = await heicConvert({
      buffer: file.buffer,
      format: 'JPEG',
      quality: 0.9,
    });

    return {
      ...file,
      buffer: outputBuffer,
      originalname: file.originalname.replace(/\.(heic|heif)$/i, '.jpg'),
      mimetype: 'image/jpeg',
    };
  }
}
