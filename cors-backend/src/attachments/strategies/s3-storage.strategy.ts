import { Injectable } from '@nestjs/common';
import { StorageStrategy } from './storage.strategy';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import { Multer } from 'multer';

@Injectable()
export class S3StorageStrategy implements StorageStrategy {
  private readonly s3Client: S3Client;
  private readonly bucket: string;
  private readonly region: string;

  constructor() {
    this.bucket = process.env.AWS_S3_BUCKET || '';
    this.region = process.env.AWS_REGION || 'us-east-1';

    this.s3Client = new S3Client({
      region: this.region,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
      }
    });
  }

  async uploadFile(file: Multer["File"]) {
    const filename = `${uuidv4()}${path.extname(file.originalname)}`;

    await this.s3Client.send(new PutObjectCommand({
      Bucket: this.bucket,
      Key: filename,
      Body: file.buffer,
      ContentType: file.mimetype
    }));

    return {
      url: `https://${this.bucket}.s3.${this.region}.amazonaws.com/${filename}`,
      filename,
      mimetype: file.mimetype,
      size: file.size
    };
  }

  async deleteFile(filename: string) {
    await this.s3Client.send(new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: filename
    })).catch(() => { });
  }
} 