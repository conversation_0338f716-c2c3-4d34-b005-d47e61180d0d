import { Injectable } from '@nestjs/common';
import { StorageStrategy } from './storage.strategy';
import * as fs from 'fs/promises';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { Multer } from 'multer';
import { accessEnv } from 'src/env.validation';

@Injectable()
export class LocalStorageStrategy implements StorageStrategy {
  private readonly uploadDir = 'uploads';
  private readonly baseUrl: string;

  constructor() {
    // Ensure upload directory exists
    fs.mkdir(this.uploadDir, { recursive: true }).catch(console.error);
    this.baseUrl = accessEnv('BASE_URL') || 'http://localhost:3000';
  }

  async uploadFile(file: Multer["File"]) {
    const filename = `${uuidv4()}${path.extname(file.originalname)}`;
    const filePath = path.join(this.uploadDir, filename);

    await fs.writeFile(filePath, file.buffer);

    return {
      url: `${this.baseUrl}/uploads/${filename}`,
      filename: filename,
      mimetype: file.mimetype,
      size: file.size
    };
  }

  async deleteFile(filename: string) {
    const filePath = path.join(this.uploadDir, filename);
    await fs.unlink(filePath).catch(() => { });
  }
} 