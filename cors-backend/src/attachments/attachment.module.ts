import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AttachmentController } from './attachment.controller';
import { AttachmentService } from './attachment.service';
import { StorageService } from './services/storage.service';
import { Attachment } from './entities/attachment.entity';
import { STORAGE_STRATEGY } from './strategies/storage.strategy';
import { LocalStorageStrategy } from './strategies/local-storage.strategy';
import { S3StorageStrategy } from './strategies/s3-storage.strategy';
import { CloudinaryStorageStrategy } from './strategies/cloudinary-storage.strategy';
import { accessEnv } from '../env.validation';

const useS3 = accessEnv('STORAGE_DRIVER') === 's3';
const useCloudinary = accessEnv('STORAGE_DRIVER') === 'cloudinary';

@Module({
  imports: [
    TypeOrmModule.forFeature([Attachment])
  ],
  controllers: [AttachmentController],
  providers: [
    AttachmentService,
    StorageService,
    {
      provide: STORAGE_STRATEGY,
      useClass: useS3 ? S3StorageStrategy : useCloudinary ? CloudinaryStorageStrategy : LocalStorageStrategy
    }
  ],
  exports: [
    AttachmentService,
    StorageService,
    STORAGE_STRATEGY
  ]
})
export class AttachmentModule { } 