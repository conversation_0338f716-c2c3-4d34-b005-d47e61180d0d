import { User } from 'src/users/entities/user.entity';
import { Column, Entity, ManyToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';

interface Permission {
  resource: string;
  actions: string[];
}

@Entity({ name: 'roles' })
export class Role extends BaseEntity {
  @Column({ unique: true, type: 'citext' })
  @ApiProperty({ description: 'The name of the role' })
  name: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'jsonb', default: [] })
  @ApiProperty({
    description: 'The permissions of the role',
    type: [Object],
    example: [{ resource: 'Product Management', actions: ['view', 'edit'] }],
  })
  rolePermissions: Permission[];

  @Column({ default: 0 })
  @ApiProperty({
    description: 'Total count of permissions in the role',
    example: 2,
  })
  permissionCount: number;

  @ManyToMany(() => User, user => user.roles)
  @ApiProperty({ description: 'The users of the role' })
  users: User[];
}
