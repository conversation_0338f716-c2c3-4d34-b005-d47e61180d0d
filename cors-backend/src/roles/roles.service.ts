import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, ILike, Repository } from 'typeorm';
import { Role } from './entities/role.entity';
import { BaseService } from '../common/base.service';
import { permissions } from '../permissions/permissions';

@Injectable()
export class RolesService extends BaseService<Role> {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {
    super(roleRepository);
  }

  getPermissions() {
    return permissions;
  }

  private calculatePermissionCount(role: Role) {
    const permissions = role.rolePermissions ?? [];
    role.permissionCount = permissions.reduce(
      (total, permission) => total + (permission.actions?.length || 0),
      0,
    );
  }

  async createRole(data: DeepPartial<Role>): Promise<Role> {
    await this.validateRoleName(data.name as string);
    const role = this.roleRepository.create(data as DeepPartial<Role>);
    this.calculatePermissionCount(role);
    return this.roleRepository.save(role);
  }

  private async validateRoleName(
    name: string,
    currentRoleName?: string,
  ): Promise<void> {
    if (!name || (currentRoleName && name === currentRoleName)) {
      return;
    }
    const existingRole = await this.roleRepository.findOne({
      where: {
        name: ILike(name),
      },
    });

    if (existingRole) {
      throw new BadRequestException('Role name must be unique');
    }
  }

  async updateRole(id: string, data: DeepPartial<Role>): Promise<Role> {
    const role = await this.findByIdOrThrow(id);
    await this.validateRoleName(data.name as string, role.name);

    Object.assign(role, data);
    this.calculatePermissionCount(role);
    return this.roleRepository.save(role);
  }
}
