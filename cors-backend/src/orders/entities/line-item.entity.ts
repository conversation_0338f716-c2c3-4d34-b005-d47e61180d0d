import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { Order } from './order.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { LineItemRequest } from './line-item-request.entity';
import { Product } from 'src/product-sku/entities/product.entity';
import { Queue } from 'src/workflow-queues/entities/queue.entity';
import { User } from 'src/users/entities/user.entity';
@Entity({ name: 'line_items' })
export class LineItem extends BaseEntity {
  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Shopify Line Item ID', example: '1234567890' })
  shopifyItemId: number;

  @ApiProperty({ description: 'Current status of the line item' })
  @Column({ nullable: true })
  status: string;

  @ApiProperty({ description: 'Unique identifier for the line item' })
  @Column({ nullable: true })
  itemNumber: string;

  @ApiProperty({
    description: 'Whether the line item has been flagged for attention',
  })
  @Column({ default: false })
  flagged: boolean;

  @ApiProperty({ description: 'Whether this is a remake item' })
  @Column({ default: false })
  isRemake: boolean;

  @ApiProperty({
    description: 'Array of reasons for remaking the item',
    example: ['Quality Issue', 'Wrong Size', 'Design Error'],
    type: [String],
  })
  @Column('text', { array: true, default: [] })
  remakeReason: string[];

  @ApiProperty({
    description: 'Array of detailed explanations for remaking the item',
    example: ['Color mismatch', 'Fabric quality issue', 'Wrong measurements'],
    type: [String],
  })
  @Column('text', { array: true, default: [] })
  detailedRemakeReason: string[];

  @ApiProperty({ description: 'Reason for flagging the line item' })
  @Column({ nullable: true })
  flagReason: string;

  @ApiProperty({
    description:
      'Cancellation information including status, reason, timestamp and username',
    example: {
      status: 'cancelled',
      reason: 'Customer Changed Mind',
      timestamp: '2024-03-21T10:00:00Z',
      username: 'john.doe',
    },
  })
  @Column({ type: 'jsonb', nullable: true })
  cancelReason: {
    status: string;
    reason: string;
    timestamp: Date;
    username: string;
  };

  @ApiProperty({ description: 'Quantity of the item ordered' })
  @Column({ type: 'int', nullable: true })
  quantity: number;

  @ApiProperty({ description: 'Priority level of the line item' })
  @Column({ nullable: true })
  priority: string;

  @ApiProperty({
    description: 'Array of notes for the line item',
    type: [String],
  })
  @Column('text', { array: true, default: [] })
  notes: string[];

  @ApiProperty({ description: 'Metadata of the line item' })
  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @OneToMany(() => Attachment, attachment => attachment.lineItem)
  attachments: Attachment[];

  @OneToMany(() => LineItemRequest, request => request.lineItem)
  requests: LineItemRequest[];

  @ManyToOne(() => ProductSku, productSku => productSku.lineItems)
  productSku: ProductSku;

  @ManyToOne(() => Product, product => product.lineItems)
  product: Product;

  @ManyToOne(() => Order, order => order.lineItems)
  order: Order;

  @ApiProperty({ description: 'Check line item has gift box or not' })
  @Column({ default: false })
  hasGift: boolean;

  @ApiProperty({ description: 'Zipper Pouch' })
  @Column({ default: false })
  zipperPouch: boolean;

  @ApiProperty({ description: 'Heartbeat Box' })
  @Column({ default: false })
  heartbeatBox: boolean;

  @ApiProperty({ description: 'Cuddle Crate' })
  @Column({ default: false })
  cuddleCrate: boolean;

  @ApiProperty({ description: 'Custom Bandana' })
  @Column({ default: false })
  customBandana: boolean;

  @ApiProperty({ description: 'Custom Bandana Value' })
  @Column({ nullable: true })
  customBandanaValue: string;

  @ApiProperty({ description: 'Digital Download' })
  @Column({ default: false })
  digitalDownload: boolean;

  @ManyToOne(() => Queue, queue => queue.lineItems, {
    nullable: true,
  })
  queue: Queue;

  @ManyToOne(() => User, user => user.lineItems, {
    nullable: true,
  })
  assignedTo: User;
}
