import { Column, Entity, ManyToOne, OneToMany, Index, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { LineItem } from './line-item.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';

export enum RequestType {
  REMAKE = 'remake',
  ARTWORK = 'artwork',
  CUSTOMER_CONTACT = 'customer_contact'
}

@Entity({ name: 'line_item_requests' })
@Index(['lineItemId', 'type'])
export class LineItemRequest extends BaseEntity {
  @ApiProperty({ 
    description: 'Type of request',
    enum: RequestType
  })
  @Column({
    type: 'enum',
    enum: RequestType
  })
  type: RequestType;

  @ApiProperty({ description: 'Additional notes or details about the request' })
  @Column({ nullable: true })
  notes: string;

  @Column()
  lineItemId: string;

  @ManyToOne(() => LineItem, lineItem => lineItem.requests, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'lineItemId' })
  lineItem: LineItem;

  @OneToMany(() => Attachment, attachment => attachment.request)
  attachments: Attachment[];
} 