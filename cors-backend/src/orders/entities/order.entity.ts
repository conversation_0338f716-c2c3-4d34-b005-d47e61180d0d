import { Column, Entity, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { OrderStatus } from '../enums/order.enums';
import { LineItem } from './line-item.entity';

@Entity({ name: 'orders' })
export class Order extends BaseEntity {
  @Column({ nullable: true })
  @Index()
  @ApiProperty({ description: 'Shopify Order Number', example: '1234567890' })
  shopifyOrderNumber: string;

  @Column({ unique: true, nullable: true })
  @ApiProperty({ description: 'Shopify Order Number', example: '1234567890' })
  shopifyOrderId: string;

  @Column('timestamp')
  @ApiProperty({
    description: 'Order Date Timestamp',
    example: '2025-04-28T12:34:56Z',
  })
  orderDate: Date;

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.UNFULFILLED })
  @ApiProperty({ description: 'Order Status', enum: OrderStatus })
  orderStatus: OrderStatus;

  @Column('timestamp', { nullable: true })
  @ApiProperty({
    description: 'Status Update Timestamp',
    example: '2025-04-28T12:34:56Z',
  })
  statusUpdatedAt: Date;

  @Column('boolean', { default: false })
  @ApiProperty({
    description: 'Flagged Order?',
    example: false,
    required: false,
  })
  flagged: boolean;

  @Column('timestamp', { nullable: true })
  @ApiProperty({
    description: 'Timestamp of when the order was flagged',
    required: false,
    example: '2025-04-28T12:34:56Z',
  })
  flaggedAt: Date;

  @Column()
  @ApiProperty({ description: 'Customer First Name', example: 'John' })
  customerFirstName: string;

  @Column()
  @ApiProperty({ description: 'Customer Last Name', example: 'Doe' })
  customerLastName: string;

  @Column()
  @ApiProperty({
    description: 'Customer Email',
    example: '<EMAIL>',
  })
  customerEmail: string;

  @Column()
  @ApiProperty({ description: 'Customer Phone Number', example: '+1234567890' })
  customerPhoneNumber: string;

  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Shopify Customer ID', example: 9876543210 })
  shopifyCustomerId: number;

  @Column({ type: 'jsonb', nullable: true })
  @ApiProperty({
    description: 'Payment Information in JSON format',
    example: 'shipping total, 10.55',
  })
  paymentInformation: Record<string, any>;

  @Column('jsonb')
  @ApiProperty({
    description: 'Billing Address in JSON format',
    required: false,
  })
  billingAddress: object;

  @Column('jsonb')
  @ApiProperty({
    description: 'Shipping Address in JSON format',
    required: false,
  })
  shippingAddress: object;

  @Column('int')
  @ApiProperty({
    description: 'Number of Items in the Order',
    required: false,
    example: 5,
  })
  itemCount: number;

  @Column('text', { array: true, default: [] })
  @ApiProperty({
    description: 'Array of priority values for the order',
    example: ['2 Weeks rush', 'Blanket Rush', 'Holiday Delivery'],
    type: [String],
  })
  priorities: string[];

  @OneToMany(() => LineItem, lineItem => lineItem.order)
  @ApiProperty({
    description: 'List of Line Items belonging to this Order',
    type: () => [LineItem],
  })
  lineItems: LineItem[];
}
