import { ApiProperty } from '@nestjs/swagger';
import { OrderStatus } from '../enums/order.enums';
import { LineItem } from '../entities/line-item.entity';

export class CreateOrderDto {
  @ApiProperty({ description: 'Shopify Order Number', example: '1234567890', required: false })
  shopifyOrderNumber?: string;

  @ApiProperty({ description: 'Shopify Order ID', example: '1234567890', required: false })
  shopifyOrderId?: string;

  @ApiProperty({ description: 'Order Date', example: '2025-04-28T12:34:56Z' })
  orderDate: Date;

  @ApiProperty({ description: 'Order Status', enum: OrderStatus })
  orderStatus: OrderStatus;

  @ApiProperty({ description: 'Status Updated At', example: '2025-04-28T12:34:56Z' })
  statusUpdatedAt?: Date;

  @ApiProperty({ description: 'Is Order Flagged?', example: false, required: false })
  flagged: boolean;

  @ApiProperty({ description: 'Flagged At Timestamp', example: '2025-04-28T12:34:56Z', required: false })
  flaggedAt?: Date;

  @ApiProperty({ description: 'Customer First Name', example: 'John' })
  customerFirstName: string;

  @ApiProperty({ description: 'Customer Last Name', example: 'Doe' })
  customerLastName: string;

  @ApiProperty({ description: 'Customer Email', example: '<EMAIL>' })
  customerEmail: string;

  @ApiProperty({ description: 'Customer Phone Number', example: '+1234567890' })
  customerPhoneNumber: string;

  @ApiProperty({ description: 'Shopify Customer ID', example: 9876543210, required: false })
  shopifyCustomerId?: number;

  @ApiProperty({ description: 'Billing Address', type: Object })
  billingAddress: object;

  @ApiProperty({ description: 'Shipping Address', type: Object })
  shippingAddress: object;

  @ApiProperty({ description: 'Item Count', example: 5 })
  itemCount: number;

  @ApiProperty({
    description: 'Line Items',
    type: () => [LineItem],
    isArray: true
  })
  lineItems: Partial<LineItem>[];
}
