import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsNotEmpty, IsString, IsUUID, IsOptional, IsObject } from 'class-validator';

export class RemakeLineItemDto {
  @ApiProperty({
    description: 'ID of the line item to be remade',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  lineItemId: string;

  @ApiProperty({
    description: 'Array of reasons for remaking the item',
    example: ['Quality Issue', 'Wrong Size', 'Design Error'],
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  remakeReason: string[];

  @ApiProperty({
    description: 'Array of detailed explanations for remaking the item',
    example: ['Color mismatch', 'Fabric quality issue', 'Wrong measurements'],
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  detailedRemakeReason: string[];

  @ApiProperty({
    description: 'Product SKU ID for the remake item',
    example: '22157977-5c11-477a-b56b-022345e22647',
    required: false
  })
  @IsOptional()
  @IsUUID()
  productSkuId?: string;

  @ApiProperty({
    description: 'Metadata of the line item',
    example: {
      customField1: 'value1',
      customField2: 'value2'
    },
    required: false
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
} 