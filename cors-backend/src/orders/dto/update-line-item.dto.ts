import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, IsArray, IsUUID, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class CancelReasonDto {
  @ApiProperty({ description: 'Reason for cancellation', example: 'Customer Changed Mind' })
  @IsString()
  status: string;

  @ApiProperty({ description: 'Username of who cancelled the item', example: 'john.doe' })
  @IsString()
  username: string;
}

export class UpdateLineItemDto {
  @ApiProperty({ description: 'Product SKU ID', example: '22157977-5c11-477a-b56b-022345e22647', required: false })
  @IsOptional()
  @IsUUID()
  productSkuId?: string;

  @ApiProperty({ description: 'Shopify Line Item ID', example: '1234567890', required: false })
  @IsOptional()
  @IsNumber()
  shopifyItemId?: number;

  @ApiProperty({ description: 'Current status of the line item', required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ description: 'Unique identifier for the line item', required: false })
  @IsOptional()
  @IsString()
  itemNumber?: string;

  @ApiProperty({ description: 'Whether the line item has been flagged for attention', required: false })
  @IsOptional()
  @IsBoolean()
  flagged?: boolean;

  @ApiProperty({ description: 'Reason for flagging the line item', required: false })
  @IsOptional()
  @IsString()
  flagReason?: string;

  @ApiProperty({ 
    description: 'Reason for cancellation (if cancelling)', 
    required: false,
    example: 'Customer Changed Mind',
    type: String
  })
  @IsOptional()
  @IsString()
  cancelReason?: string;

  @ApiProperty({ description: 'Quantity of the item ordered', required: false })
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @ApiProperty({ description: 'Priority level of the line item', required: false })
  @IsOptional()
  @IsString()
  priority?: string;

  @ApiProperty({ description: 'Array of notes for the line item', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  notes?: string[];

  @ApiProperty({ description: 'Metadata of the line item', required: false })
  @IsOptional()
  metadata?: Record<string, any>;
} 