import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  UsePipes,
  ValidationPipe,
  HttpException,
  HttpStatus,
  Request,
  Req,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import { Multer } from 'multer';
import { OrderStatus } from './enums/order.enums';
import { FilterOrdersDto } from './dto/filter-orders.dto';
import { Order } from './entities/order.entity';
import { SkipAuth } from 'src/auth/decorators/public.decorator';
import { UpdateLineItemDto } from './dto/update-line-item.dto';
import { BREEDS } from 'src/constants/breeds';
import { RemakeLineItemDto } from './dto/remake-line-item.dto';
import { IField } from 'src/product-sku/product-sku.controller';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { DateHelper } from 'src/helpers/date.helper';
import { PermissionsGuard } from '../permissions/permissions.guards';
import { Permissions } from '../permissions/permissions.decorator';
import { FieldPermissionsGuard } from '../permissions/field-permissions.guard';
import { FieldPermissions } from '../permissions/field-permissions.decorator';
import { PermissionResources } from '../constants/permission-resources';
import { getFieldPermissions } from 'src/permissions/field-permissions.config';

interface FormLineItem {
  productSkuId: string;
  images: Multer['File'][];
}

interface IOrderField extends Omit<IField, 'key'> {
  key:
    | keyof Pick<
        Order,
        | 'id'
        | 'shopifyOrderNumber'
        | 'shopifyOrderId'
        | 'orderDate'
        | 'orderStatus'
        | 'statusUpdatedAt'
        | 'flagged'
        | 'flaggedAt'
        | 'customerFirstName'
        | 'customerLastName'
        | 'customerEmail'
        | 'customerPhoneNumber'
        | 'shopifyCustomerId'
        | 'paymentInformation'
        | 'billingAddress'
        | 'shippingAddress'
        | 'itemCount'
        | 'priorities'
      >
    | 'lineItems.productSkuId'
    | 'lineItems.shopifyItemId'
    | 'lineItems.cancelReason'
    | 'cancelReason'
    | 'remakeReason'
    | 'detailedRemakeReason';
  remakeReason?: Record<string, string[]>;
}

@UseGuards(JwtGuard, PermissionsGuard, FieldPermissionsGuard)
@ApiBearerAuth()
@ApiTags('Orders')
@Controller('orders')
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }),
)
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    @InjectQueue('order-sync') private orderSyncQueue: Queue,
    @InjectQueue('shopify-order') private shopifyOrderQueue: Queue,
  ) {}

  private readonly fields: IOrderField[] = [
    {
      key: 'shopifyOrderNumber',
      label: 'Shopify Order Number',
      type: 'number',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Order Information',
    },

    {
      key: 'statusUpdatedAt',
      label: 'Days in status',
      type: 'number',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Order Information',
    },

    {
      key: 'orderDate',
      label: 'Order Date',
      type: 'date',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Order Information',
    },
    {
      key: 'orderStatus',
      label: 'Order Status',
      type: 'select',
      options: Object.values(OrderStatus).map(value => ({
        value,
        key: value,
      })),
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Order Information',
    },
    {
      key: 'customerFirstName',
      label: 'Customer First Name',
      type: 'text',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Customer Information',
    },
    {
      key: 'customerLastName',
      label: 'Customer Last Name',
      type: 'text',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Customer Information',
    },
    {
      key: 'customerEmail',
      label: 'Customer Email',
      type: 'text',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Customer Information',
    },
    {
      key: 'customerPhoneNumber',
      label: 'Customer Phone',
      type: 'text',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Customer Information',
    },
    {
      key: 'itemCount',
      label: 'Item Count',
      type: 'number',
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Order Information',
    },
    {
      key: 'flagged',
      label: 'Flagged',
      type: 'select',
      options: [
        { value: true, key: 'Yes' },
        { value: false, key: 'No' },
      ],
      usage: 'filter',
      sortable: true,
      json_fields: false,

      show_in_list: true,
      category: 'Order Information',
    },
    {
      key: 'lineItems.productSkuId',
      label: 'Product SKU',
      type: 'text',
      usage: 'filter',
      sortable: false,
      json_fields: false,

      show_in_list: true,
      category: 'Line Items',
    },
    {
      key: 'lineItems.shopifyItemId',
      label: 'Shopify Item ID',
      type: 'number',
      usage: 'filter',
      sortable: false,
      json_fields: false,
      show_in_list: true,
      category: 'Line Items',
    },
  ];

  private formatLabel(key: string): string {
    return key
      .split(/(?=[A-Z])/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  @Get('breeds')
  @ApiOperation({ summary: 'Get breeds by species' })
  @ApiQuery({
    name: 'species',
    required: true,
    type: String,
    description: 'Species to get breeds for (e.g. dog, horse)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns breeds for the specified species',
  })
  async getBreeds(@Query('specie') specie: string) {
    return BREEDS(specie);
  }

  @Post('manual-order')
  @ApiOperation({ summary: 'Create a new manual order with line items' })
  @ApiResponse({
    status: 201,
    description: 'Returns the created order with line items',
  })
  async createManualOrder(@Body() body: any) {
    const shippingAddress = {
      address: body.shipping_address.address1,
      city: body.shipping_address.city,
      state: body.shipping_address.state,
      country: body.shipping_address.country,
      zip: body.shipping_address.zip,
      countryCode: body.shipping_address.country_code,
    };
    const billingAddress = {
      address: body.billing_address.address1,
      city: body.billing_address.city,
      state: body.billing_address.state,
      country: body.billing_address.country,
      zip: body.billing_address.zip,
      countryCode: body.billing_address.country_code,
    };
    const orderData = {
      customerFirstName: body.customer.first_name,
      customerLastName: body.customer.last_name,
      customerEmail: body.customer.email,
      customerPhoneNumber: body.customer.phone,
      shippingAddress,
      billingAddress,
      orderStatus: OrderStatus.UNFULFILLED,
      orderDate: new Date(),
      flagged: false,
      itemCount: body.line_items.length,
      lineItems: body.line_items,
    };
    return this.ordersService.createManualOrder(orderData);
  }

  @SkipAuth()
  @Post('webhook_orders')
  async webhookOrders(@Body() body: any) {
    await this.shopifyOrderQueue.add('shopify-order', {
      order: body,
      runAt: DateHelper.getCurrentDateString(),
    });
    return { message: 'Order sync job queued successfully' };
  }

  @Post('resync_orders')
  @ApiOperation({ summary: 'Resync orders' })
  @ApiResponse({ status: 200, description: 'Returns the order' })
  async resyncOrders() {
    await this.orderSyncQueue.add('order-sync', {
      runAt: DateHelper.getCurrentDateString(),
    });
    return { message: 'Order sync job queued successfully' };
  }

  @Post()
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({ status: 201, description: 'Returns the created order' })
  create(@Body() createOrderDto: CreateOrderDto) {
    return this.ordersService.create(createOrderDto);
  }

  @Permissions(PermissionResources.ORDERS, 'View Order Listing Page')
  @Get()
  @ApiOperation({ summary: 'Get all orders' })
  @ApiResponse({ status: 200, description: 'Returns all orders' })
  @ApiQuery({ name: 'q', required: false, type: String })
  @ApiQuery({ name: 'fq', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async findAll(
    @Query('q') q?: string,
    @Query('fq') fq?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.ordersService.findAll({
      q,
      fq,
      page,
      limit,
      relations: ['lineItems', 'lineItems.productSku'],
    });
  }

  @Get('fields')
  @ApiOperation({
    summary: 'Get all available fields for orders and line items',
  })
  @ApiResponse({ status: 200, description: 'Returns array of field names' })
  async getAvailableFields(): Promise<{ fields: IOrderField[] }> {
    const fields: IOrderField[] = [
      {
        key: 'shopifyOrderNumber',
        label: 'Order #',
        type: 'number',
        usage: 'filter',
        sortable: false,
        show_in_list: true,
        json_fields: false,
        fetch_db: true,
      },
      {
        key: 'statusUpdatedAt',
        label: 'Days in status',
        type: 'number',
        usage: 'filter',
        sortable: false,
        show_in_list: false,
        category: 'Order Information',
        json_fields: false,
      },
      {
        key: 'orderDate',
        label: 'Order Date',
        type: 'date',
        usage: 'filter',
        sortable: false,
        show_in_list: true,
        json_fields: false,
      },
      {
        key: 'itemCount',
        label: 'No of items',
        type: 'number',
        usage: 'filter',
        sortable: false,
        show_in_list: true,
        json_fields: false,
      },
      {
        key: 'customerFirstName',
        label: 'Customer First Name',
        type: 'text',
        usage: 'filter',
        sortable: false,
        show_in_list: true,
        json_fields: false,
      },
      {
        key: 'customerLastName',
        label: 'Customer Last Name',
        type: 'text',
        usage: 'filter',
        sortable: false,
        show_in_list: true,
        json_fields: false,
      },
      {
        key: 'customerEmail',
        label: 'Email',
        type: 'text',
        usage: 'filter',
        sortable: false,
        show_in_list: true,
        json_fields: false,
      },
      {
        key: 'priorities',
        label: 'Priority',
        type: 'multi_select',
        usage: 'filter',
        options: [
          { value: '2 Weeks Rush', key: '2 Weeks Rush' },
          { value: '4 Weeks Rush', key: '4 Weeks Rush' },
          { value: '6 Weeks Rush', key: '6 Weeks Rush' },
          { value: 'Pajama Rush', key: 'Pajamas Rush' },
          { value: 'Socks Rush', key: 'Socks Rush' },
          { value: 'Jewellery Rush', key: 'Jewellery Rush' },
        ],
        sortable: false,
        show_in_list: true,
        json_fields: false,
      },

      {
        key: 'lineItems.cancelReason',
        label: 'Cancel Reason',
        type: 'select',
        usage: undefined,
        options: [
          { value: 'Fraudulent Order', key: 'Fraudulent Order' },
          { value: 'Duplicate Order', key: 'Duplicate Order' },
          { value: 'Customer Not Reachable', key: 'Customer Not Reachable' },
          { value: 'Ordered By Mistake', key: 'Ordered By Mistake' },
          { value: 'Incorrect Item Ordered', key: 'Incorrect Item Ordered' },
          { value: 'Payment Method', key: 'Payment Method' },
          { value: 'No Reason Given', key: 'No Reason Given' },
          { value: 'Duplicate Gift', key: 'Duplicate Gift' },
          {
            value: 'Requested Info Not Provided',
            key: 'Requested Info Not Provided',
          },
          { value: 'Design Limitation', key: 'Design Limitation' },
          { value: 'Long Production Time', key: 'Long Production Time' },
          { value: 'Internal System Issue', key: 'Internal System Issue' },
          {
            value: 'Sweater- colors dont match pet',
            key: 'Sweater- colors dont match pet',
          },
          {
            value: 'Portrait- # of pets w/ size ordered',
            key: 'Portrait- # of pets w/ size ordered',
          },
          {
            value: 'Portrait-  illustration only bust up',
            key: 'Portrait-  illustration only bust up',
          },
          { value: 'Portrait- font change', key: 'Portrait- font change' },
          {
            value: 'Portrait- wont add additional details around pet',
            key: 'Portrait- wont add additional details around pet',
          },
          {
            value: 'Portrait/Sweater- Image not usable',
            key: 'Portrait/Sweater- Image not usable',
          },
          {
            value: 'Portrait/Sweater- human included',
            key: 'Portrait/Sweater- human included',
          },
          {
            value: 'Sweater- wants more colors',
            key: 'Sweater- wants more colors',
          },
          {
            value: 'Sweater - cant do font request',
            key: 'Sweater - cant do font request',
          },
          { value: 'Customer Changed Mind', key: 'Customer Changed Mind' },
          { value: 'Financial Contraint', key: 'Financial Contraint' },
          { value: 'Grief', key: 'Grief' },
          { value: 'Plush - Fur Color', key: 'Plush - Fur Color' },
          { value: 'Plush - Too Small', key: 'Plush - Too Small' },
          { value: 'Plush - Too Large', key: 'Plush - Too Large' },
          { value: 'Plush - Eyes', key: 'Plush - Eyes' },
          { value: 'Plush - Fur Pattern', key: 'Plush - Fur Pattern' },
          { value: 'Plush - Shape', key: 'Plush - Shape' },
          { value: 'Plush - Other', key: 'Plush - Other' },
          {
            value: 'Plush - Partial Satisfaction Refund',
            key: 'Plush - Partial Satisfaction Refund',
          },
        ],
        sortable: false,
        show_in_list: false,
        json_fields: false,
      },
      {
        key: 'orderStatus',
        label: 'Status',
        type: 'select',
        usage: 'filter',
        options: Object.values(OrderStatus).map(value => ({
          value,
          key: value,
        })),
        sortable: true,
        show_in_list: true,
        json_fields: false,
      },
      {
        key: 'remakeReason',
        label: 'Remake Reason',
        type: 'select',
        usage: undefined,
        options: [
          { value: 'cropped image', key: 'cropped image' },
          { value: 'art', key: 'art' },
          { value: 'background / template', key: 'background / template' },
          { value: 'size', key: 'size' },
          { value: 'production', key: 'production' },
          { value: 'transit', key: 'transit' },
        ],
        sortable: false,
        show_in_list: false,
        json_fields: false,
      },
      {
        key: 'detailedRemakeReason',
        label: 'Detailed Remake Reason',
        type: 'select',
        usage: undefined,
        remakeReason: {
          'cropped image': [
            'no new image provided',
            'blurry - approved',
            'low resolution - approved',
            'dark image - approved',
            'small image - approved',
            'large image - approved',
            'contains human - approved',
            'bad crop - approved',
            'sideways image',
            'upside down image',
            'bad manual crop',
            'accessory missing',
            'accessory not removed',
            'incorrect crop type',
            'should not be crop',
            'inconsistent crop style',
            'missing pet/human - approved',
          ],
          art: [
            'skipped approval',
            'incorrect art style',
            'line art detail quality',
            'name missing',
            'name misspelled',
            'color contrast issue',
            'sweater color issue',
            'incorrect background color',
            'incorrect pet color',
          ],
          'background / template': [
            'low face count',
            'background discoloration',
            'missing additional pet in cors',
            'missing additional pet on product',
            'incorrect background assigned',
            'incorrect background used',
            'incorrect background printed',
          ],
          size: [
            'incorrect size ordered',
            'incorrect size made',
            'inseam made short',
            'inseam made long',
            'waist made large',
            'waist made small',
            'shirt made short',
            'shirt made long',
            'sleeves made short',
            'sleeves made long',
          ],
          production: [
            'incorrect style made',
            'bad print quality',
            'bad sewing quality',
            'lost in production',
            'incorrect quantity made',
          ],
          transit: [
            'lost in transit',
            'delivered not received',
            'not assigned to vendor',
            'rts',
            'damaged on delivery',
          ],
        },
        options: [
          { value: 'no new image provided', key: 'no new image provided' },
          { value: 'blurry - approved', key: 'blurry - approved' },
          {
            value: 'low resolution - approved',
            key: 'low resolution - approved',
          },
          { value: 'dark image - approved', key: 'dark image - approved' },
          { value: 'small image - approved', key: 'small image - approved' },
          { value: 'large image - approved', key: 'large image - approved' },
          {
            value: 'contains human - approved',
            key: 'contains human - approved',
          },
          { value: 'bad crop - approved', key: 'bad crop - approved' },
          { value: 'sideways image', key: 'sideways image' },
          { value: 'upside down image', key: 'upside down image' },
          { value: 'bad manual crop', key: 'bad manual crop' },
          { value: 'accessory missing', key: 'accessory missing' },
          { value: 'accessory not removed', key: 'accessory not removed' },
          { value: 'incorrect crop type', key: 'incorrect crop type' },
          { value: 'should not be crop', key: 'should not be crop' },
          { value: 'inconsistent crop style', key: 'inconsistent crop style' },
          {
            value: 'missing pet/human - approved',
            key: 'missing pet/human - approved',
          },
          { value: 'skipped approval', key: 'skipped approval' },
          { value: 'incorrect art style', key: 'incorrect art style' },
          { value: 'line art detail quality', key: 'line art detail quality' },
          { value: 'name missing', key: 'name missing' },
          { value: 'name misspelled', key: 'name misspelled' },
          { value: 'color contrast issue', key: 'color contrast issue' },
          { value: 'sweater color issue', key: 'sweater color issue' },
          {
            value: 'incorrect background color',
            key: 'incorrect background color',
          },
          { value: 'incorrect pet color', key: 'incorrect pet color' },
          { value: 'low face count', key: 'low face count' },
          {
            value: 'background discoloration',
            key: 'background discoloration',
          },
          {
            value: 'missing additional pet in cors',
            key: 'missing additional pet in cors',
          },
          {
            value: 'missing additional pet on product',
            key: 'missing additional pet on product',
          },
          {
            value: 'incorrect background assigned',
            key: 'incorrect background assigned',
          },
          {
            value: 'incorrect background used',
            key: 'incorrect background used',
          },
          {
            value: 'incorrect background printed',
            key: 'incorrect background printed',
          },
          { value: 'incorrect size ordered', key: 'incorrect size ordered' },
          { value: 'incorrect size made', key: 'incorrect size made' },
          { value: 'inseam made short', key: 'inseam made short' },
          { value: 'inseam made long', key: 'inseam made long' },
          { value: 'waist made large', key: 'waist made large' },
          { value: 'waist made small', key: 'waist made small' },
          { value: 'shirt made short', key: 'shirt made short' },
          { value: 'shirt made long', key: 'shirt made long' },
          { value: 'sleeves made short', key: 'sleeves made short' },
          { value: 'sleeves made long', key: 'sleeves made long' },
          { value: 'incorrect style made', key: 'incorrect style made' },
          { value: 'bad print quality', key: 'bad print quality' },
          { value: 'bad sewing quality', key: 'bad sewing quality' },
          { value: 'lost in production', key: 'lost in production' },
          { value: 'incorrect quantity made', key: 'incorrect quantity made' },
          { value: 'lost in transit', key: 'lost in transit' },
          { value: 'delivered not received', key: 'delivered not received' },
          { value: 'not assigned to vendor', key: 'not assigned to vendor' },
          { value: 'rts', key: 'rts' },
          { value: 'damaged on delivery', key: 'damaged on delivery' },
        ],
        sortable: false,
        show_in_list: false,
        json_fields: false,
      },
    ];

    return { fields };
  }

  @Permissions(PermissionResources.ORDERS, 'Order Edit/Detail Page')
  @Get(':id')
  @ApiOperation({ summary: 'Get a order by ID' })
  @ApiResponse({ status: 200, description: 'Returns the order' })
  findOne(@Param('id') id: string) {
    return this.ordersService.findByIdOrThrow(id, [
      'lineItems',
      'lineItems.productSku',
    ]);
  }

  @Permissions(PermissionResources.ORDERS, 'Order Edit/Detail Page')
  @Patch(':id')
  @ApiOperation({ summary: 'Update a order by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated order' })
  patch(@Param('id') id: string, @Body() updateOrder: UpdateOrderDto) {
    return this.ordersService.updateById(id, updateOrder);
  }

  @Permissions(PermissionResources.ORDERS, 'Order Edit/Detail Page')
  @Put(':id')
  @ApiOperation({ summary: 'Update a order by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated order' })
  put(@Param('id') id: string, @Body() updateOrder: UpdateOrderDto) {
    return this.ordersService.updateById(id, updateOrder);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a order by ID' })
  @ApiResponse({ status: 200, description: 'Returns the deleted order' })
  remove(@Param('id') id: string) {
    return this.ordersService.deleteById(id);
  }

  @Permissions(PermissionResources.ORDERS, 'View Order Listing Page')
  @Post('advanced-filter')
  @ApiOperation({
    summary: 'Filter orders based on multiple criteria',
    description: `
      Advanced filtering endpoint for orders with support for multiple conditions and operators.
    `,
  })
  @ApiBody({
    type: FilterOrdersDto,
    examples: {
      simpleFilter: {
        summary: 'Simple Filter',
        description: 'Filter orders by status',
        value: {
          filters: [
            [
              {
                attribute: 'orderStatus',
                operator: 'eq',
                value: 'unfulfilled',
              },
            ],
          ],
          page: 1,
          limit: 10,
        },
      },
      complexFilter: {
        summary: 'Complex Filter',
        description: 'Filter orders with multiple conditions',
        value: {
          filters: [
            [
              {
                attribute: 'orderStatus',
                operator: 'eq',
                value: 'unfulfilled',
              },
              { attribute: 'itemCount', operator: 'gt', value: 5 },
            ],
            [
              {
                attribute: 'customerEmail',
                operator: 'like',
                value: '@example.com',
              },
            ],
          ],
          page: 1,
          limit: 10,
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered orders with pagination',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              shopifyOrderNumber: { type: 'number' },
              orderDate: { type: 'string', format: 'date-time' },
              orderStatus: { type: 'string', enum: Object.values(OrderStatus) },
              customerFirstName: { type: 'string' },
              customerLastName: { type: 'string' },
              customerEmail: { type: 'string' },
              itemCount: { type: 'number' },
              lineItems: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    productSkuId: { type: 'string' },
                  },
                },
              },
            },
          },
        },
        count: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Validation error',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        message: { type: 'string' },
        error: { type: 'string' },
      },
    },
  })
  async filterOrders(@Body() filterDto: FilterOrdersDto) {
    try {
      filterDto.validate();
      return this.ordersService.filterOrders(filterDto);
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: error.message,
          error: 'Bad Request',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Permissions(PermissionResources.ORDERS, 'Order Edit/Detail Page')
  @Get('line-items/:id')
  @ApiOperation({ summary: 'Get a line item by ID' })
  @ApiResponse({ status: 200, description: 'Returns the line item' })
  async getLineItemById(@Param('id') id: string) {
    return this.ordersService.findLineItemById(id);
  }

  @Permissions(PermissionResources.ORDERS, 'Order Edit/Detail Page')
  @FieldPermissions(getFieldPermissions('lineItems', 'updateLineItem'))
  @Put('line-items/:id')
  @ApiOperation({ summary: 'Update a line item' })
  @ApiResponse({
    status: 200,
    description: 'The line item has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'Line item not found.' })
  async updateLineItem(
    @Param('id') id: string,
    @Body() updateLineItemDto: UpdateLineItemDto,
    @Request() req,
  ) {
    return this.ordersService.updateLineItem(id, updateLineItemDto, req.user);
  }

  @Permissions(PermissionResources.LINE_ITEMS, 'Create Line Item Remake')
  @Post('line-items/remake')
  @ApiOperation({ summary: 'Create a remake line item' })
  @ApiBody({
    type: RemakeLineItemDto,
    description: 'Data for creating a remake line item',
    examples: {
      example1: {
        value: {
          lineItemId: '123e4567-e89b-12d3-a456-426614174000',
          remakeReason: ['Quality Issue', 'Wrong Size'],
          detailedRemakeReason: ['Color mismatch', 'Fabric quality issue'],
        },
        summary: 'Example payload for creating a remake line item',
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The remake line item has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async remakeLineItem(@Body() remakeDto: RemakeLineItemDto, @Request() req) {
    return this.ordersService.remakeLineItem(remakeDto, req.user);
  }
}
