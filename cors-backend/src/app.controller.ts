import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { DataSource } from 'typeorm';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private dataSource: DataSource,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('db-status')
  async getDatabaseStatus(): Promise<{ status: string }> {
    try {
      // Check if database connection is alive
      const isConnected = this.dataSource.isInitialized;
      return { status: isConnected ? 'connected' : 'disconnected' };
    } catch (error) {
      return { status: `error: ${error.message}` };
    }
  }
}
