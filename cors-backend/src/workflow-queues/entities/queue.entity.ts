import { Column, Entity, Index, OneToMany } from 'typeorm';
import { BaseEntity } from 'src/common/base.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';

@Entity('queues')
export class Queue extends BaseEntity {
  @Column({ unique: true })
  @Index()
  name: string;

  @Column({ type: 'int', default: 5 })
  maxItemsToAssign: number;

  @OneToMany(() => LineItem, lineItem => lineItem.queue)
  lineItems: LineItem[];

  @OneToMany(() => Attachment, attachment => attachment.queue)
  attachments: Attachment[];
}
