// src/bull-board.ts
import { ExpressAdapter } from '@bull-board/express';
import { createBullBoard } from '@bull-board/api';
import { Queue } from 'bullmq';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { Logger } from '@nestjs/common';
import { accessEnv } from './env.validation';
import * as express from 'express';

const basicAuth = require('express-basic-auth');

export function setupBullBoard(app: express.Express, queues: Queue[]) {
  const logger = new Logger('BullBoard');
  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/queues');

  createBullBoard({
    queues: queues.map(queue => new BullMQAdapter(queue)),
    serverAdapter,
  });

  app.use(
    '/queues',
    basicAuth({
      users: {
        [accessEnv('BULLMQ_USERNAME')]: accessEnv('BULLMQ_PASSWORD'),
      },
      challenge: true,
      realm: 'Bull Board',
    }),
  );
  app.use('/queues', serverAdapter.getRouter());

  logger.log('Bull Board dashboard mounted at /queues');
}
