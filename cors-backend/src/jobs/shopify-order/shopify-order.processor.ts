import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { OrdersService } from '../../orders/orders.service';

@Processor('shopify-order')
export class ShopifyOrderProcessor extends WorkerHost {
  private readonly logger = new Logger(ShopifyOrderProcessor.name);

  constructor(private readonly ordersService: OrdersService) {
    super();
  }

  async process(job: Job) {
    if (job.data.order) {
      await this.ordersService.createOrderFromWebhook(job.data.order);
      this.logger.log('Shopify order created successfully');
      return;
    }
  }
}
