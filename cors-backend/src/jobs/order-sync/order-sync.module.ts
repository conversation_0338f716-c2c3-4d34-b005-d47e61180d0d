// daily-task.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { OrderSyncQueue } from './order-sync.queue';
import { OrderSyncProcessor } from './order-sync.processor';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { Order } from '../../orders/entities/order.entity';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { accessEnv } from 'src/env.validation';
import { EmailUtil } from 'src/utils/email.util';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'order-sync',
      connection: {
        host: accessEnv('REDIS_HOST'),
        port: accessEnv('REDIS_PORT'),
        password: accessEnv('REDIS_PASSWORD') || '',
        tls: JSON.parse(accessEnv('REDIS_SSL_ENABLED'))
          ? {
              rejectUnauthorized: false,
            }
          : undefined,
      },
    }),
    TypeOrmModule.forFeature([Order]),
    HttpModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
  ],
  providers: [OrderSyncQueue, OrderSyncProcessor, EmailUtil, ConfigService],
})
export class OrderSyncModule {}
