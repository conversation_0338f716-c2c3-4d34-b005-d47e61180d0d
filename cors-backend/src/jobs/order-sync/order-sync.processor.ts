import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { HttpService } from '@nestjs/axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../../orders/entities/order.entity';
import { firstValueFrom } from 'rxjs';
import * as qs from 'querystring';
import { EmailUtil } from '../../utils/email.util';
import { Logger } from '@nestjs/common';
import { DateHelper } from '../../helpers/date.helper';
import { accessEnv } from '../../env.validation';

@Processor('order-sync')
export class OrderSyncProcessor extends WorkerHost {
  private storeUrl: string;
  private accessToken: string;
  private shopName: string;
  private limit = 250;
  private readonly logger = new Logger(OrderSyncProcessor.name);

  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly httpService: HttpService,
    private readonly emailUtil: EmailUtil,
  ) {
    super();
    this.storeUrl = accessEnv('SHOPIFY_STORE_URL') || '';
    this.accessToken = accessEnv('SHOPIFY_API_SECRET') || '';
    this.shopName = accessEnv('SHOPIFY_STORE_NAME') || '';
  }

  async process(job: Job) {
    this.logger.log('************* Order Sync Job Start *************');
    const syncedOrders: number[] = [];
    const notSyncedOrders: number[] = [];
    const orders = await this.fetchOrders();
    for (const order of orders) {
      const existingOrder = await this.orderRepository.findOne({
        where: { shopifyOrderId: order.id },
      });
      if (order.financial_status === 'paid') {
        if (!existingOrder) {
          const success = await this.resyncOrderToCORS(order.id);
          if (success) {
            syncedOrders.push(order.id);
          } else {
            notSyncedOrders.push(order.id);
          }
        }
      }
    }
    if (syncedOrders.length || notSyncedOrders.length) {
      try {
        await this.emailUtil.sendOrderResyncEmail(
          syncedOrders,
          notSyncedOrders,
          this.shopName,
        );
      } catch (error) {
        this.logger.error('Failed to send email:', error);
      }
    } else {
      this.logger.log('No orders to send email about');
    }
    this.logger.log('************* Order Sync Job End *************');
  }

  private async fetchOrders(): Promise<any[]> {
    const orders: any[] = [];
    let pageInfo: string | undefined = undefined;
    let retryCount = 0;
    const maxRetries = 3;

    do {
      try {
        const { orders: pageOrders, nextPageInfo } =
          await this.getLast24HoursOrdersFromShopify(pageInfo);
        orders.push(...pageOrders);
        pageInfo = nextPageInfo;
        retryCount = 0;
      } catch (error) {
        // Handle dayjs error specifically
        if (error.message.includes('dayjs_1.default is not a function')) {
          this.logger.error('Error with date handling:', error);
          throw new Error(
            'Failed to process date in order sync. Please check dayjs configuration.',
          );
        }

        this.logger.error(`Failed to fetch orders page: ${error.message}`);
        if (retryCount < maxRetries) {
          retryCount++;
          this.logger.log(
            `Retrying page fetch attempt ${retryCount} of ${maxRetries}`,
          );
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          continue;
        }
        throw new Error(
          `Failed to fetch orders after ${maxRetries} retries: ${error.message}`,
        );
      }
    } while (pageInfo);
    return orders;
  }

  private async getLast24HoursOrdersFromShopify(pageInfo?: string) {
    const createdAtMin = DateHelper.getDateStringMinusHours(24);
    let url = `${this.storeUrl}/orders.json?status=any&created_at_min=${createdAtMin}&limit=${this.limit}`;
    if (pageInfo) {
      url = `${this.storeUrl}/orders.json?page_info=${pageInfo}&limit=${this.limit}`;
    }
    const headers = {
      'X-Shopify-Access-Token': this.accessToken,
      'Content-Type': 'application/json',
    };
    const response = await firstValueFrom(
      this.httpService.get(url, { headers }),
    );
    const linkHeader = response.headers['link'];
    const nextPageInfo = this.extractNextPageInfo(linkHeader);
    return {
      orders: response.data.orders || [],
      nextPageInfo,
    };
  }

  private extractNextPageInfo(linkHeader: string): string | undefined {
    if (!linkHeader) return undefined;
    const match = linkHeader.match(/<([^>]+)>; rel="next"/);
    if (!match) return undefined;
    const nextUrl = match[1];
    const query = nextUrl.split('?')[1];
    const params = qs.parse(query);
    return params.page_info as string;
  }

  private async resyncOrderToCORS(orderId: number): Promise<boolean> {
    const url = `${this.storeUrl}/orders/${orderId}.json`;
    const headers = {
      'X-Shopify-Access-Token': this.accessToken,
      'Content-Type': 'application/json',
    };
    try {
      await firstValueFrom(
        this.httpService.put(
          url,
          {
            order: {
              id: orderId,
              note: 'Resync to CORS',
            },
          },
          { headers },
        ),
      );
      return true;
    } catch (err) {
      this.logger.error(
        `Failed to resync order ${orderId}:`,
        err?.response?.data || err,
      );
      return false;
    }
  }
}
