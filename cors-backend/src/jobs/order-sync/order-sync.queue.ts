import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { DateHelper } from '../../helpers/date.helper';

@Injectable()
export class OrderSyncQueue implements OnModuleInit {
  constructor(@InjectQueue('order-sync') private readonly queue: Queue) {}

  async onModuleInit() {
    await this.queue.add(
      'order-sync',
      { runAt: DateHelper.getCurrentDateString() },
      {
        repeat: { pattern: '0 0 0 * * *' },
        jobId: 'order-sync-job',
      },
    );
  }
}
