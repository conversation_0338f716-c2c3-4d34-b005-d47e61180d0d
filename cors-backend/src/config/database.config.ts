import { registerAs } from '@nestjs/config';
import { accessEnv } from '../env.validation';

export default registerAs('database', () => ({
  type: 'postgres',
  host: accessEnv('DATABASE_HOST') || 'localhost',
  port: parseInt(accessEnv('DATABASE_PORT') || '5432', 10),
  username: accessEnv('DATABASE_USERNAME') || 'postgres',
  password: accessEnv('DATABASE_PASSWORD') || 'postgres',
  database: accessEnv('DATABASE_NAME') || 'cors-development',
  synchronize: false,
  logging: accessEnv('NODE_ENV') !== 'production',
  ssl:
    accessEnv('DATABASE_SSL_ENABLED') === 'true'
      ? {
        rejectUnauthorized: false,
      }
      : false,
}));
