#!/usr/bin/env node

import * as crypto from 'crypto';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

function generateCustomerToken() {
  const secret = process.env.JWT_FIXED_SECRET;

  if (!secret) {
    console.error(
      '❌ Error: JWT_FIXED_SECRET not found in environment variables',
    );
    process.exit(1);
  }

  const header = {
    alg: 'HS256',
    typ: 'JWT',
  };

  const payload = {
    aud: 'customer-access',
    iss: 'cors-backend',
    sub: 'customer',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60, // 1 year
  };

  const headerB64 = Buffer.from(JSON.stringify(header))
    .toString('base64')
    .replace(/=/g, '');
  const payloadB64 = Buffer.from(JSON.stringify(payload))
    .toString('base64')
    .replace(/=/g, '');

  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${headerB64}.${payloadB64}`)
    .digest('base64')
    .replace(/=/g, '');

  const token = `${headerB64}.${payloadB64}.${signature}`;

  console.log('Token:');
  console.log(token);
}

// Run the script
generateCustomerToken();
