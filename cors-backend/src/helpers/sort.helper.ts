import { BadRequestException } from '@nestjs/common';
import { SelectQueryBuilder, Repository, ObjectLiteral } from 'typeorm';

export class SortHelper {
  static applySorting<T extends ObjectLiteral>(
    qb: SelectQueryBuilder<T>,
    repository: Repository<T>,
    sortQuery?: string,
    alias = 'entity',
  ): void {
    if (!sortQuery) return;

    const validColumns = repository.metadata.columns.map(
      col => col.propertyName,
    );

    const validRelations = repository.metadata.relations.map(
      rel => rel.propertyName,
    );

    const sortFields = sortQuery.split(';').map(s => {
      const [field, direction] = s.split(':');

      const fieldParts = field.split('.');
      if (fieldParts.length > 2) {
        const [mainEntity, relation, nestedField] = fieldParts;
        if (mainEntity !== alias) {
          throw new BadRequestException(`Invalid main entity: ${mainEntity}`);
        }

        if (!validRelations.includes(relation)) {
          throw new BadRequestException(`Invalid relation: ${relation}`);
        }

        // Generate unique alias for the join
        const joinAlias = `${relation}_sort`;

        // For nested sorting, join the relation with unique alias
        qb.leftJoinAndSelect(`${alias}.${relation}`, joinAlias);

        return {
          field: `${joinAlias}.${nestedField}`,
          direction: direction.toUpperCase() as 'ASC' | 'DESC',
        };
      }

      if (!validColumns.includes(field)) {
        throw new BadRequestException(`Invalid sort field: ${field}`);
      }

      if (!['asc', 'desc'].includes(direction.toLowerCase())) {
        throw new BadRequestException(
          `Invalid sort direction for ${field}. Use 'asc' or 'desc'.`,
        );
      }

      return { field, direction: direction.toUpperCase() as 'ASC' | 'DESC' };
    });

    sortFields.forEach(({ field, direction }) => {
      let _direction = direction;
      if (field.includes('isActive')) {
        _direction = _direction === 'ASC' ? 'DESC' : 'ASC';
      }

      if (field.includes('.')) {
        qb.addOrderBy(field, _direction);
      } else {
        qb.addOrderBy(`${alias}.${field}`, _direction);
      }
    });
  }
}
