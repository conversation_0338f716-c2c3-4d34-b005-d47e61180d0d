import { BadRequestException, Logger } from '@nestjs/common';
import { <PERSON>ike, Not, Repository } from 'typeorm';

export class FilterHelper {
  private static readonly logger = new Logger(FilterHelper.name);

  static parseCombinedFilters(
    orQuery: string | undefined,
    andQuery: string | undefined,
    repository: Repository<any>,
  ): Record<string, any> {
    try {
      if (!orQuery && !andQuery) return [];

      const entityColumns = repository.metadata.columns.map(
        col => col.propertyName,
      );

      const orFiltersArr = this.parseFilterArray(
        orQuery?.split(';') || [],
        entityColumns,
      );
      const andFiltersArr = this.parseFilterArray(
        andQuery?.split(';') || [],
        entityColumns,
      );

      const result: Record<string, any> = {};

      if (orFiltersArr.length > 0) {
        result['$or'] = orFiltersArr;
      }

      andFiltersArr.forEach(filter => {
        const key = Object.keys(filter)[0];
        result[key] = filter[key];
      });

      return result;
    } catch (error) {
      this.logger.error(
        `Error in parseCombinedFilters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  static parseAdvancedFilters(
    filters: any[][],
    repository: Repository<any>,
  ): Record<string, any> {
    try {
      if (!filters || !Array.isArray(filters)) {
        throw new BadRequestException(
          'Invalid filters format. Expected an array of arrays.',
        );
      }

      this.logger.debug(
        `Processing advanced filters: ${JSON.stringify(filters)}`,
      );

      const entityColumns = repository.metadata.columns.map(
        col => col.propertyName,
      );
      const relations = repository.metadata.relations.map(
        rel => rel.propertyName,
      );
      const parsedFilters: any[] = [];

      for (const andGroup of filters) {
        const andConditions: any[] = [];

        for (const filter of andGroup) {
          const { attribute, operator, effectiveValue } = filter;

          this.logger.debug(
            `Processing filter: attribute=${attribute}, operator=${operator}, value=${JSON.stringify(effectiveValue)}`,
          );

          const parts = attribute.split('.');
          const isRelationPath = parts.length > 1;

          if (isRelationPath) {
            const [relationName, fieldName] = parts;

            if (!relations.includes(relationName)) {
              throw new BadRequestException(
                `Invalid relation: '${relationName}'. Available relations are: ${relations.join(', ')}`,
              );
            }

            const relation = repository.metadata.relations.find(
              r => r.propertyName === relationName,
            );
            const relatedEntityColumns =
              relation?.inverseEntityMetadata.columns.map(
                col => col.propertyName,
              ) || [];

            if (!relatedEntityColumns.includes(fieldName)) {
              throw new BadRequestException(
                `Invalid field '${fieldName}' in relation '${relationName}'. Available fields are: ${relatedEntityColumns.join(', ')}`,
              );
            }

            andConditions.push({
              [`${relationName}.${fieldName}`]: {
                _type: operator,
                _value: effectiveValue,
              },
            });
          } else {
            if (!entityColumns.includes(attribute)) {
              throw new BadRequestException(
                `Invalid filter key: '${attribute}'. Available fields are: ${entityColumns.join(', ')}`,
              );
            }

            andConditions.push({
              [attribute]: {
                _type: operator,
                _value: effectiveValue,
                keyField: filter.keyField,
              },
            });
          }
        }

        parsedFilters.push(andConditions);
      }

      return { $or: parsedFilters };
    } catch (error) {
      this.logger.error(
        `Error in parseAdvancedFilters: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private static parseFilterArray(
    filters: string[],
    validKeys: string[],
  ): Record<string, unknown>[] {
    try {
      const filtersArray: Record<string, unknown>[] = [];

      filters.forEach(filter => {
        const [key, op, value] = filter.split(':');

        if (!key || !op || !value) {
          throw new BadRequestException(
            `Invalid filter format. Use key:operator:value`,
          );
        }

        if (!validKeys.includes(key)) {
          throw new BadRequestException(`Invalid filter key: '${key}'`);
        }

        switch (op) {
          case 'like':
            filtersArray.push({ [key]: ILike(`%${value}%`) });
            break;
          case 'eq':
            filtersArray.push({ [key]: value });
            break;
          case 'ne':
            filtersArray.push({ [key]: `!=${value}` });
            break;
          default:
            throw new BadRequestException(`Unsupported operator: ${op}`);
        }
      });

      return filtersArray;
    } catch (error) {
      this.logger.error(
        `Error in parseFilterArray: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
