import { NotFoundException } from '@nestjs/common';
import {
  Repository,
  FindOptionsWhere,
  FindManyOptions,
  FindOptionsSelect,
  FindOneOptions,
} from 'typeorm';
import { BaseEntity } from 'src/common/base.entity';

export interface IOptions<T extends BaseEntity> {
  where?: FindManyOptions<T> | FindOptionsWhere<T>;
  select?: FindOptionsSelect<T> | undefined;
  q?: string | undefined;
  fq?: string | undefined;
  sort?: string | undefined;
  page?: number;
  limit?: number;
  relations?: string[];
  // TODO: Add orderBy, limit, offset and filters here
}

export class DBHelper {
  static async findMany<T extends BaseEntity>(
    repository: Repository<T>,
    sOptions?: IOptions<T>,
    excludeDeleted = true,
  ): Promise<T[]> {
    return repository.find({
      select: sOptions?.select ?? undefined,
      where: {
        ...sOptions?.where,
        ...(excludeDeleted && { deletedAt: null }),
      },
    } as FindManyOptions<T>);
  }

  static async findByIdOrThrow<T extends BaseEntity>(
    repository: Repository<T>,
    id: string,
    relations?: string[],
    select?: FindOptionsSelect<T>,
    excludeDeleted = true,
  ): Promise<T> {
    const entity = await repository.findOne({
      where: {
        id,
        ...(excludeDeleted && { deletedAt: null }),
      } as unknown as FindOptionsWhere<T>,
      relations,
      select,
    } as FindOneOptions<T>);

    if (!entity) {
      throw new NotFoundException(`Entity with ID ${id} not found`);
    }
    return entity;
  }

  static async findOne<T extends BaseEntity>(
    repository: Repository<T>,
    sOptions?: IOptions<T>,
    excludeDeleted = true,
  ): Promise<T | null> {
    const entity = await repository.findOne({
      select: sOptions?.select ?? undefined,
      where: {
        ...sOptions?.where,
        ...(excludeDeleted && { deletedAt: null }),
      },
      relations: sOptions?.relations,
    } as FindOneOptions<T>);
    return entity;
  }

  static validateEntityIds<T extends BaseEntity>(
    foundEntities: T[],
    expectedIds: (string | number)[],
    idSelector: (entity: T) => string | number = e => (e as any).id,
    entityName = 'Entity',
  ): void {
    const foundIds = foundEntities.map(idSelector);
    const missingIds = expectedIds.filter(id => !foundIds.includes(id));

    if (missingIds.length > 0) {
      throw new NotFoundException(
        `${entityName}(s) not found: ${missingIds.join(', ')}`,
      );
    }
  }

  static parseSelect<T>(select?: string): FindOptionsSelect<T> | undefined {
    if (!select) return { id: true } as unknown as FindOptionsSelect<T>;
    
    const fields: any = {};
  
    select.split(',').forEach((field) => {
      const parts = field.trim().split('.');
      let current = fields;
  
      parts.forEach((part, index) => {
        if (index === parts.length - 1) {
          current[part] = true;
        } else {
          if (!current[part]) current[part] = { id: true };
          current = current[part];
        }
      });
    });
  
    return fields as FindOptionsSelect<T>;
  }
}
