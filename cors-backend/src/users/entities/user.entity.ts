import { BaseEntity } from 'src/common/base.entity';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { Role } from 'src/roles/entities/role.entity';
import { Column, Entity, JoinTable, ManyToMany, OneToMany } from 'typeorm';

@Entity({ name: 'users' })
export class User extends BaseEntity {
  @Column({ type: 'citext' })
  firstName: string;

  @Column({ type: 'citext' })
  lastName: string;

  @Column({ unique: true, type: 'citext' })
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ default: true })
  isActive: boolean;

  @ManyToMany(() => Role, role => role.users)
  @JoinTable({
    name: 'user_role_pivot',
    joinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'roleId',
      referencedColumnName: 'id',
    },
  })
  roles: Role[];

  @OneToMany(() => LineItem, lineItem => lineItem.assignedTo)
  lineItems: LineItem[];

  @OneToMany(() => Attachment, attachment => attachment.assignedTo)
  attachments: Attachment[];
}
