import { Injectable } from '@nestjs/common';
import { AbilityBuilder, PureAbility } from '@casl/ability';
import { User } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';

export type Actions = string;
export type Subjects = string;

export type AppAbility = PureAbility<[Actions, Subjects]>;

@Injectable()
export class CaslAbilityFactory {
  constructor(private readonly usersService: UsersService) {}

  async createForUser(userId: string) {
    const user = await this.usersService.findByIdWithRolesAndPermissions(userId);
    const { can, build } = new AbilityBuilder<AppAbility>(PureAbility);

    // If user has no role, they can't do anything
    if (!user.roles) {
      return build();
    }

    // Get all permissions from the user's role
    const permissions = user.roles.flatMap(role => role.rolePermissions);

    // Define abilities based on permissions
    permissions.forEach(permission => {
      const { resource, actions } = permission;
      actions.forEach(action => {
        can(action.toLowerCase(), resource);
      });
    });

    return build();
  }
}
