import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY, RequiredPermission } from './permissions.decorator';
import { CaslAbilityFactory } from './casl-ability.factory';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.get<RequiredPermission>(
      PERMISSIONS_KEY,
      context.getHandler(),
    );

    if (!requiredPermission) return true;

    const { user } = context.switchToHttp().getRequest();
    // user may be an object with a 'user' property or just the ID
    const userId = typeof user === 'object' && user.user ? user.user : user;
    const ability = await this.caslAbilityFactory.createForUser(userId);
    // Attach ability to request for reuse in controllers/services
    context.switchToHttp().getRequest().ability = ability;

    const normalizedAction = requiredPermission.action.toLowerCase();
    const hasPermission = ability.can(
      normalizedAction,
      requiredPermission.resource,
    );

    if (!hasPermission) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    return true;
  }
}
