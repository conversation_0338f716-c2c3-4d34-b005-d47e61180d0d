import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VendorsService } from './vendors.service';
import { VendorsController } from './vendors.controller';
import { Vendor } from './entities/vendor.entity';
import { VendorSupportedSku } from './entities/vendor-supported-sku.entity';
import { VendorApiDetail } from './entities/vendor-api-detail.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';
import { PermissionsModule } from 'src/permissions/permissions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Vendor,
      VendorSupportedSku,
      VendorApiDetail,
      ProductSku,
    ]),
    forwardRef(() => PermissionsModule),
  ],
  controllers: [VendorsController],
  providers: [VendorsService],
})
export class VendorsModule {}
