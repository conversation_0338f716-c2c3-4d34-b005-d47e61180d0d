import { OmitType, PartialType } from '@nestjs/swagger';
import { CreateVendorDto } from './create-vendor.dto';
import { CreateVendorSupportedSkuDto } from './create-vendor-supported-sku.dto';
import { CreateVendorApiDetailDto } from './create-vendor-api-detail.dto';
import { IsOptional, IsArray, ValidateNested, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateVendorSupportedSkuDto extends PartialType(
  CreateVendorSupportedSkuDto,
) {
  @IsOptional()
  @IsUUID()
  id?: string;
}

export class UpdateVendorApiDetailDto extends PartialType(
  CreateVendorApiDetailDto,
) {
  @IsOptional()
  @IsUUID()
  id?: string;
}

export class UpdateVendorDto extends PartialType(
  OmitType(CreateVendorDto, [
    'vendorSupportedSkus',
    'vendorApiDetails',
  ] as const),
) {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateVendorSupportedSkuDto)
  vendorSupportedSkus?: UpdateVendorSupportedSkuDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateVendorApiDetailDto)
  vendorApiDetails?: UpdateVendorApiDetailDto[];
}
