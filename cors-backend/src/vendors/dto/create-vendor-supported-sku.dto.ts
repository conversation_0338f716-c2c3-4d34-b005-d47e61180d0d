import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsBoolean, IsNumber } from 'class-validator';

export class CreateVendorSupportedSkuDto {
  @ApiProperty({ example: 'uuid-sku' })
  @IsUUID()
  supportedProductSkuId: string;

  @ApiProperty({ example: 3 })
  @IsNumber()
  productionTimeDays: number;

  @ApiProperty({ example: true })
  @IsBoolean()
  handlePeakSeason: boolean;

  @ApiProperty({ example: 5 })
  @IsNumber()
  peakProductionTimeDays: number;

  @ApiProperty({ example: 100 })
  @IsNumber()
  maxCapacityPerDay: number;

  @ApiProperty({ example: 12.99 })
  @IsNumber()
  cost: number;

  @ApiProperty({ example: 5.0 })
  @IsNumber()
  rushCost: number;

  @ApiProperty({ example: 3.5 })
  @IsNumber()
  shippingPerOrder: number;
}
