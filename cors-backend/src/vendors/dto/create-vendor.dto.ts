import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsUUID,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateVendorSupportedSkuDto } from './create-vendor-supported-sku.dto';
import { CreateVendorApiDetailDto } from './create-vendor-api-detail.dto';

export class CreateVendorDto {
  @ApiProperty({ example: 'VND-001' })
  @IsString()
  sku: string;

  @ApiProperty({ example: 'Awesome Vendor Co.' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'manufacturer' })
  @IsString()
  type: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  email: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  contactName: string;

  @ApiProperty({ example: '+1234567890' })
  @IsString()
  phoneNumber: string;

  @ApiProperty({ example: { street: '123 Main St', city: 'NYC' } })
  @IsObject()
  address: any;

  @ApiProperty({ example: 'USA' })
  @IsString()
  country: string;

  @ApiProperty({ example: 'America/New_York' })
  @IsString()
  timeZone: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ example: 'round_robin' })
  @IsString()
  primaryAssignmentRule: string;

  @ApiProperty({ example: 'least_cost' })
  @IsString()
  secondaryAssignmentRule: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  capacityBasedAssignment: boolean;

  @ApiProperty({ example: 'uuid-backup-vendor', required: false })
  @IsUUID()
  @IsOptional()
  backupVendorId?: string;

  @ApiProperty({ example: false })
  @IsBoolean()
  canManualOverride: boolean;

  @ApiProperty({ example: 'Net 30' })
  @IsString()
  paymentTerms: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  canDropship: boolean;

  @ApiProperty({ example: 'FedEx' })
  @IsString()
  shippingCarrier: string;

  @ApiProperty({ example: 5 })
  @IsNumber()
  transitTimeDays: number;

  @ApiProperty({ example: true })
  @IsBoolean()
  requireTrackingNumber: boolean;

  @ApiProperty({ example: 'Store001' })
  @IsString()
  shipstationStore: string;

  @ApiProperty({ example: 'ground, express' })
  @IsString()
  shippingMethodOptions: string;

  @ApiProperty({ example: 0.98 })
  @IsNumber()
  fulfillmentRate: number;

  @ApiProperty({ example: 0.02 })
  @IsNumber()
  defectRate: number;

  @ApiProperty({ example: 1 })
  @IsNumber()
  performanceGap: number;

  @ApiProperty({ type: [CreateVendorSupportedSkuDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVendorSupportedSkuDto)
  vendorSupportedSkus: CreateVendorSupportedSkuDto[];

  @ApiProperty({ type: [CreateVendorApiDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVendorApiDetailDto)
  vendorApiDetails: CreateVendorApiDetailDto[];
}
