import {
  IsArray,
  IsInt,
  IsOptional,
  IsString,
  ValidateNested,
  IsPositive,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsStringBooleanNumber } from 'src/utils/validation.decorator';
import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

function IsArrayOfArraysContainingObjects(
  objectType: any,
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'IsArrayOfArraysContainingObjects',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!Array.isArray(value)) {
            return false;
          }

          return value.every(item => {
            return (
              Array.isArray(item) &&
              item.every(
                subItem =>
                  typeof subItem === 'object' &&
                  subItem.hasOwnProperty('attribute') &&
                  subItem.hasOwnProperty('operator') &&
                  subItem.hasOwnProperty('value'),
              )
            );
          });
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} should be an array of arrays, with each inner array containing valid FilterItem objects`;
        },
      },
    });
  };
}

class FilterItem {
  @IsString()
  attribute: string;

  @IsString()
  operator: string;

  @IsStringBooleanNumber({
    message: 'Value must be a string, boolean, or number',
  })
  value: string | boolean | number | string[];
}

export class VendorAdvancedFilterDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterItem)
  @IsArrayOfArraysContainingObjects(FilterItem, {
    message:
      'Filters should be an array of arrays, with each inner array containing valid FilterItem objects',
  })
  filters: FilterItem[][];

  @IsOptional()
  @IsInt()
  @IsPositive()
  page: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  limit: number;
} 