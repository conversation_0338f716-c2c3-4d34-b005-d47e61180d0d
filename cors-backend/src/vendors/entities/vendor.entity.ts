import { <PERSON>tity, Column, OneToMany, Index } from 'typeorm';
import { VendorSupportedSku } from './vendor-supported-sku.entity';
import { VendorApiDetail } from './vendor-api-detail.entity';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';

@Entity('vendors')
export class Vendor extends BaseEntity {
  @Column({ unique: true })
  @Index()
  @ApiProperty({ description: 'SKU of the vendor', example: 'VND-001' })
  sku: string;

  @Column()
  @ApiProperty({
    description: 'Name of the vendor',
    example: 'Awesome Vendor Co.',
  })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Type of vendor', example: 'manufacturer' })
  type: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Vendor email', example: '<EMAIL>' })
  email: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Vendor contact name', example: '<PERSON>' })
  contactName: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Vendor phone number', example: '+1234567890' })
  phoneNumber: string;

  @Column('json', { nullable: true })
  @ApiProperty({
    description: 'Vendor address as JSON',
    example: { street: '123 Main St', city: 'NYC' },
  })
  address: any;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Country of the vendor', example: 'USA' })
  country: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Vendor timezone', example: 'America/New_York' })
  timeZone: string;

  @Column({ default: true })
  @ApiProperty({ description: 'Whether the vendor is active', example: true })
  isActive: boolean;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Primary assignment rule',
    example: 'round_robin',
  })
  primaryAssignmentRule: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Secondary assignment rule',
    example: 'least_cost',
  })
  secondaryAssignmentRule: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Capacity-based assignment enabled',
    example: true,
  })
  capacityBasedAssignment: boolean;

  @Column({ type: 'uuid', nullable: true })
  @ApiProperty({
    description: 'Backup vendor ID',
    example: 'uuid-5678',
    required: false,
  })
  backupVendorId: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Allow manual override of vendor',
    example: false,
  })
  canManualOverride: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Payment terms', example: 'Net 30' })
  paymentTerms: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Whether vendor supports dropshipping',
    example: true,
  })
  canDropship: boolean;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Preferred shipping carrier', example: 'FedEx' })
  shippingCarrier: string;

  @Column('bigint', { nullable: true })
  @ApiProperty({ description: 'Transit time in days', example: 5 })
  transitTimeDays: number;

  @Column({ nullable: true })
  @ApiProperty({ description: 'Tracking number required', example: true })
  requireTrackingNumber: boolean;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Associated ShipStation store',
    example: 'Store001',
  })
  shipstationStore: string;

  @Column({ nullable: true })
  @ApiProperty({
    description: 'Shipping method options',
    example: 'ground, express',
  })
  shippingMethodOptions: string;

  @Column('decimal', { nullable: true, precision: 5, scale: 2 })
  @ApiProperty({ description: 'Fulfillment rate as decimal', example: 0.98 })
  fulfillmentRate: number;

  @Column('decimal', { nullable: true, precision: 5, scale: 2 })
  @ApiProperty({ description: 'Defect rate as decimal', example: 0.02 })
  defectRate: number;

  @Column('decimal', { nullable: true, precision: 10, scale: 2 })
  @ApiProperty({ description: 'Performance gap in days', example: 1.5 })
  performanceGap: number;

  @Column({ nullable: true, default: false })
  @ApiProperty({ description: 'Whether this vendor has priority for certain SKUs', example: false })
  preferredSkusOverCompetitors: boolean;

  @Column({ nullable: true, default: '3,5,7,14,28,42' })
  @ApiProperty({ description: 'Available rush production options in days', example: '3,5,7,14,28,42' })
  rushProductionOptions: string;

  @OneToMany(() => ProductSku, productSku => productSku.primaryVendor)
  productSku: ProductSku[];

  @OneToMany(() => VendorSupportedSku, sku => sku.vendor)
  supportedSkus: VendorSupportedSku[];

  @OneToMany(() => VendorApiDetail, api => api.vendor)
  apiDetails: VendorApiDetail[];
}
