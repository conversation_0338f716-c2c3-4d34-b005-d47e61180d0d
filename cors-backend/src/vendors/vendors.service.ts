import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Vendor } from './entities/vendor.entity';
import { CreateVendorDto } from './dto/create-vendor.dto';
import { UpdateVendorDto } from './dto/update-vendor.dto';
import { BaseService } from 'src/common/base.service';
import { DBHelper } from 'src/helpers/db.helpers';
import { VendorSupportedSku } from './entities/vendor-supported-sku.entity';
import { VendorApiDetail } from './entities/vendor-api-detail.entity';
import { ProductSku } from 'src/product-sku/entities/product-sku.entity';

@Injectable()
export class VendorsService extends BaseService<Vendor> {
  constructor(
    @InjectRepository(Vendor)
    private readonly vendorsRepository: Repository<Vendor>,
    @InjectRepository(VendorSupportedSku)
    private readonly vendorSupportedSkuRepository: Repository<VendorSupportedSku>,
    @InjectRepository(VendorApiDetail)
    private readonly vendorApiDetailRepository: Repository<VendorApiDetail>,
    @InjectRepository(ProductSku)
    private readonly productSkuRepository: Repository<ProductSku>,
  ) {
    super(vendorsRepository);
  }

  async createVendor(createVendorDto: CreateVendorDto): Promise<Vendor> {
    try {
      const { vendorSupportedSkus, vendorApiDetails, ...vendorData } =
        createVendorDto;

      const vendor = this.vendorsRepository.create(vendorData);
      const savedVendor = await this.vendorsRepository.save(vendor);

      if (vendorSupportedSkus && vendorSupportedSkus.length > 0) {
        const productSkuIds = vendorSupportedSkus.map(
          sku => sku.supportedProductSkuId,
        );
        const productSkus = await DBHelper.findMany(this.productSkuRepository, {
          where: { id: In(productSkuIds) },
        });
        DBHelper.validateEntityIds(
          productSkus,
          productSkuIds,
          sku => sku.id,
          'Product SKU',
        );

        const supportedSkus = vendorSupportedSkus.map(sku =>
          this.vendorSupportedSkuRepository.create({
            ...sku,
            vendor: savedVendor,
          }),
        );
        await this.vendorSupportedSkuRepository.save(supportedSkus);
      }

      if (vendorApiDetails && vendorApiDetails.length > 0) {
        const apiDetails = vendorApiDetails.map(detail =>
          this.vendorApiDetailRepository.create({
            ...detail,
            vendor: savedVendor,
          }),
        );
        await this.vendorApiDetailRepository.save(apiDetails);
      }

      return await DBHelper.findByIdOrThrow(
        this.vendorsRepository,
        savedVendor.id,
        ['supportedSkus', 'apiDetails'],
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to create vendor: ${error.message || 'Unknown error occurred'}`,
      );
    }
  }

  async updateVendor(
    id: string,
    updateVendorDto: UpdateVendorDto,
  ): Promise<Vendor> {
    try {
      const { vendorSupportedSkus, vendorApiDetails, ...vendorData } =
        updateVendorDto;

      const vendor = await DBHelper.findByIdOrThrow(
        this.vendorsRepository,
        id,
        ['supportedSkus', 'apiDetails'],
      );
      Object.assign(vendor, vendorData);
      const savedVendor = await this.vendorsRepository.save(vendor);

      if (vendorSupportedSkus) {
        const existingSkus = vendor.supportedSkus || [];

        const existingSkuMap = new Map(existingSkus.map(sku => [sku.id, sku]));

        for (const skuData of vendorSupportedSkus) {
          if (skuData.id && existingSkuMap.has(skuData.id)) {
            const existingSku = existingSkuMap.get(skuData.id);
            if (existingSku) {
              Object.assign(existingSku, skuData);
              await this.vendorSupportedSkuRepository.save(existingSku);
              existingSkuMap.delete(skuData.id);
            }
          } else {
            if (!skuData.supportedProductSkuId) {
              throw new Error('supportedProductSkuId is required for new SKUs');
            }
            await DBHelper.findByIdOrThrow(
              this.productSkuRepository,
              skuData.supportedProductSkuId,
            );
            const newSku = this.vendorSupportedSkuRepository.create({
              ...skuData,
              vendor: savedVendor,
            });
            await this.vendorSupportedSkuRepository.save(newSku);
          }
        }

        for (const [id, sku] of existingSkuMap) {
          if (sku) {
            await this.vendorSupportedSkuRepository.remove(sku);
          }
        }
      }

      if (vendorApiDetails) {
        const existingApiDetails = vendor.apiDetails || [];

        const existingApiDetailMap = new Map(
          existingApiDetails.map(detail => [detail.id, detail]),
        );

        for (const apiDetailData of vendorApiDetails) {
          if (apiDetailData.id && existingApiDetailMap.has(apiDetailData.id)) {
            const existingApiDetail = existingApiDetailMap.get(
              apiDetailData.id,
            );
            if (existingApiDetail) {
              Object.assign(existingApiDetail, apiDetailData);
              await this.vendorApiDetailRepository.save(existingApiDetail);
              existingApiDetailMap.delete(apiDetailData.id);
            }
          } else {
            const newApiDetail = this.vendorApiDetailRepository.create({
              ...apiDetailData,
              vendor: savedVendor,
            });
            await this.vendorApiDetailRepository.save(newApiDetail);
          }
        }

        for (const [id, apiDetail] of existingApiDetailMap) {
          if (apiDetail) {
            await this.vendorApiDetailRepository.remove(apiDetail);
          }
        }
      }

      return await DBHelper.findByIdOrThrow(
        this.vendorsRepository,
        savedVendor.id,
        ['supportedSkus', 'apiDetails'],
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to update vendor: ${error.message || 'Unknown error occurred'}`,
      );
    }
  }
}
