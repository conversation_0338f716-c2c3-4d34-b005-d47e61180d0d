export const BREEDS = (species: string): string[] => {
  const breedMap = {
    "dog": [
      "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      "Afghan Hound",
      "Airedale", 
      "Aki<PERSON>",
      "Alaskan Malamute",
      "American Bulldog",
      "American English Coonhound",
      "American Eskimo Dog",
      "American Foxhound",
      "American Hairless Terrier",
      "American Staffordshire Terrier",
      "American Water Spaniel",
      "Anatolian Shepherd Dog",
      "Appenzeller Sennenhund",
      "Aussiedoodle",
      "Australian Cattle Dog",
      "Australian Shepherd",
      "Australian Terrier",
      "Basenji",
      "Basset Hound",
      "Beagle",
      "Beaglier",
      "Bearded Collie",
      "Beauceron",
      "Bedlington Terrier",
      "Belgian Malinois",
      "Belgian Sheepdog",
      "Belgian Tervuren",
      "Berger Blanc Suisse",
      "Bernese Mountain Dog",
      "Bichon Frise",
      "Bichpoo",
      "Black and Tan Coonhound",
      "Black Mouthed Cur",
      "Black Russian Terrier",
      "Bloodhound",
      "Bluetick Coonhound",
      "Bolonka",
      "Borador",
      "Border Collie",
      "Border Terrier",
      "Bordoodle",
      "<PERSON>rzoi",
      "Boston Terrier",
      "Bouvier des Flandres",
      "Boxer",
      "Boykin Spaniel",
      "Briard",
      "Brittany",
      "Brussels Griffon",
      "Bulldog",
      "Bullmastiff",
      "Bull Terrier",
      "Cairn Terrier",
      "Canaan Dog",
      "Cane Corso",
      "Cardigan Welsh Corgi",
      "Carolina Dog",
      "Catahoula Leopard Dog",
      "Caucasian Shepherd",
      "Cavachon",
      "Cavalier King Charles Spaniel",
      "Cavapoo",
      "Cesky Terrier",
      "Cheeks",
      "Chesapeake Bay Retriever",
      "Chihuahua",
      "Chinese Crested Dog",
      "Chinese Shar-Pei",
      "Chinook",
      "Chion",
      "Chiweenie",
      "Chorkie",
      "Chow Chow",
      "Clumber Spaniel",
      "Cockapoo",
      "Cocker Spaniel",
      "Collie",
      "Coton de Tulear",
      "Curly-Coated Retriever",
      "Czechoslovakian Wolfdog",
      "Dachshund",
      "Dalmatian",
      "Dandie Dinmont Terrier",
      "Doberman Pinscher",
      "Dogo Argentino",
      "Dogue de Bordeaux",
      "Doxiepoo",
      "Dutch Shepherd",
      "English Bulldog",
      "English Cocker Spaniel",
      "English Foxhound",
      "English Pointer",
      "English Setter",
      "English Springer Spaniel",
      "English Toy Spaniel",
      "Entlebucher Mountain Dog",
      "Eurasier",
      "Field Spaniel",
      "Finnish Lapphund",
      "Finnish Spitz",
      "Flat-Coated Retriever",
      "French Bulldog",
      "German Pinscher",
      "German Shepherd",
      "German Shorthaired Pointer",
      "German Wirehaired Pointer",
      "Giant Schnauzer",
      "Glen of Imaal Terrier",
      "Goldendoodle",
      "Golden Retriever",
      "Gordon Setter",
      "Great Dane",
      "Greater Swiss Mountain Dog",
      "Great Pyrenees",
      "Greyhound",
      "Griffon Bruxellois",
      "Groenendael",
      "Harrier",
      "Havanese",
      "Hovawart",
      "Ibizan Hound",
      "Icelandic Sheepdog",
      "Irish Red and White Setter",
      "Irish Setter",
      "Irish Terrier",
      "Irish Water Spaniel",
      "Irish Wolfhound",
      "Italian Greyhound",
      "Jack Russell Terrier",
      "Japanese Chin",
      "Kai Ken",
      "Kangal",
      "Keeshonden",
      "Kerry Blue Terrier",
      "Komondorok",
      "Kooikerhondje",
      "Kuvaszok",
      "Labradoodle",
      "Labrador Retriever",
      "Lagotto Romagnolo",
      "Lakeland Terrier",
      "Leonberger",
      "Lhasa Apso",
      "Lowchen",
      "Mal-shi",
      "Maltese",
      "Maltipoo",
      "Manchester Terrier",
      "Mastiff",
      "Miniature Bull Terrier",
      "Miniature Pinscher",
      "Miniature Schnauzer",
      "Morkie",
      "Mountain Cur",
      "Münsterländer",
      "Neapolitan Mastiff",
      "Newfoundland",
      "Norfolk Terrier",
      "Norwegian Buhund",
      "Norwegian Elkhound",
      "Norwegian Lundehund",
      "Norwich Terrier",
      "Nova Scotia Duck Tolling Retriever",
      "Old English Sheepdog",
      "Other/Mixed",
      "Otterhound",
      "Papillon",
      "Parson Russell Terrier"
    ],
    "cat": [
      "Abyssinian",
      "American Bobtail",
      "American Curl",
      "American Wirehair",
      "Balinese",
      "Balinese-Javanese",
      "Bengal",
      "Birman",
      "Bombay",
      "British Shorthair",
      "Burmese",
      "Burmilla",
      "Chartreux",
      "Chinese Li Hua",
      "Color Point Shorthair",
      "Cornish Rex",
      "Devon Rex",
      "Domestic Long Hair",
      "Domestic Short Hair",
      "Egyptian Mau",
      "European Burmese",
      "Exotic",
      "Havana Brown",
      "Himalayan",
      "Japanese Bobtail",
      "Korat",
      "LaPerm",
      "Maine Coon",
      "Manx",
      "Munchkin",
      "Nebelung",
      "Norwegian Forest Cat",
      "Ocicat",
      "Oriental",
      "Other/Mixed",
      "Pastel Calico",
      "Persian",
      "Ragamuffin",
      "Ragdoll",
      "Russian Blue",
      "Scottish Fold",
      "Selkirk Rex",
      "Siamese",
      "Siberian",
      "Singapura",
      "Somali",
      "Sphynx",
      "Tonkinese",
      "Tortoiseshell",
      "Turkish Angora",
      "Turkish Van"
    ],
    "horse": [
      "Abyssinian",
      "Akhal-Teke",
      "Albanian",
      "Albino",
      "American Paint Horse",
      "American Quarter Horse",
      "American Saddlebred",
      "Andalusian",
      "Anglo-Kabarda",
      "Appaloosa",
      "Araappaloosa",
      "Arabian",
      "Argentine Criollo",
      "Barb",
      "Belgian",
      "Buckskin",
      "Camargue",
      "Caspian",
      "Clydesdale",
      "Connemara Pony",
      "Dales Pony",
      "Dartmoor Pony",
      "Deliboz",
      "Donkey",
      "Dutch Warmblood",
      "Eriskay Pony",
      "Falabella",
      "Fell Pony",
      "Finnhorse",
      "Frederiksborg",
      "French Trotter",
      "Friesian",
      "Gidran",
      "Gotland",
      "Groningen",
      "Hackney",
      "Haflinger",
      "Hanoverian",
      "Hequ",
      "Highland Pony",
      "Icelandic Horse",
      "Iomud",
      "Jinzhou",
      "Jutland",
      "Kabarda",
      "Karabair",
      "Kathiawari",
      "Kazakh",
      "Kerry Bog Pony",
      "Kiger Mustang",
      "Kiso",
      "Kladruby",
      "Knabstrup",
      "Lipizzan",
      "Lusitano",
      "Marwari",
      "Miniature Horse",
      "Missouri Fox Trotter",
      "Morab",
      "Morgan",
      "Mule",
      "Mustang",
      "National Show Horse",
      "New Forest Pony",
      "Newfoundland Pony",
      "Norwegian Fjord",
      "Other/Mixed",
      "Paso Fino",
      "Percheron",
      "Pinto",
      "Pony of the Americas",
      "Shetland Pony",
      "Shire",
      "Standardbred",
      "Tennessee Walking Horse",
      "Thoroughbred",
      "Trakehner",
      "Welsh Pony and Cob"
    ],
    "other": [
      "Alpaca",
      "Chicken",
      "Chinchilla",
      "Cockatiel",
      "Cockatoo",
      "Cow",
      "Deer",
      "Duck",
      "Ferret",
      "Fish",
      "Fox",
      "Gerbil",
      "Goat",
      "Guinea Pig",
      "Hamster",
      "Hedgehog",
      "Kangaroo",
      "Lizard",
      "Micro Pig",
      "Monkey",
      "Mouse",
      "Opossum",
      "Other/Mixed",
      "Owl",
      "Parakeet",
      "Parrot",
      "Pig",
      "Prairie Dog",
      "Rabbit",
      "Raccoon",
      "Rat",
      "Sheep",
      "Skunk",
      "Sloth",
      "Snake",
      "Spider",
      "Squirrel",
      "Sugar Glider",
      "Turtle",
      "Wolf"
    ]
  };
  return breedMap[species] || [];
};