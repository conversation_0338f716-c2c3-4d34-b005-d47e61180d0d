import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Request } from 'express';
import { accessEnv } from '../../env.validation';
import * as crypto from 'crypto';

@Injectable()
export class FixedTokenGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('Authorization header is required');
    }

    if (!authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Invalid authorization header format');
    }

    const token = authHeader.substring(7);
    const fixedSecret = accessEnv('JWT_FIXED_SECRET');

    if (!fixedSecret) {
      throw new UnauthorizedException('Customer access token secret not configured');
    }

    try {
      // Verify the token format and signature
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new UnauthorizedException('Invalid token format');
      }

      const [headerB64, payloadB64, signature] = parts;
      
      // Verify signature
      const expectedSignature = crypto
        .createHmac('sha256', fixedSecret)
        .update(`${headerB64}.${payloadB64}`)
        .digest('base64')
        .replace(/=/g, '');

      if (signature !== expectedSignature) {
        throw new UnauthorizedException('Invalid token signature');
      }

      // Decode and verify payload
      const payload = JSON.parse(Buffer.from(payloadB64, 'base64').toString());
      
      // Check audience
      if (payload.aud !== 'customer-access') {
        throw new UnauthorizedException('Invalid token audience');
      }

      // Check expiration
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        throw new UnauthorizedException('Token has expired');
      }

      // Add the payload to the request for potential future use
      request['customer'] = payload;
      
      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Token verification failed');
    }
  }
} 