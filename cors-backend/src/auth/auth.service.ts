import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { DBHelper } from 'src/helpers/db.helpers';
import { EmailUtil } from '../utils/email.util';
import { accessEnv } from '../env.validation';

type UserWithoutPassword = Omit<User, 'password'>;

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private usersRepo: Repository<User>,
    private jwtService: JwtService,
    private emailUtil: EmailUtil,
  ) {}

  async validateUser(
    email: string,
    password: string,
  ): Promise<UserWithoutPassword> {
    const user = await <PERSON>H<PERSON>per.findOne(this.usersRepo, {
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        firstName: true,
        lastName: true,
        isActive: true,
      },
      relations: ['roles'],
    });

    if (!user) throw new UnauthorizedException('Invalid Credentials');

    const isMatch = await bcrypt.compare(password, user.password);

    if (!isMatch) throw new UnauthorizedException('Invalid Credentials');

    if (!user.isActive) throw new UnauthorizedException('User is inactive');

    const { password: _, ...result } = user;

    // Filter out inactive roles
    result.roles = result.roles.filter(role => role.isActive);

    return result;
  }

  async login(user: User) {
    const payload = { sub: user.id, email: user.email, roles: user.roles };
    return {
      access_token: this.jwtService.sign(payload),
      refresh_token: this.jwtService.sign(payload, {
        expiresIn: accessEnv('JWT_REFRESH_EXPIRES_IN'),
      }),
    };
  }

  async refreshToken(user: any) {
    const currentUser = await DBHelper.findOne(this.usersRepo, {
      where: { id: user.user },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
      },
      relations: ['roles'],
    });

    if (!currentUser || !currentUser.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Filter out inactive roles
    const activeRoles = currentUser.roles.filter(role => role.isActive);

    const payload = {
      sub: currentUser.id,
      email: currentUser.email,
      roles: activeRoles,
    };

    return {
      access_token: this.jwtService.sign(payload),
    };
  }

  async generateResetToken(email: string): Promise<void> {
    const user = await DBHelper.findOne(this.usersRepo, {
      where: { email },
      select: {
        id: true,
        email: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      return;
    }

    const payload = {
      sub: user.id,
      email: user.email,
      type: 'password_reset',
    };

    const resetToken = this.jwtService.sign(payload, {
      expiresIn: accessEnv('RESET_ACCESS_EXPIRES_IN'),
    });

    await this.emailUtil.sendPasswordResetEmail(user.email, resetToken);
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      const payload = this.jwtService.verify(token);

      if (payload.type !== 'password_reset') {
        throw new BadRequestException('Invalid token type');
      }

      const user = await DBHelper.findOne(this.usersRepo, {
        where: { id: payload.sub },
        select: {
          id: true,
          email: true,
          isActive: true,
        },
      });

      if (!user || !user.isActive) {
        return;
      }

      const hashedPassword = await bcrypt.hash(newPassword, 10);

      await this.usersRepo.update(user.id, {
        password: hashedPassword,
      });
    } catch (error) {
      if (
        error.name === 'JsonWebTokenError' ||
        error.name === 'TokenExpiredError'
      ) {
        throw new BadRequestException('Invalid or expired token');
      }
      throw error;
    }
  }
}
