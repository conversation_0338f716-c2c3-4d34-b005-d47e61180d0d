import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { accessEnv } from '../../env.validation';

export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: accessEnv('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    return {
      user: payload.sub,
      email: payload.email,
      roles: payload.roles.filter(role => role.isActive === true),
    };
  }
}
