import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ArtworkTypesService } from './artwork-types.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  AddArtworkTypeDto,
  ArtworkTypeResponseDto,
} from './dto/artwork-type.dto';

@UseGuards(JwtGuard)
@ApiBearerAuth()
@ApiTags('Artwork Types')
@Controller('artwork-types')
export class ArtworkTypesController {
  constructor(private readonly artworkTypeService: ArtworkTypesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new artwork type' })
  @ApiBody({
    type: AddArtworkTypeDto,
    description: 'Data for creating a new artwork type',
    examples: {
      vector: {
        summary: 'Vector Artwork Type',
        value: {
          name: 'Vector',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Artwork type created successfully',
    type: ArtworkTypeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  async create(@Body() dto: AddArtworkTypeDto) {
    const result = await this.artworkTypeService.insert(dto);
    return { statusCode: HttpStatus.CREATED, data: result };
  }

  @Get()
  @ApiOperation({ summary: 'Get all artwork types' })
  @ApiResponse({
    status: 200,
    description: 'List of artwork types retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: {
          type: 'number',
          example: 200,
        },
        data: {
          type: 'array',
          items: {
            $ref: '#/components/schemas/ArtworkTypeResponseDto',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'name:like:line',
  })
  async findAll(@Query('q') q?: string) {
    return await this.artworkTypeService.findAll({
      q,
    });
  }
}
