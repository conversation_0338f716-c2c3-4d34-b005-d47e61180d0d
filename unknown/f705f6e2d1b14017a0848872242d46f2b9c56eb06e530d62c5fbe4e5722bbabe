import { MigrationInterface, QueryRunner } from "typeorm";

export class ChangeParentChildSkuAssociation1746704754960 implements MigrationInterface {
  name = 'ChangeParentChildSkuAssociation1746704754960'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_8d8a7a58e45866b9f30336ab0f6"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_5c5f06898204ed602c273db7b47"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_5fa6c0a83b25414b8dde8100e6a"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP COLUMN "parentSkuId"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP COLUMN "childSkuId"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP COLUMN "upSellParentSkuId"`);
    await queryRunner.query(`CREATE TYPE "public"."sku_relationships_associated_sku_type_enum" AS ENUM('parent', 'child', 'upsell')`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD "associated_sku_type" "public"."sku_relationships_associated_sku_type_enum" NOT NULL`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD "sku_id" uuid`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD "associated_sku_id" uuid`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_bd5de2e2ea94f55bbb379a0aee4" FOREIGN KEY ("sku_id") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_36abbccaf410a4358018a90e301" FOREIGN KEY ("associated_sku_id") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_36abbccaf410a4358018a90e301"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP CONSTRAINT "FK_bd5de2e2ea94f55bbb379a0aee4"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP COLUMN "associated_sku_id"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP COLUMN "sku_id"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" DROP COLUMN "associated_sku_type"`);
    await queryRunner.query(`DROP TYPE "public"."sku_relationships_associated_sku_type_enum"`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD "upSellParentSkuId" uuid`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD "childSkuId" uuid`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD "parentSkuId" uuid`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_5fa6c0a83b25414b8dde8100e6a" FOREIGN KEY ("upSellParentSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_5c5f06898204ed602c273db7b47" FOREIGN KEY ("childSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "sku_relationships" ADD CONSTRAINT "FK_8d8a7a58e45866b9f30336ab0f6" FOREIGN KEY ("parentSkuId") REFERENCES "product_sku"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }
}
