'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid2' // using MUI Grid v2
import Divider from '@mui/material/Divider'
import Chip from '@mui/material/Chip'
import CircularProgress from '@mui/material/CircularProgress'
import Backdrop from '@mui/material/Backdrop'
import CustomAvatar from '@core/components/mui/Avatar'
import { getInitials } from '@/utils/getInitials'
import { RoleProtected } from '@/components/ProtectedRoleWrapper'
import { Actions, ActionsTarget } from '@/libs/casl/ability'

const UserViewForm = ({
  userData,
  roleData
}: {
  userData: { id: string; firstName: string; lastName: string; email: string; isActive: boolean }
  roleData?: { name: string }
}) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleBack = () => {
    setLoading(true)
    router.push('/users')
  }

  const handleEdit = () => {
    setLoading(true)
    router.push(`/users/edit/${userData.id}`)
  }

  if (!userData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <>
      <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>

      <Card>
        <CardContent>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 4,
            }}
          >
            <Typography variant="h5">View User Details</Typography>
            <Box sx={{ display: "flex", gap: 2 }}>
              <RoleProtected
                action={Actions.EditUser}
                actionTarget={ActionsTarget.UserManagment}
              >
                <Button variant="contained" onClick={handleEdit}>
                  Edit User
                </Button>
              </RoleProtected>
              <Button variant="outlined" onClick={handleBack}>
                Back to Users
              </Button>
            </Box>
          </Box>

          <Divider sx={{ mb: 4 }} />

          <Grid container spacing={4}>
            <Grid>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <CustomAvatar
                  skin="light"
                  sx={{ width: 80, height: 80, mr: 3, fontSize: "2rem" }}
                >
                  {getInitials(`${userData.firstName} ${userData.lastName}`)}
                </CustomAvatar>
                <Box>
                  <Typography variant="h6">{`${userData.firstName} ${userData.lastName}`}</Typography>
                  <Typography variant="body2">{userData.email}</Typography>
                </Box>
              </Box>
            </Grid>

            <Grid>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                User Information
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", flexWrap: "wrap" }}>
                  <Typography sx={{ mr: 2, fontWeight: 500 }}>
                    Full Name:
                  </Typography>
                  <Typography>{`${userData.firstName} ${userData.lastName}`}</Typography>
                </Box>
                <Box sx={{ display: "flex", flexWrap: "wrap" }}>
                  <Typography sx={{ mr: 2, fontWeight: 500 }}>
                    Email:
                  </Typography>
                  <Typography>{userData.email}</Typography>
                </Box>
                <Box sx={{ display: "flex", flexWrap: "wrap" }}>
                  <Typography sx={{ mr: 2, fontWeight: 500 }}>Role:</Typography>
                  <Typography>
                    {roleData?.name || "No role assigned"}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", flexWrap: "wrap" }}>
                  <Typography sx={{ mr: 2, fontWeight: 500 }}>
                    Status:
                  </Typography>
                  <Chip
                    label={userData.isActive ? "Active" : "Inactive"}
                    color={userData.isActive ? "success" : "error"}
                    size="small"
                    variant="tonal"
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </>
  );
}

export default UserViewForm
