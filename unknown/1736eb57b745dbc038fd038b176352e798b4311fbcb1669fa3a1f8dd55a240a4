import { Column, Entity, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from 'src/common/base.entity';
import { ProductSku } from '../../product-sku/entities/product-sku.entity';

@Entity({ name: 'artwork_types' })
export class ArtworkType extends BaseEntity {
  @Column()
  @ApiProperty({ description: 'Name of the artwork type', example: 'Vector' })
  name: string;

  @OneToMany(() => ProductSku, productSku => productSku.artworkType)
  @ApiProperty({
    description: 'List of product SKUs associated with this artwork type',
  })
  productSku: ProductSku[];
}
