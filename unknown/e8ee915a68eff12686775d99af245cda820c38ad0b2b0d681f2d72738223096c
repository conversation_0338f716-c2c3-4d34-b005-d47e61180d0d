"use client";

import Link from "next/link";
import { useParams } from "next/navigation";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import MaterioLogo from "@core/svg/Logo";
import classnames from "classnames";
import type { Mode } from "@core/types";
import { i18n, type Locale } from "@configs/i18n";
import Logo from "@components/layout/shared/Logo";
import Illustrations from "@components/Illustrations";
import { useImageVariant } from "@core/hooks/useImageVariant";
import { useSettings } from "@core/hooks/useSettings";
import { getLocalizedUrl } from "@/utils/i18n";
import { useState } from "react";

const ForgotPasswordV2 = ({ mode }: { mode: Mode }) => {
  const darkImg = "/images/pages/auth-v2-mask-dark.png";
  const lightImg = "/images/pages/auth-v2-mask-light.png";
  const darkIllustration = "/images/illustrations/characters/5.png";
  const lightIllustration = "/images/illustrations/characters/5.png";
  const borderedDarkIllustration =
    "/images/illustrations/auth/v2-forgot-password-dark-border.png";
  const borderedLightIllustration =
    "/images/illustrations/auth/v2-forgot-password-light-border.png";

  const { lang: locale } = useParams();
  const authBackground = useImageVariant(mode, lightImg, darkImg);
  const { settings } = useSettings();

  const characterIllustration = useImageVariant(
    mode,
    lightIllustration,
    darkIllustration,
    borderedLightIllustration,
    borderedDarkIllustration
  );
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = regex.test(email);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateEmail(email)) {
      setError("Please enter a valid email address");
      return;
    }
    setError("");

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || "Failed to send reset email");
      }

      setSuccess(true);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to send reset email"
      );
    }
  };

  return (
    <div className="flex bs-full justify-center">
      <div
        className={classnames(
          "flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden",
          {
            "border-ie": settings.skin === "bordered",
          }
        )}>
        <Link href="/" className="flex justify-center items-center mbe-6">
          <div style={{ transform: "scale(5)" }}>
            <MaterioLogo />
          </div>
        </Link>
        <Illustrations
          image1={{ src: "/images/illustrations/objects/tree-2.png" }}
          image2={null}
          maskImg={{ src: authBackground }}
        />
      </div>
      <div className="flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]">
        <Link
          href={getLocalizedUrl("/", (locale as Locale) || i18n.defaultLocale)}
          className="absolute block-start-5 sm:block-start-[38px] inline-start-6 sm:inline-start-[38px]">
          <Logo />
        </Link>
        <div className="flex flex-col gap-5 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset]">
          <div>
            <Typography variant="h4">Forgot Password 🔒</Typography>
            <Typography className="mbs-1">
              Enter your email and we&#39;ll send you instructions to reset your
              password
            </Typography>
          </div>
          {success ? (
            <div className="text-center">
              <Typography color="success.main" className="mbs-2">
                Reset instructions have been sent to your email!
              </Typography>
              <Link href="/login" className="text-primary">
                Return to Login
              </Link>
            </div>
          ) : (
            <form
              noValidate
              autoComplete="off"
              onSubmit={handleSubmit}
              className="flex flex-col gap-5">
              <TextField
                autoFocus
                fullWidth
                label="Email"
                value={email}
                onChange={(e) => {
                  const newEmail = e.target.value;
                  setEmail(newEmail);
                  if (newEmail && !validateEmail(newEmail)) {
                    setError("Please enter a valid email address");
                  } else {
                    setError("");
                  }
                }}
                error={!!error}
                helperText={error}
              />
              <Button fullWidth variant="contained" type="submit">
                Send reset link
              </Button>
              <Typography
                className="flex justify-center items-center"
                color="primary.main">
                <Link href="/login" className="flex items-center">
                  <i className="ri-arrow-left-s-line" />
                  <span>Back to Login</span>
                </Link>
              </Typography>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordV2;
