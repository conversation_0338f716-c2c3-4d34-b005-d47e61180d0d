import type { ThemeColor } from '@core/types'
import type { TextFieldProps } from '@mui/material/TextField'

export type UsersType = {
  id: string
  firstName: string
  lastName: string
  fullName?: string
  email: string
  isActive: boolean
  roles?: {
    id: string
    name: string
  }[]
  status: string
  avatar: string
  company: string
  country: string
  contact: string
  username: string
  currentPlan: string
  avatarColor?: ThemeColor
}

export type UserInfo = {
  name: string
  email: string
  avatar?: string
  role: {
    name: string
  }
}

export type FormValidateType = {
  fullName: string
  username: string
  email: string
  role: string
  plan: string
  status: string
}

export type FormNonValidateType = {
  company: string
  country: string
  contact: string
}

export type Role = {
  id: string
  name: string
  status: string
}

export type UserFormData = {
  id?: string
  firstName: string
  lastName: string
  email: string
  password: string
  roleId: string
  isActive: boolean
}
