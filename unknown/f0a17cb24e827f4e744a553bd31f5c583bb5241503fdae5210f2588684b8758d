import { Suspense } from 'react'
import apiClient from '@/utils/axios'
import UserAddForm from '@/views/users/add/UserAddForm'
import LoadingView from '@/components/LoadingView'
import { RolesData } from '@/types/roleTypes'

const UserEditData = async ({ params }: { params: { id: string } }) => {
  try {
    // Fetch user data
    const userResponse = await apiClient.get(`/users/${params.id}`)
    const userData = userResponse.data
    
    // Fetch roles data
    const rolesResponse = await apiClient.get('/roles')
    const rolesData = rolesResponse.data.data.filter((role : RolesData)=> role.name !== "Owner") || []
    
    // Prepare user data for the form
    const formattedUserData = {
      ...userData,
      id: params.id, // Ensure the ID is included
      // If user has roles, set roleId to the first role's id
      roleId: userData.roles && userData.roles.length > 0 ? userData.roles[0].id : ''
    }
    
    return <UserAddForm roles={rolesData} userData={formattedUserData} isEdit={true} />
  } catch (error) {
    console.error('Failed to fetch user or roles data:', error)
    return <div>Failed to load user data. Please try again later.</div>
  }
}

const UserEditPage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const resolvedParams = await params
  
  return (
    <Suspense fallback={<LoadingView />}>
      <UserEditData params={{ id: resolvedParams.id }} />
    </Suspense>
  )
}

export default UserEditPage
