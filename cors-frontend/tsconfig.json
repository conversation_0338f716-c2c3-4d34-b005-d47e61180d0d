{"compilerOptions": {"target": "ESNext", "downlevelIteration": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@core/*": ["./src/@core/*"], "@layouts/*": ["./src/@layouts/*"], "@menu/*": ["./src/@menu/*"], "@assets/*": ["./src/assets/*"], "@components/*": ["./src/components/*"], "@configs/*": ["./src/configs/*"], "@views/*": ["./src/views/*"]}}, "include": ["next.config.ts", "tailwind.config.ts", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}