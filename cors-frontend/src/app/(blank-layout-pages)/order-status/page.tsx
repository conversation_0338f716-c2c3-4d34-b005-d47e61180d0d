import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import {
  getFormTypeFromParams,
  getCustomerEmailFromParams,
  getShopifyOrderNumberFromParams,
  handleOrderStatusError,
} from '@/utils/order-status.utils';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

const OrderStatusPage: React.FC<OrderStatusPageProps> = async ({ searchParams }) => {
  const params = await searchParams;

  const shopifyOrderNumber = getShopifyOrderNumberFromParams(params);
  const customerEmail = getCustomerEmailFromParams(params);
  const formType = getFormTypeFromParams(params);

  let orderData: PublicOrderStatus | null = null;
  let error: string | null = null;

  // If we have order number, fetch data server-side
  if (shopifyOrderNumber) {
    try {
      orderData = await fetchPublicOrderStatus(shopifyOrderNumber, customerEmail, formType);
    } catch (err) {
      error = handleOrderStatusError(err);
    }
  }

  return (
    <PublicOrderStatusPage
      initialOrderData={orderData}
      initialError={error}
      shopifyOrderNumber={shopifyOrderNumber}
      customerEmail={customerEmail}
      formType={formType}
    />
  );
};

export default OrderStatusPage;
