import RoleViewForm from '@views/roles/RoleViewForm';
import apiClient from '@/utils/axios';

export default async function RoleViewPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const id = resolvedParams.id;

  try {
    const permissionsResponse = await apiClient.get('/roles/permissions');
    const roleResponse = await apiClient.get(`/roles/${id}`);
    return <RoleViewForm permissions={permissionsResponse.data} roleData={roleResponse.data} />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <div>Failed to load data. Please try again later.</div>;
  }
}
