import { Suspense } from 'react';
import RoleAddForm from '@/views/roles/RoleAddForm';
import LoadingView from '@/components/LoadingView';
import apiClient from '@/utils/axios';

const RoleAddData = async () => {
  try {
    const response = await apiClient.get('/roles/permissions');
    return <RoleAddForm permissions={response.data} />;
  } catch (error) {
    console.error('Failed to fetch permissions:', error);
    return <div>Failed to load permissions data. Please try again later.</div>;
  }
};

const RoleAddPage = () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <RoleAddData />
    </Suspense>
  );
};

export default RoleAddPage;
