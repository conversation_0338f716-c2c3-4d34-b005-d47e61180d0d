import { Suspense } from 'react'
import apiClient from '@/utils/axios'
import Roles from '@/views/roles'
import LoadingView from '@/components/LoadingView'
import { PageProps } from '../../../../.next/types/app/(dashboard)/roles/page'

const RolesData = async ({ page, limit, q, fq, sort}: { page: number , limit : number, q : string , fq : string,sort ?: string}) => {
  try {
    const response = await apiClient.get(`/roles?page=${page}&limit=${limit}${q ? `&q=${q}` :  ''}${fq ? `&fq=${fq}` : ''}${sort ? `&sort=${sort}` : ''}`)
    return <Roles userData={response.data.data} count={response.data.count}/>
  } catch (error) {
    console.error('Failed to fetch role:', error)
    return <div>Failed to load data for role. Please try again later.</div>
  }
}

const RolesApp = async (Props :  PageProps) => {
  const {searchParams} = Props
  const {page = 1 , limit = 25, q , fq,sort} = await searchParams;
  return (
    <Suspense fallback={<LoadingView />}>
      <RolesData sort={sort} q={q} fq={fq} page={page} limit={limit} />
    </Suspense>
  )
}

export default RolesApp
