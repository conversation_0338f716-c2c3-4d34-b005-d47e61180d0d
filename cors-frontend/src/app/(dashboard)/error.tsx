'use client';

import { Box, Typography, Button, Paper } from '@mui/material';

export default function GlobalError({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
      <Paper elevation={3} sx={{ p: 4, maxWidth: 500, textAlign: 'center' }}>
        <Typography variant="h5">Oops! Something went wrong.</Typography>
        <Typography variant="body2" sx={{ mt: 2 }}>{error.message}</Typography>
        <Button variant="contained" sx={{ mt: 2 }} onClick={() => reset()}>
          Retry
        </Button>
      </Paper>
    </Box>
  );
}
