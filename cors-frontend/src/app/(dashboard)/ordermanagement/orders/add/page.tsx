import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import AddManualOrderForm from '@/views/order-management/orders/add/AddManualOrderForm';

const OrdersAddManualData = async () => {
  try {
    return <AddManualOrderForm />;
  } catch (error) {
    console.error('Failed to fetch roles:', error);
    return <div>Failed to load roles data. Please try again later.</div>;
  }
};

const UserAddPage = () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <OrdersAddManualData />
    </Suspense>
  );
};

export default UserAddPage;
