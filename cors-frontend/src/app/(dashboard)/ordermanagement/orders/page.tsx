import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrdersWrapper from '@/views/order-management/orders';
import { fetchOrders } from '@/actions/orders';
import { PaginationQueryParams } from '@/utils/paginationParamHelper';

const OrdersData = async ({ page, limit }: Pick<PaginationQueryParams, 'page' | 'limit'>) => {
  try {
    // Regular fetch without filters - filtering is handled client-side
    const [unflaggedData, flaggedData] = await Promise.all([
      fetchOrders({ page, limit, fq: 'flagged:eq:false' }),
      fetchOrders({ page, limit, fq: 'flagged:eq:true' }),
    ]);

    return (
      <OrdersWrapper
        data={{
          unflagged: unflaggedData,
          flagged: flaggedData,
        }}
        page={page}
        limit={limit}
      />
    );
  } catch (error) {
    return <div>Failed to load data. Please try again later.</div>;
  }
};

const OrderIntaketPage = async (Props: { searchParams: any }) => {
  const params = await Props.searchParams;
  const page = params?.get ? params.get('page') ?? 1 : params.page ?? 1;
  const limit = params?.get ? params.get('limit') ?? 25 : params.limit ?? 25;
  
  return (
    <Suspense fallback={<LoadingView />}>
      <OrdersData 
        page={Number(page)} 
        limit={Number(limit)} 
      />
    </Suspense>
  );
};

export default OrderIntaketPage;
