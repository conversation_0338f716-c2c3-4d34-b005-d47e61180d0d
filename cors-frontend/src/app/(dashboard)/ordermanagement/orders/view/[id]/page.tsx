import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrderView from '@/views/order-management/orders/view';
import apiClient from '@/utils/axios';

const OrderViewData = async ({ id }: { id: string }) => {
  try {
    const response = await apiClient.get(`/orders/${id}`);
    const orderData = response.data;
    return <OrderView orderData={orderData} />;
  } catch (error) {
    console.error('Failed to load order data:', error);
    return <div>Failed to load order data. Please try again later.</div>;
  }
};

const OrderViewPage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const resolvedParams = await params;
  const id = resolvedParams.id;

  return (
    <Suspense fallback={<LoadingView />}>
      <OrderViewData id={id} />
    </Suspense>
  );
};

export default OrderViewPage;
