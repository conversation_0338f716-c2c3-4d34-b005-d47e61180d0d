import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrderManagementWrapper from '@/views/order-management';
import { getServerMode } from '@/@core/utils/serverHelpers';
import apiClient from '@/utils/axios';

const OrderManagementData = async ({ 
  page, 
  limit, 
}: { 
  page: number; 
  limit: number; 
}) => {
  try {
    return <OrderManagementWrapper page={page} limit={limit} />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <div>Failed to load data. Please try again later.</div>;
  }
};

const OrderManagementPage = async (Props: any) => {
  const { searchParams } = Props;
  const { page = 1, limit = 25 } = await searchParams;
  return (
    <Suspense fallback={<LoadingView />}>
      <OrderManagementData page={page} limit={limit} />
    </Suspense>
  );
};

export default OrderManagementPage;
