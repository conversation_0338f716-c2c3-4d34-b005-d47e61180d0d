import QueueActionWrapper from '@/views/order-management/queues/action/QueueActionWrapper';
import React from 'react';

const QueueActionPage = async ({
  params,
  searchParams,
}: {
  params: Promise<{ type: string }>;
  searchParams: Promise<{ queueId: string }>;
}) => {
  const { type: actionType } = await params;
  const { queueId } = await searchParams;
  const decodedType = decodeURIComponent(actionType);
  return <QueueActionWrapper actionType={decodedType} queueId={queueId} />;
};

export default QueueActionPage;
