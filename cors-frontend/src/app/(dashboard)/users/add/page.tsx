import { Suspense } from 'react';
import apiClient from '@/utils/axios';
import UserAddForm from '@/views/users/add/UserAddForm';
import LoadingView from '@/components/LoadingView';
import { RolesData } from '@/types/roleTypes';

const UserAddData = async () => {
  try {
    const response = await apiClient.get('/roles');
    const rolesData = response.data.data.filter((role: RolesData) => role.name !== 'Owner') || [];
    return <UserAddForm roles={rolesData} />;
  } catch (error) {
    console.error('Failed to fetch roles:', error);
    return <div>Failed to load roles data. Please try again later.</div>;
  }
};

const UserAddPage = () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <UserAddData />
    </Suspense>
  );
};

export default UserAddPage;
