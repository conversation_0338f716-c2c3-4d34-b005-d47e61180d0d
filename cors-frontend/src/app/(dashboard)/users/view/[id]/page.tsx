import { Suspense } from 'react'
import UserViewForm from '@/views/users/view/UserViewForm'
import apiClient from '@/utils/axios'
import LoadingView from '@/components/LoadingView'

async function UserView({ id }: { id: string }) {
  try {
    const userResponse = await apiClient.get(`/users/${id}`)
    
    return (
      <UserViewForm 
        userData={userResponse.data} 
        roleData={userResponse?.data.roles[0]}
      />
    )
  } catch (error) {
    console.error('Failed to fetch data:', error)
    return <div>Failed to load data. Please try again later.</div>
  }
}

export default async function UserViewPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params
  const id = resolvedParams.id
  
  return (
    <Suspense fallback={<LoadingView />}>
      <UserView id={id} />
    </Suspense>
  )
}
