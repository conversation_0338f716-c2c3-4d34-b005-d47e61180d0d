import { Suspense } from 'react';

import LoadingView from '@/components/LoadingView';
import SettingsView from '@/views/settings';

const SettingsData = async () => {
  try {
    return <SettingsView />;
  } catch (error) {
    console.error('Failed to fetch role:', error);
    return <div>Failed to load data for role. Please try again later.</div>;
  }
};

const Settings = async () => {
  return (
    <Suspense fallback={<LoadingView />}>
      <SettingsData />
    </Suspense>
  );
};

export default Settings;
