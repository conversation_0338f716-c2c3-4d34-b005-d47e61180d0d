import apiClient from '@/utils/axios';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    const { data, status} = await apiClient.post(`auth/forgot-password`, {
      email
    });

    if (!status) {
      throw new Error(data.message || 'Failed to process password reset request');
    }

    return NextResponse.json({ message: data.message ?? 'If your email is registered, you will receive a password reset link.' });
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { message: 'Failed to process password reset request' },
      { status: 500 }
    );
  }
} 