'use server'

import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;

// Create a reusable axios client for public endpoints
function createPublicApiClient() {
  return axios.create({
    baseURL: API_URL,
    withCredentials: false,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
}

// Common error handler for API responses
function handleApiError(error: any, context: string): never {
  console.error(`Error in ${context}:`, error);

  if (axios.isAxiosError(error)) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      throw new Error('Invalid access. Please use the link from your shopify order tracking.');
    }
    if (error.response?.status === 404) {
      throw new Error(context.includes('rejected image')
        ? 'No rejected image found for this order'
        : 'Order not found. Please check your order number and email.');
    }
    if (error.response?.status && error.response.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }
    throw new Error(error.response?.data?.message || `Failed to ${context.toLowerCase()}`);
  }

  throw new Error(`Unable to ${context.toLowerCase()}. Please check your connection and try again.`);
}


export interface PublicOrderStatusResponse {
  shopifyOrderNumber: string;
  orderDate: string;
  orderStatus: string;
  statusUpdatedAt: string;
  customerFirstName: string;
  customerLastName: string;
  customerEmail: string;
  itemCount: number;
  shippingAddress?: {
    city: string;
    country: string;
    address1: string;
    address2?: string | null;
    province: string;
    country_code: string;
    province_code: string;
  };
  lineItems: Array<{
    id: string;
    itemNumber: string;
    priority: string;
    quantity: number;
    status: string;
    currentStatus?: string;
    lastUpdatedAt?: string;
    rejectedImage?: {
      url: string;
      rejectionReason?: string;
    };
    selectedImage?: {
      url: string;
      message?: string;
    };
  }>;
}

export interface RejectedImageResponse {
  url: string;
  rejectionReason?: string;
  itemNumber: string;
}

export async function fetchRejectedImageData(
  orderNumber: string,
  customerEmail: string,
  itemNumber?: string
): Promise<RejectedImageResponse | null> {
  try {
    const publicApiClient = createPublicApiClient();

    const params = new URLSearchParams({
      orderNumber,
      email: customerEmail,
    });

    if (itemNumber) {
      params.append('itemNumber', itemNumber);
    }

    const response = await publicApiClient.get(`/order-tracking/rejected-image?${params.toString()}`);
    console.log('Rejected image response:', response.data);

    if (!response.data) {
      return null;
    }

    return response.data;
  } catch (error) {
    // Handle 404 as a special case - return null instead of throwing
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      console.log('No rejected image found for this order');
      return null;
    }

    handleApiError(error, 'fetch rejected image data');
  }
}

export async function fetchPublicOrderStatus(
  orderNumber: string,
  customerEmail?: string,
  formType?: string,
): Promise<PublicOrderStatusResponse> {
  try {
    const publicApiClient = createPublicApiClient();

    const requestBody = {
      orderNumber: orderNumber,
      email: customerEmail || '',
    };

    const response = await publicApiClient.post('/order-tracking', requestBody);
    console.log('Order tracking response:', response.data);

    if (!response.data) {
      throw new Error('No data returned from server');
    }

    // If it's newImageRequest, also fetch rejected image data
    if (formType === 'newImageRequest' && response.data.lineItems) {
      await enrichWithRejectedImageData(response.data, orderNumber, customerEmail || '');
    }

    return response.data;

  } catch (error) {
    handleApiError(error, 'fetch order status');
  }
}

// Helper function to enrich order data with rejected image data
async function enrichWithRejectedImageData(
  orderData: any,
  orderNumber: string,
  customerEmail: string
): Promise<void> {
  try {
    const rejectedImageData = await fetchRejectedImageData(orderNumber, customerEmail);

    // Find the line item that needs the rejected image and add it
    if (rejectedImageData && orderData.lineItems) {
      const targetLineItem = orderData.lineItems.find(
        (item: any) => item.itemNumber === rejectedImageData.itemNumber
      );

      if (targetLineItem) {
        targetLineItem.rejectedImage = {
          url: rejectedImageData.url,
          rejectionReason: rejectedImageData.rejectionReason
        };
      }
    }
  } catch (rejectedImageError) {
    // Log the error but don't fail the entire request
    console.error('Failed to fetch rejected image data:', rejectedImageError);
  }
}

export async function requestNewImage(
  orderNumber: string,
  lineItemId: string,
  reason: string,
  customerEmail: string,
  imageFile?: File
): Promise<void> {
  try {
    const publicApiClient = createPublicApiClient();

    // Create FormData for file upload if image is provided
    const formData = new FormData();
    formData.append('orderNumber', orderNumber);
    formData.append('lineItemId', lineItemId);
    formData.append('reason', reason);
    formData.append('customerEmail', customerEmail);

    if (imageFile) {
      formData.append('newImage', imageFile);
    }

    await publicApiClient.post('/order-tracking/request-new-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('New image request submitted successfully');
  } catch (error) {
    handleApiError(error, 'submit new image request');
  }
}

export async function submitCustomerContact(
  orderNumber: string,
  lineItemId: string,
  customerText: string,
  customerEmail: string,
  imageFile?: File
): Promise<void> {
  try {
    const publicApiClient = createPublicApiClient();

    const formData = new FormData();
    formData.append('orderNumber', orderNumber);
    formData.append('lineItemId', lineItemId);
    formData.append('customerText', customerText);
    formData.append('customerEmail', customerEmail);

    if (imageFile) {
      formData.append('contactImage', imageFile);
    }

    await publicApiClient.post('/order-tracking/customer-contact', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('Customer contact submitted successfully');
  } catch (error) {
    handleApiError(error, 'submit customer contact');
  }
}

export async function submitCustomerApproval(
  orderNumber: string,
  lineItemId: string,
  approved: boolean,
  customerEmail: string,
  revisionText?: string
): Promise<void> {
  try {
    const publicApiClient = createPublicApiClient();

    const requestBody = {
      orderNumber,
      lineItemId,
      approved,
      customerEmail,
      revisionText: revisionText || undefined,
    };

    await publicApiClient.post('/order-tracking/customer-approval', requestBody);

    console.log('Customer approval submitted successfully');
  } catch (error) {
    handleApiError(error, 'submit customer approval');
  }
}
