'use server'

import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;


export interface PublicOrderStatusResponse {
  shopifyOrderNumber: string;
  orderDate: string;
  orderStatus: string;
  statusUpdatedAt: string;
  customerFirstName: string;
  customerLastName: string;
  customerEmail: string;
  itemCount: number;
  shippingAddress?: {
    city: string;
    country: string;
    address1: string;
    address2?: string | null;
    province: string;
    country_code: string;
    province_code: string;
  };
  lineItems: Array<{
    id: string;
    itemNumber: string;
    priority: string;
    quantity: number;
    status: string;
    currentStatus?: string;
    lastUpdatedAt?: string;
    rejectedImage?: {
      url: string;
      rejectionReason?: string;
    };
    selectedImage?: {
      url: string;
      message?: string;
    };
  }>;
}

export interface RejectedImageResponse {
  url: string;
  rejectionReason?: string;
  itemNumber: string;
}

export async function fetchRejectedImageData(
  orderNumber: string,
  customerEmail: string,
  itemNumber?: string
): Promise<RejectedImageResponse | null> {
  try {
    // Create a new axios instance without authentication for public endpoints
    const publicApiClient = axios.create({
      baseURL: API_URL,
      withCredentials: false,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    const params = new URLSearchParams({
      orderNumber,
      email: customerEmail,
    });

    if (itemNumber) {
      params.append('itemNumber', itemNumber);
    }

    const response = await publicApiClient.get(`/order-tracking/rejected-image?${params.toString()}`);
    console.log('Rejected image response:', response.data);

    if (!response.data) {
      return null;
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching rejected image data:', error);

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        console.log('No rejected image found for this order');
        return null;
      }
      if (error.response?.status === 401 || error.response?.status === 403) {
        throw new Error('Invalid access. Please use the link from your shopify order tracking.');
      }
      if (error.response?.status && error.response.status >= 500) {
        throw new Error('Server error. Please try again later.');
      }
    }

    // For network errors or other issues
    throw new Error('Unable to fetch rejected image data. Please check your connection and try again.');
  }
}

export async function fetchPublicOrderStatus(
  orderNumber: string,
  customerEmail?: string,
  formType?: string,
): Promise<PublicOrderStatusResponse> {
  try {
    // Create a new axios instance without authentication for public endpoints
    const publicApiClient = axios.create({
      baseURL: API_URL,
      withCredentials: false,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    const requestBody = {
      orderNumber: orderNumber,
      email: customerEmail || '',
      formType: formType || undefined,
    };

    const response = await publicApiClient.post('/order-tracking', requestBody);
    console.log('Order tracking response:', response.data);

    if (!response.data) {
      throw new Error('No data returned from server');
    }

    // If it's newImageRequest, also fetch rejected image data
    if (formType === 'newImageRequest' && response.data.lineItems) {
      try {
        const rejectedImageData = await fetchRejectedImageData(orderNumber, customerEmail || '');

        // Find the line item that needs the rejected image and add it
        if (rejectedImageData) {
          const targetLineItem = response.data.lineItems.find(
            (item: any) => item.itemNumber === rejectedImageData.itemNumber
          );

          if (targetLineItem) {
            targetLineItem.rejectedImage = {
              url: rejectedImageData.url,
              rejectionReason: rejectedImageData.rejectionReason
            };
          }
        }
      } catch (rejectedImageError) {
        // Log the error but don't fail the entire request
        console.error('Failed to fetch rejected image data:', rejectedImageError);
      }
    }

    return response.data;
    
  } catch (error) {
    console.error('Error fetching public order status:', error);

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 401 || error.response?.status === 403) {
        throw new Error('Invalid access. Please use the link from your shopify order tracking.');
      }
      if (error.response?.status === 404) {
        throw new Error('Order not found. Please check your order number and email.');
      }
      if (error.response?.status && error.response.status >= 500) {
        throw new Error('Server error. Please try again later.');
      }
      throw new Error(error.response?.data?.message || 'Failed to fetch order status');
    }

    throw new Error('Unable to fetch order status. Please check your connection and try again.');
  }
}

export async function requestNewImage(orderNumber: string, lineItemId: string, reason: string): Promise<void> {
  try {
    // This would be implemented when the backend endpoint is ready
    // For now, we'll just log the request
    console.log('Request new image:', { orderNumber, lineItemId, reason });
    
    // TODO: Implement actual API call when backend endpoint is available
    // const publicApiClient = axios.create({
    //   baseURL: API_URL,
    //   withCredentials: false,
    // });
    
    // await publicApiClient.post(`/orders/public/request-new-image`, {
    //   orderNumber,
    //   lineItemId,
    //   reason
    // });
    
    throw new Error('Request new image functionality is not yet implemented');
  } catch (error) {
    console.error('Error requesting new image:', error);
    throw error;
  }
}
