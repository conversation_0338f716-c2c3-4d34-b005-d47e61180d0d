// Type Imports
import type { HorizontalMenuDataType } from '@/types/menuTypes'

const horizontalMenuData = (): HorizontalMenuDataType[] => [
  {
    label: 'Home',
    href: '/home',
    icon: 'ri-home-smile-line'
  },
 {
  label: 'users',
  icon: 'ri-user-line',
  children: [
    {
      label: 'List',
      href: '/users'
    },
  ]
  },
  {
        label: 'Roles',
        icon: 'ri-lock-line',
        children: [
          {
            label: 'Roles',
            href: '/roles'
          },
        ]
  },
   {
  label: 'Orders Management',
  icon: 'ri-menu-line',
  children: [
    {
      label: 'PIMS',
      href: '/ordermanagement/products'
    },
  ]
  },
  // {
  //   label: 'About',
  //   href: '/about',
  //   icon: 'ri-information-line'
  // },

 
]

export default horizontalMenuData
