import apiClient from "@/utils/axios";


const authService = {
  // Login function
  async login(email: string, password: string) {
    try {
      const response = await apiClient.post('/auth/login', { email, password });
      // Store tokens immediately after successful login
      if (typeof window !== 'undefined' && response.data.access_token) {
        localStorage.setItem('auth-token', response.data.access_token);
        localStorage.setItem('refresh-token', response.data.refresh_token);
      }
      
      return response.data;
    } catch (error) {
      throw error;
    }
  },

}

export default authService
