export const handleDownload = async (imageUrl: string, fileName: string) => {
  try {
    // Fetch the image as a blob
    const response = await fetch(imageUrl);
    const blob = await response.blob();

    // Create object URL from blob
    const blobUrl = window.URL.createObjectURL(blob);

    // Create download link
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error('Error downloading file:', error);
    // If download fails, open in new tab as fallback
    window.open(imageUrl, '_blank');
  }
};
