import * as Yup from 'yup';


export const PASSWORD_ERROR_MESSAGE =
  "Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and can include special characters (@$!%*?&)";

export const validatePassword = (password: string): boolean => {
  const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return PASSWORD_REGEX.test(password);
};

// Password validation with error message
export const validatePasswordWithError = (password: string): { isValid: boolean; error?: string } => {
  if (!password) {
    return { isValid: true };
  }
  
  if (!validatePassword(password)) {
    return { isValid: false, error: PASSWORD_ERROR_MESSAGE };
  }
  
  return { isValid: true };
}; 


export type Filter = {
  attribute: string;
  operator: string;
  value: any;
  required?: boolean;
};

export const buildFiltersSchema = (filters: Filter[][]) => {
  return Yup.array().of(
    Yup.array().of(
      Yup.object().shape({
        attribute: Yup.string().required('Attribute is required'),
        operator: Yup.string()
          .oneOf(['eq', 'ne', 'lt', 'lte', 'gt', 'gte', 'like', 'in', 'notIn', 'between'])
          .required('Operator is required'),
        value: Yup.lazy((value, context) => {
          const filter = context.parent as Filter;
          
          // Special handling for orderDate
          if (filter.attribute === 'orderDate' && filter.operator === 'between') {
            return Yup.object().shape({
              start: Yup.string().required('Start date is required'),
              end: Yup.string().required('End date is required')
            });
          }

          if (filter.required !== false) {
            if (Array.isArray(value)) {
              return Yup.array()
                .of(Yup.string())
                .min(1, `${filter.attribute} must have at least one item`)
                .required();
            }

            if (typeof value === 'boolean') {
              return Yup.boolean().required(`${filter.attribute} is required`);
            }

            if (!isNaN(value)) {
              return Yup.number().required(`${filter.attribute} is required`);
            }

            return Yup.string().required(`${filter.attribute} is required`);
          }

          // Fallback for optional values
          return Yup.mixed();
        }),
      })
    )
  );
};



