import { Actions, ActionsTarget } from '@/libs/casl/ability';

/**
 * Defines hierarchical relationships between permissions
 * When a permission is granted, all its dependencies should also be granted
 */
export const permissionDependencies: Record<Actions, Actions[]> = {
  // Queues dependencies
  [Actions.ViewQueuesListingPage]: [],
  [Actions.ManageQueuesSettings]: [Actions.ViewQueuesListingPage],

  // Crop Review Queue dependencies
  [Actions.ViewCropReviewQueue]: [Actions.ViewQueuesListingPage],
  [Actions.StartStopCropReview]: [Actions.ViewCropReviewQueue, Actions.ViewQueuesListingPage],

  // Crop Needed Queue dependencies
  [Actions.ViewCropNeededQueue]: [Actions.ViewQueuesListingPage],
  [Actions.StartStopCropNeeded]: [Actions.ViewCropNeededQueue, Actions.ViewQueuesListingPage],

  // Template Placement Queue dependencies
  [Actions.ViewTemplatePlacementQueue]: [Actions.ViewQueuesListingPage],
  [Actions.StartStopTemplatePlacement]: [
    Actions.ViewTemplatePlacementQueue,
    Actions.ViewQueuesListingPage,
  ],

  // Artwork Ready Queue dependencies
  [Actions.ViewArtworkReadyQueue]: [Actions.ViewQueuesListingPage],
  [Actions.StartStopArtworkReady]: [Actions.ViewArtworkReadyQueue, Actions.ViewQueuesListingPage],

  // Artwork Revision Queue dependencies
  [Actions.ViewArtworkRevisionQueue]: [Actions.ViewQueuesListingPage],
  [Actions.StartStopArtworkRevision]: [
    Actions.ViewArtworkRevisionQueue,
    Actions.ViewQueuesListingPage,
  ],

  // Orders dependencies
  [Actions.ViewOrderListingPage]: [],
  [Actions.CreateOrder]: [Actions.ViewOrderListingPage],
  [Actions.EditOrderDetialPage]: [Actions.ViewOrderListingPage],

  // Line Items dependencies - all depend on EditOrderDetialPage
  [Actions.EditLineItem]: [Actions.EditOrderDetialPage],
  [Actions.CancelLineItem]: [Actions.EditOrderDetialPage],
  [Actions.CreateLineItemRemake]: [Actions.EditOrderDetialPage],
  [Actions.FlagLineItem]: [Actions.EditOrderDetialPage],
  [Actions.UploadLineItemCompletedArtfile]: [Actions.EditOrderDetialPage],
  [Actions.InitiateLineItemCustomerContactRequest]: [Actions.EditOrderDetialPage],
  [Actions.AddLineItemRevisionRequest]: [Actions.EditOrderDetialPage],

  // PIMS dependencies
  [Actions.EditDetailPage]: [Actions.ViewDetailPage, Actions.ViewPIMSListingPage],
  [Actions.ViewDetailPage]: [Actions.ViewPIMSListingPage],
  [Actions.ActivateDeactivateSKU]: [Actions.ViewPIMSListingPage],
  [Actions.AddArtwork]: [
    Actions.EditDetailPage,
    Actions.ViewDetailPage,
    Actions.ViewPIMSListingPage,
  ],

  // Users dependencies
  [Actions.CreateUsers]: [Actions.ViewUsersListingPage, Actions.ViewUserDetail],
  [Actions.EditUser]: [Actions.ViewUserDetail, Actions.ViewUsersListingPage],
  [Actions.ViewUserDetail]: [Actions.ViewUsersListingPage],
  [Actions.DeactivateUser]: [Actions.ViewUsersListingPage],
  [Actions.ActivateUser]: [Actions.ViewUsersListingPage],

  // Roles dependencies
  [Actions.CreateRole]: [Actions.ViewRolesListingPage, Actions.ViewRoleDetail],
  [Actions.EditRole]: [Actions.ViewRoleDetail, Actions.ViewRolesListingPage],
  [Actions.ViewRoleDetail]: [Actions.ViewRolesListingPage],
  [Actions.DeactivateRole]: [Actions.ViewRolesListingPage],
  [Actions.ActivateRole]: [Actions.ViewRolesListingPage],
  [Actions.DeleteRole]: [Actions.ViewRolesListingPage],

  // Vendors dependencies
  [Actions.CreateVendor]: [Actions.ViewVendorsListingPage, Actions.ViewVendorDetail],
  [Actions.EditVendor]: [Actions.ViewVendorDetail, Actions.ViewVendorsListingPage],
  [Actions.ViewVendorDetail]: [Actions.ViewVendorsListingPage],
  [Actions.DeactivateVendor]: [Actions.ViewVendorsListingPage],
  [Actions.ActivateVendor]: [Actions.ViewVendorsListingPage],
  [Actions.DeleteVendor]: [Actions.ViewVendorsListingPage],

  // Add other actions with empty dependencies
  [Actions.ViewUsers]: [],
  [Actions.ViewPIMSListingPage]: [],
  [Actions.ViewUsersListingPage]: [],
  [Actions.ViewRolesListingPage]: [],
  [Actions.ViewVendorsListingPage]: [],
  [Actions.Manage]: [],
};

/**
 * Identifies which permissions are listing pages that should be mandatory
 */
export const listingPagePermissions: Actions[] = [
  Actions.ViewPIMSListingPage,
  Actions.ViewUsersListingPage,
  Actions.ViewRolesListingPage,
  Actions.ViewOrderListingPage,
  Actions.ViewVendorsListingPage,
];

/**
 * Groups permissions by their resource category
 */
export const permissionsByResource: Record<ActionsTarget, Actions[]> = {
  [ActionsTarget.ORDERS]: [
    Actions.ViewOrderListingPage,
    Actions.CreateOrder,
    Actions.EditOrderDetialPage,
  ],
  [ActionsTarget.LINE_ITEMS]: [
    Actions.EditLineItem,
    Actions.CancelLineItem,
    Actions.CreateLineItemRemake,
    Actions.FlagLineItem,
    Actions.UploadLineItemCompletedArtfile,
    Actions.InitiateLineItemCustomerContactRequest,
    Actions.AddLineItemRevisionRequest,
  ],
  [ActionsTarget.PIMS]: [
    Actions.ViewPIMSListingPage,
    Actions.ActivateDeactivateSKU,
    Actions.ViewDetailPage,
    Actions.EditDetailPage,
    Actions.AddArtwork,
  ],
  [ActionsTarget.QUEUES]: [Actions.ViewQueuesListingPage, Actions.ManageQueuesSettings],
  [ActionsTarget.CROP_REVIEW_QUEUE]: [Actions.ViewCropReviewQueue, Actions.StartStopCropReview],
  [ActionsTarget.CROP_NEEDED_QUEUE]: [Actions.ViewCropNeededQueue, Actions.StartStopCropNeeded],
  [ActionsTarget.TEMPLATE_PLACEMENT_QUEUE]: [
    Actions.ViewTemplatePlacementQueue,
    Actions.StartStopTemplatePlacement,
  ],
  [ActionsTarget.ARTWORK_READY_QUEUE]: [
    Actions.ViewArtworkReadyQueue,
    Actions.StartStopArtworkReady,
  ],
  [ActionsTarget.ARTWORK_REVISION_QUEUE]: [
    Actions.ViewArtworkRevisionQueue,
    Actions.StartStopArtworkRevision,
  ],
  [ActionsTarget.UserManagment]: [
    Actions.ViewUsersListingPage,
    Actions.CreateUsers,
    Actions.ViewUserDetail,
    Actions.EditUser,
    Actions.DeactivateUser,
    Actions.ActivateUser,
  ],
  [ActionsTarget.RoleManagement]: [
    Actions.ViewRolesListingPage,
    Actions.ViewRoleDetail,
    Actions.CreateRole,
    Actions.EditRole,
    Actions.DeactivateRole,
    Actions.ActivateRole,
    Actions.DeleteRole,
  ],
  [ActionsTarget.VENDORS]: [
    Actions.ViewVendorsListingPage,
    Actions.ViewVendorDetail,
    Actions.CreateVendor,
    Actions.EditVendor,
    Actions.ActivateVendor,
    Actions.DeactivateVendor,
    Actions.DeleteVendor,
  ],
  [ActionsTarget.ALL]: [Actions.Manage],
};

/**
 * Resolves permission dependencies to ensure that when a permission is selected,
 * all its dependencies are also selected
 * @param selectedPermissions Current selected permissions
 * @returns Updated permissions with dependencies resolved
 */
export const resolvePermissionDependencies = (
  selectedPermissions: Record<string, Actions[]>,
): Record<string, Actions[]> => {
  let newPermissions = { ...selectedPermissions };

  // Process each resource and its actions
  Object.entries(selectedPermissions).forEach(([resource, actions]) => {
    // Process each action to add its dependencies
    actions.forEach(action => {
      const dependencies = permissionDependencies[action] || [];

      dependencies.forEach(dep => {
        // Find the resource that contains this dependency
        const resourceForDep = Object.entries(permissionsByResource).find(([_, perms]) =>
          perms.includes(dep),
        );

        if (resourceForDep) {
          const [depResource] = resourceForDep;
          const resourceActions = newPermissions[depResource] || [];

          if (!resourceActions.includes(dep)) {
            newPermissions = {
              ...newPermissions,
              [depResource]: [...resourceActions, dep],
            };
          }
        }
      });
    });
  });

  // Special case for Line Items - add ViewOrderListingPage and EditOrderDetialPage if Line Items are selected
  const lineItemPermissions = selectedPermissions['Line Items'] || [];
  if (lineItemPermissions.length > 0) {
    const ordersPermissions = newPermissions['Orders'] || [];
    const requiredPermissions = [Actions.ViewOrderListingPage, Actions.EditOrderDetialPage];
    const missingPermissions = requiredPermissions.filter(p => !ordersPermissions.includes(p));

    if (missingPermissions.length > 0) {
      newPermissions = {
        ...newPermissions,
        Orders: [...ordersPermissions, ...missingPermissions],
      };
    }
  }

  // Special case for Queue Types - add ViewQueuesListingPage if any Queue Type is selected
  const queueTypes = [
    'Crop Review Queue',
    'Crop Needed Queue',
    'Template Placement Queue',
    'Artwork Ready Queue',
    'Artwork Revision Queue',
  ];

  const hasQueueTypePermissions = queueTypes.some(
    queueType => (selectedPermissions[queueType] || []).length > 0,
  );

  if (hasQueueTypePermissions) {
    const queuesPermissions = newPermissions['Queues'] || [];
    if (!queuesPermissions.includes(Actions.ViewQueuesListingPage)) {
      newPermissions = {
        ...newPermissions,
        Queues: [...queuesPermissions, Actions.ViewQueuesListingPage],
      };
    }
  }

  // Special case for Vendors
  if (selectedPermissions['Vendors']?.length > 0) {
    newPermissions = ensureVendorDependencies(newPermissions);
  }

  return newPermissions;
};

/**
 * Special function to handle Line Items permissions
 * Ensures that when a Line Item permission is selected, the ViewOrderListingPage is also selected
 * @param selectedPermissions Current selected permissions
 * @returns Updated permissions with dependencies resolved
 */
export const ensureLineItemDependencies = (
  selectedPermissions: Record<string, Actions[]>,
): Record<string, Actions[]> => {
  const lineItemPermissions = selectedPermissions['Line Items'] || [];

  if (lineItemPermissions.length > 0) {
    const ordersPermissions = selectedPermissions['Orders'] || [];

    if (!ordersPermissions.includes(Actions.EditOrderDetialPage)) {
      return {
        ...selectedPermissions,
        Orders: [...ordersPermissions, Actions.EditOrderDetialPage],
      };
    }
  }

  return selectedPermissions;
};

/**
 * Special function to handle Vendor permissions
 * Ensures that when Vendor-related permissions are selected, the ViewVendorsListingPage and ViewVendorDetail are also selected
 * @param selectedPermissions Current selected permissions
 * @returns Updated permissions with dependencies resolved
 */
export const ensureVendorDependencies = (
  selectedPermissions: Record<string, Actions[]>,
): Record<string, Actions[]> => {
  // Check if any Vendor permissions are selected
  const vendorPermissions = selectedPermissions['Vendors'] || [];

  if (vendorPermissions.length > 0) {
    let updatedVendorPermissions = [...vendorPermissions];
    let needsUpdate = false;

    // Check if CreateVendor or EditVendor is selected
    const hasCreateOrEdit = vendorPermissions.some(
      action => action === Actions.CreateVendor || action === Actions.EditVendor,
    );

    // If CreateVendor or EditVendor is selected, ensure ViewVendorDetail is also selected
    if (hasCreateOrEdit && !updatedVendorPermissions.includes(Actions.ViewVendorDetail)) {
      updatedVendorPermissions.push(Actions.ViewVendorDetail);
      needsUpdate = true;
    }

    // Ensure ViewVendorsListingPage is selected for any vendor permission
    if (!updatedVendorPermissions.includes(Actions.ViewVendorsListingPage)) {
      updatedVendorPermissions.push(Actions.ViewVendorsListingPage);
      needsUpdate = true;
    }

    if (needsUpdate) {
      return {
        ...selectedPermissions,
        Vendors: updatedVendorPermissions,
      };
    }
  }

  return selectedPermissions;
};

/**
 * Checks if a permission is a listing page permission
 * @param action The permission action to check
 * @returns True if the permission is a listing page
 */
export const isListingPagePermission = (action: Actions): boolean => {
  return listingPagePermissions.includes(action);
};

/**
 * Checks if any non-listing page permission is selected for a resource
 * @param resource The resource to check
 * @param selectedActions Array of currently selected actions for the resource
 * @returns True if any non-listing page permission is selected
 */
export const hasNonListingPagePermissions = (
  resource: ActionsTarget,
  selectedActions: Actions[],
): boolean => {
  const resourcePermissions = permissionsByResource[resource] || [];
  const nonListingPermissions = resourcePermissions.filter(p => !isListingPagePermission(p));

  return nonListingPermissions.some(p => selectedActions.includes(p));
};

/**
 * Maps resource string to ActionsTarget enum
 * @param resource The resource string
 * @returns The corresponding ActionsTarget enum value
 */
export const mapResourceToTarget = (resource: string): ActionsTarget => {
  switch (resource) {
    case 'Product Information Management System':
      return ActionsTarget.PIMS;
    case 'Users':
      return ActionsTarget.UserManagment;
    case 'Roles':
      return ActionsTarget.RoleManagement;
    case 'Orders':
      return ActionsTarget.ORDERS;
    case 'Line Items':
      return ActionsTarget.LINE_ITEMS;
    case 'Vendors':
      return ActionsTarget.VENDORS;
    case 'Queues':
      return ActionsTarget.QUEUES;
    default:
      return ActionsTarget.ALL;
  }
};

/**
 * Maps ActionsTarget enum to resource string
 * @param target The ActionsTarget enum value
 * @returns The corresponding resource string
 */
export const mapTargetToResource = (target: ActionsTarget): string => {
  return target.toString();
};
