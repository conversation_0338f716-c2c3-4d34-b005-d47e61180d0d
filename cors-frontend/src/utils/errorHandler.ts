export function handleApiError(error: unknown, fallbackMessage = 'An unexpected error occurred'): Error {
  if (error && typeof error === 'object') {
    if ('response' in error && error.response && typeof error.response === 'object') {
      const response = (error as any).response;
      if (response.data && typeof response.data === 'object' && 'message' in response.data) {
        return new Error(response.data.message || fallbackMessage);
      }
      if (typeof response.statusText === 'string' && response.statusText) {
        return new Error(response.statusText);
      }
    }
    if ('message' in error && typeof (error as any).message === 'string') {
      return new Error((error as any).message);
    }
  }
  return new Error(fallbackMessage);
} 