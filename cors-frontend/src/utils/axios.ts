import { authOptions } from '@/libs/auth';
import axios from 'axios';
import { getServerSession } from 'next-auth/next';
import { getSession, signOut, signIn } from 'next-auth/react';
import { toast } from 'react-toastify';

const API_URL = process.env.NEXT_PUBLIC_API_URL;

const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: false,
});

let failedQueue: any[] = [];

const logout: () => void = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth-token');
    localStorage.removeItem('refresh-token');
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    window.location.href = '/login';
  }
};

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

const refreshToken = async (refreshToken: string) => {
  try {
    const response = await apiClient.post('/auth/refresh', {
      refresh: refreshToken,
    });
    return response?.data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};

apiClient.interceptors.request.use(
  async (config) => {
    if (typeof window !== 'undefined') {
      const session = await getSession();
      if (session?.user?.token) {
        config.headers['Authorization'] = `Bearer ${session.user.token}`;
      }
    } else {
      const session = await getServerSession(authOptions);
      if (session?.user?.token) {
        config.headers['Authorization'] = `Bearer ${session.user.token}`;
      }
    }
    config.headers['ngrok-skip-browser-warning'] = `69420`;

    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response) => {
    if (response.status < 200 || response.status >= 300) {
      throw new Error(`API Error: ${response.status} ${response.statusText || ''}`);
    }
    return response;
  },
  async (err) => {
    if (err.response && err.response.status === 403) {
      toast.error('You are not allowed to perform this action');
      try {
        const session = await getSession();
        const refreshTokenValue = session?.user?.refreshToken;
        if (refreshTokenValue) {
          try {
            const refreshedTokens = await refreshToken(refreshTokenValue);
            // Update NextAuth session with new tokens
            const signInResult = await signIn('credentials', {
              redirect: false,
              accessToken: refreshedTokens.access_token,
              refreshToken: refreshedTokens.refresh_token || refreshTokenValue,
            });
            if (signInResult?.ok) {
              window.location.assign('/home');
            } else {
              await signOut({ redirect: true });
            }
          } catch (refreshErr) {
            toast.error('Session expired. Please log in again.');
            await signOut({ redirect: true });
          }
        } else {
          await signOut({ redirect: true });
        }
      } catch (sessionErr) {
        await signOut({ redirect: true });
      }
    }
    return Promise.reject(err);
  }
);

export default apiClient;
