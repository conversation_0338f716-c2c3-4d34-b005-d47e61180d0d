import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';


export const isItemActionable = (item: { status?: string }, includeCancelled: boolean = false) => {
    const nonActionableStatuses = ['shipped', 'ready for vendors', 'cancelled'];
    const statuses = includeCancelled
      ? [...nonActionableStatuses, 'cancelled']
      : nonActionableStatuses;
    if (!item.status) return true;
    const itemStatus = item.status.toLowerCase();
    return !statuses.includes(itemStatus);
};

// Modify these functions to call the hook inside them
export const canEditLineItem = () => {
    const ability = useAbility();
    return ability?.can(Actions.EditLineItem, ActionsTarget.LINE_ITEMS);
};
   
export const canCancelLineItem = () => {
    const ability = useAbility();
    return ability?.can(Actions.CancelLineItem, ActionsTarget.LINE_ITEMS);
};

export const canCreateRemake = () => {
    const ability = useAbility();
    return ability?.can(Actions.CreateLineItemRemake, ActionsTarget.LINE_ITEMS);
};
    
export const canFlagLineItem = () => {
    const ability = useAbility();
    return ability?.can(Actions.FlagLineItem, ActionsTarget.LINE_ITEMS);
};

export const canInitiateCustomerContact = () => {
    const ability = useAbility();
    return ability?.can(Actions.InitiateLineItemCustomerContactRequest, ActionsTarget.LINE_ITEMS);
};
