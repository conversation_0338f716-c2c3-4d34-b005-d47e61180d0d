"use client";
import { useEffect, useState, useRef } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import { signIn } from "next-auth/react";
import { Controller, useForm } from "react-hook-form";
import { valibotResolver } from "@hookform/resolvers/valibot";
import { object, minLength, string, email, pipe, nonEmpty } from "valibot";
import type { SubmitHandler } from "react-hook-form";
import type { InferInput } from "valibot";
import { toast } from "react-toastify";
import type { Mode } from "@core/types";
import Logo from "@components/layout/shared/Logo";
import Illustrations from "@components/Illustrations";
import themeConfig from "@configs/themeConfig";
import { useImageVariant } from "@core/hooks/useImageVariant";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";

type ErrorType = {
  message: string[];
};

type FormData = InferInput<typeof schema>;

const schema = object({
  email: pipe(
    string(),
    minLength(1, "This field is required"),
    email("Please enter a valid email address")
  ),
  password: pipe(string(), nonEmpty("This field is required")),
});

const Login = ({ mode }: { mode: Mode }) => {
  const [isPasswordShown, setIsPasswordShown] = useState(false);
  const [errorState, setErrorState] = useState<ErrorType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const darkImg = "/images/pages/auth-v2-mask-dark.png";
  const lightImg = "/images/pages/auth-v2-mask-light.png";

  const emailInputRef = useRef<HTMLInputElement>(null);
  const passwordInputRef = useRef<HTMLInputElement>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const errorParam = searchParams.get("error");

  useEffect(() => {
    const checkAutofill = () => {
      requestAnimationFrame(() => {
        const emailInput = emailInputRef.current;
        const passwordInput = passwordInputRef.current;
        if (emailInput && emailInput.value) {
          const event = new Event("input", { bubbles: true });
          emailInput.dispatchEvent(event);
        }

        if (passwordInput && passwordInput.value) {
          const event = new Event("input", { bubbles: true });
          passwordInput.dispatchEvent(event);
        }
      });
    };
    const timer = setTimeout(checkAutofill, 100);
    return () => clearTimeout(timer);
  }, []);

  // Set error from URL parameter on component mount
  useEffect(() => {
    if (errorParam) {
      if (errorParam === "session-expired") {
        setErrorState({
          message: ["Your session has expired. Please log in again."],
        });
      } else if (errorParam === "refresh-token-expired") {
        setErrorState({
          message: ["Your session has expired. Please log in again."],
        });
      } else {
        setErrorState({ message: [errorParam] });
      }
    }
  }, [errorParam]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: valibotResolver(schema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const authBackground = useImageVariant(mode, lightImg, darkImg);

  const handleClickShowPassword = () => setIsPasswordShown((show) => !show);

  const onSubmit: SubmitHandler<FormData> = async (data: FormData) => {
    setIsLoading(true);
    try {
      const res = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false, // Changed to false to handle redirect manually
      });

      if (res && res.ok && !res.error) {
        toast.success("Login successful!");
        const redirectURL = searchParams.get("redirectTo") || "/";

        setTimeout(() => {
          router.push("/home");
        }, 1000);
      } else {
        // Handle error case
        if (res?.error) {
          try {
            const error = JSON.parse(res.error);
            setErrorState(error);
            // Display the first error message in the array
            toast.error(error.message[0] || "Login failed");
          } catch (e) {
            setErrorState({ message: [res.error] });
            toast.error(res.error || "Login failed");
          }
        }
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (errorParam === "session-expired") {
      toast.warning("Your session has expired. Please sign in again.");
    }
  }, [errorParam]);

  return (
    <div className="flex flex-col justify-center items-center min-bs-[100dvh] relative p-6">
      <Card className="flex flex-col sm:is-[450px]">
        <CardContent className="p-6 sm:!p-12">
          <Link href="/" className="flex justify-center items-center mbe-6">
            <Logo />
          </Link>
          <div className="flex flex-col gap-5">
            <div>
              <Typography variant="h4">{`Welcome to ${themeConfig.templateName}!👋🏻`}</Typography>
              <Typography className="mbs-1">
                Please sign-in to your account and start the PIMS
              </Typography>
            </div>
            <form
              noValidate
              action={() => {}}
              autoComplete="off"
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col gap-5">
              <Controller
                name="email"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    autoFocus
                    type="email"
                    label="Email"
                    autoComplete="email"
                    slotProps={{
                      inputLabel: { shrink: true }
                    }}
                    inputRef={emailInputRef}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      errorState !== null && setErrorState(null);
                    }}
                    {...((errors.email || errorState !== null) && {
                      error: true,
                      helperText:
                        errors?.email?.message || errorState?.message[0],
                    })}
                    sx={{
                      "& .MuiFormHelperText-root": {
                        marginTop: "1.25rem",
                      },
                      "& input:-webkit-autofill": {
                        WebkitBoxShadow:
                          "0 0 0 100px var(--mui-palette-background-paper) inset !important",
                        WebkitTextFillColor:
                          "var(--mui-palette-text-primary) !important",
                      },
                    }}
                  />
                )}
              />
              <Controller
                name="password"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Password"
                    id="login-password"
                    inputRef={passwordInputRef}
                    autoComplete="current-password"
                    slotProps={{
                      inputLabel: { shrink: true },
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              size="small"
                              edge="end"
                              onClick={handleClickShowPassword}
                              onMouseDown={(e) => e.preventDefault()}
                              aria-label="toggle password visibility">
                              <i
                                className={
                                  isPasswordShown
                                    ? "ri-eye-off-line"
                                    : "ri-eye-line"
                                }
                              />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }
                    }}
                    type={isPasswordShown ? "text" : "password"}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      errorState !== null && setErrorState(null);
                    }}
                    sx={{
                      "& .MuiFormHelperText-root": {
                        marginTop: "1.25rem",
                      },
                      // Add styles to handle autofill
                      "& input:-webkit-autofill": {
                        WebkitBoxShadow:
                          "0 0 0 100px var(--mui-palette-background-paper) inset !important",
                        WebkitTextFillColor:
                          "var(--mui-palette-text-primary) !important",
                      },
                    }}
                    {...(errors.password && {
                      error: true,
                      helperText: errors.password.message,
                    })}
                  />
                )}
              />
              <div className="flex justify-end items-center">
                <Typography
                  className="text-end"
                  color="primary.main"
                  component={Link}
                  href="/forgot-password">
                  Forgot password?
                </Typography>
              </div>
              <Button
                fullWidth
                variant="contained"
                type="submit"
                disabled={isLoading}>
                {isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  "Log In"
                )}
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>
      <Illustrations maskImg={{ src: authBackground }} />
    </div>
  );
};

export default Login;
