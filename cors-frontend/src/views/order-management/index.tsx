'use client';
import { useEffect } from 'react';
import { Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import ProductSkuListTable from './ProductSkuListTable';
import { useSettings } from '@core/hooks/useSettings';
import { useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '@/redux-store';
import { setProducts } from '@/redux-store/stores/ProductSku.store';
import { useSelector } from 'react-redux';
import { fetchFilteredProductsSkus } from '@/actions';

const OrderManagementWrapper = ({ page = 0, limit = 25 }: { page?: any; limit: any }) => {
  const { updatePageSettings } = useSettings();
  const dispatch = useDispatch<AppDispatch>();
  const filters = useSelector((state: RootState) => state.productSku.filters);

  useEffect(() => {
    const cleanup = updatePageSettings({
      skin: 'default',
    });
    return cleanup;
  }, [filters.length, dispatch, updatePageSettings]);

  useEffect(() => {
    // Only fetch data if there are no filters or if filters are being reset
    if (!filters || filters.length === 0) {
      fetchFilteredProductsSkus({
        filters: filters,
        page: parseInt(page),
        limit: parseInt(limit),
      }).then(val => {
        const transformedData = {
          ...val,
          data: val.data.map((item: any) => ({
            ...item,
            shippingWeight: item.shippingWeight ? Number(item.shippingWeight) : null,
            productLength: item.productLength ? Number(item.productLength) : null,
            productWidth: item.productWidth ? Number(item.productWidth) : null,
            productHeight: item.productHeight ? Number(item.productHeight) : null,
          })),
        };
        dispatch(setProducts(transformedData));
      });
    }
  }, [filters, page, limit, dispatch]);

  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
        <Typography variant="h4">PIMS Listing</Typography>
      </Grid>
      <Grid size={{ xs: 12 }}>
        <ProductSkuListTable />
      </Grid>
    </Grid>
  );
};

export default OrderManagementWrapper;
