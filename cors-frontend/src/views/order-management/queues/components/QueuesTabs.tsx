'use client';
import { Box, Tab, Tabs, Typography } from '@mui/material';
import React, { useMemo, useEffect } from 'react';
import QueuesTableWrapper from './QueuesTableWrapper';
import { TabQueues } from '@/types/queues.types';
import { useAbility } from '@/libs/casl/AbilityContext';
import { getViewPermissionForQueue } from '@/utils/queuePermissionUtils';
import { useSearchParams, useRouter } from 'next/navigation';
import LoadingView from '@/components/LoadingView';

const QueuesTabs = ({ allQueues }: { allQueues: TabQueues[] }) => {
  const ability = useAbility();
  const searchParams = useSearchParams();

  const router = useRouter();

  const currentTab = searchParams.get('tab');

  // Check if user has permission for a specific queue
  const hasQueuePermission = (queueName: string) => {
    const permission = getViewPermissionForQueue(queueName);
    if (!permission) return false;
    return ability?.can(permission.action, permission.target);
  };

  const tabs: TabQueues[] = useMemo(
    () =>
      allQueues?.map(queue => ({
        label: `${queue.name}`,
        id: queue.id,
        content: <></>,
        item_count: queue.item_count,
        name: queue.name,
        disabled: !hasQueuePermission(queue.name),
      })),
    [allQueues, ability],
  );

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  const currentTabIndex = tabs.findIndex(tab => tab.id === currentTab && !tab.disabled);
  const activeTabIndex = currentTabIndex >= 0 ? currentTabIndex : 0;

  useEffect(() => {
    if (!currentTab && tabs.length > 0) {
      if (tabs.length > 0) {
        const params = new URLSearchParams(searchParams);
        params.set('tab', tabs[0].id);
        router.push(`?${params.toString()}`);
      }
    }
  }, [tabs]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    const newTab = tabs[newValue];
    if (!newTab.disabled) {
      const params = new URLSearchParams(searchParams);
      params.set('tab', newTab.id);
      router.push(`?${params.toString()}`);
    }
  };
  // if (!currentTab) {
  //   return <LoadingView />;
  // }
  const activeTab = tabs?.find(tab => tab.id === currentTab);
  return (
    <>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={activeTabIndex}
          onChange={handleChange}
          aria-label="queues tabs"
          role="navigation"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              disabled={tab.disabled}
              label={`${tab.name} (${tab.item_count || 0})`}
              {...a11yProps(index)}
            />
          ))}
        </Tabs>
      </Box>
      {activeTab?.disabled ? (
        <Box sx={{ p: 3, textAlign: 'center', color: 'text.secondary' }}>
          <Typography variant="h6" gutterBottom>
            Access Restricted
          </Typography>
          <Typography variant="body2">
            You don't have permission to view this queue. Contact your administrator for access.
          </Typography>
        </Box>
      ) : (
        activeTab && <QueuesTableWrapper queue={activeTab} />
      )}
    </>
  );
};

export default React.memo(QueuesTabs);
