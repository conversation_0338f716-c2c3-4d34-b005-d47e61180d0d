import DataTable from '@/components/Datatable';

const QueuesTable = ({
  data,
  columns,
  loading,
  queue,
  page,
  limit,
  setPage,
  setLimit,
}: {
  data: any;
  columns: any;
  loading: boolean;
  queue: any;
  page: number;
  limit: number;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}) => {
  return (
    <div>
      {' '}
      <DataTable
        data={data}
        columns={columns || []}
        enableFilters={false}
        loading={loading}
        totalCount={queue?.item_count || 0}
        page={Number(page)}
        initialPageSize={Number(limit)}
        limit={Number(limit)}
        onPaginationUpdate={(page, limit) => {
          setPage(page);
          setLimit(limit);
        }}
      />
    </div>
  );
};

export default QueuesTable;
