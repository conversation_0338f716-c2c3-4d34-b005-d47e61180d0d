'use client';
import Button from '@/components/Button';
import { Box } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { SingleQueueItem } from '@/types/queues.types';
import ProductAttributesViewer from '../../components/ProductAttributesViewer';
import React, { useEffect, useState } from 'react';
import useApiCall from '@/hooks/useApiCall';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import LoadingView from '@/components/LoadingView';
type FormType = {
  image: string[];
};
const TemplatePlacementQueue = ({
  queueItem,
  queueId,
  actionType,
}: {
  queueItem: SingleQueueItem | undefined;
  queueId: string;
  actionType: string;
}) => {
  const router = useRouter();
  const [upcomingQueue, setUpcomingQueue] = useState<SingleQueueItem | undefined>(undefined);
  const { isLoading: loading, makeRequest: requestUpdateQueueItem } = useApiCall<SingleQueueItem>(
    `/workflow-queues/upload-template-placement-file`,
    'post',
    false,
  );

  useEffect(() => {
    if (upcomingQueue) {
      if (upcomingQueue?.lineItems == null) {
        router.push(`/ordermanagement/queues/?tab=${queueId}`);
      }
    }
  }, [upcomingQueue, router]);

  const {
    handleSubmit,
    control,
    setValue,

    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(1, 'Only one art file is allowed')
          .required('Art file is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });

  const onSubmit = async (data: FormType) => {
    const response = await requestUpdateQueueItem({
      body: {
        lineItemId: queueItem?.lineItems?.id,
        templatePlacementFileUrl: data?.image[0],
        queueId: queueId,
      },
    });
    if (response) {
      setUpcomingQueue(response);
      toast.success('Queue item updated successfully');
    }
  };

  if (loading) return <LoadingView />;

  if (upcomingQueue?.lineItems) {
    return (
      <TemplatePlacementQueue queueItem={upcomingQueue} queueId={queueId} actionType={actionType} />
    );
  }

  return (
    <>
      {/* Images Section */}
      <ProductAttributesViewer actionType={actionType} queueItem={queueItem} />

      <Grid container spacing={4} sx={{ mb: 4 }}>
        {queueItem?.lineItems?.attachments?.map((attachment, index) => (
          <React.Fragment key={index}>
            <Grid size={{ xs: 12, md: 6 }}>
              <SingleImageViewCard
                imageUrl={attachment?.attachment_url || ''}
                title="Original Image"
                downloadUrl={attachment?.attachment_url || ''}
                imageName={attachment?.attachment_url?.split('/').pop() || ''}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <SingleImageViewCard
                imageUrl={attachment?.completed_art_file_url || attachment?.cutout_pro_url || ''}
                title={
                  attachment?.completed_art_file_url ? 'Completed Art File' : 'Cutout Pro Image'
                }
                downloadUrl={attachment?.completed_art_file_url || attachment?.cutout_pro_url || ''}
                imageName={
                  attachment?.completed_art_file_url?.split('/').pop() ||
                  attachment?.cutout_pro_url?.split('/').pop() ||
                  ''
                }
              />
            </Grid>
          </React.Fragment>
        ))}
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              key="image-upload-field"
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              maxFileSizeMB={5}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />
        {/* Actions Section */}
        <Box maxWidth="600px" mx="auto">
          <Grid container spacing={2} justifyContent="center">
            <Grid size={{ xs: 12, sm: 4 }}>
              <Button
                variant="outlined"
                fullWidth
                size="small"
                title="Submit"
                type="submit"
                disabled={isSubmitting}
              />
            </Grid>
          </Grid>
        </Box>
      </form>
    </>
  );
};

export default TemplatePlacementQueue;
