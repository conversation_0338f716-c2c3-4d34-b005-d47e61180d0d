'use client';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material';
import CropNeededQueue from './sub-components/CropNeededQueue';
import CropReviewQueue from './sub-components/CropReviewQueue';
import TemplatePlacementQueue from './sub-components/TemplatePlacementQueue';
import ArtworkQueue from './sub-components/ArtworkQueue';
import LoadingView from '@/components/LoadingView';
import { SingleQueueItem } from '@/types/queues.types';
import useApiCall from '@/hooks/useApiCall';
import { redirect } from 'next/navigation';
import { useAbility } from '@/libs/casl/AbilityContext';
import { getStopPermissionForQueue } from '@/utils/queuePermissionUtils';

const QueueActionWrapper = ({ actionType, queueId }: { actionType: string; queueId: string }) => {
  const ability = useAbility();

  const hasStopPermission = () => {
    const permission = getStopPermissionForQueue(actionType);
    if (!permission) return false;
    return ability?.can(permission.action, permission.target);
  };

  const { data: queueItem, isLoading } = useApiCall<SingleQueueItem>(
    `/workflow-queues/get-queue-item/`,
    'get',
    true,
    { queryParams: { queueId } },
  );
  const {
    isLoading: isLoadingEndAction,
    makeRequest: requestEndAction,
    isSuccess: isSuccessEndAction,
  } = useApiCall<SingleQueueItem>(`/workflow-queues/stop-review/`, 'get', false, {
    queryParams: { queueId },
  });

  if (isSuccessEndAction) {
    redirect(`/ordermanagement/queues?tab=${queueId}`);
  }
  if (isLoading) return <LoadingView />;
  return (
    <Card>
      <CardContent>
        <Typography variant="h5" sx={{ mb: 4 }}>
          {actionType
            .split('-')
            .map(word => word?.charAt(0)?.toUpperCase() + word?.slice(1))
            .join(' ')}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
            alignItems: 'center',
            my: 4,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar>
              {queueItem?.attachments?.assigned_to?.charAt(0) ||
                queueItem?.lineItems?.assigned_to?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="h6">
                {queueItem?.attachments?.assigned_to || queueItem?.lineItems?.assigned_to}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              color="error"
              variant="outlined"
              onClick={async () => {
                if (!queueId) return;
                requestEndAction();
              }}
              disabled={isLoading || isLoadingEndAction || !hasStopPermission()}
              title={!hasStopPermission() ? "You don't have permission to stop this queue" : ''}
              sx={{ width: '100px' }}
            >
              {isLoadingEndAction ? 'Stopping...' : 'Stop'}
            </Button>
          </Box>
        </Box>

        {actionType === 'Crop Needed' && (
          <CropNeededQueue queueItem={queueItem} queueId={queueId} actionType={actionType} />
        )}
        {actionType === 'Crop Review' && (
          <CropReviewQueue queueItem={queueItem} queueId={queueId} actionType={actionType} />
        )}
        {actionType === 'Template Placement' && (
          <TemplatePlacementQueue queueItem={queueItem} queueId={queueId} actionType={actionType} />
        )}
        {actionType === 'Ready For Artwork' && <ArtworkQueue actionType={actionType} />}
        {actionType === 'Artwork Revision' && <ArtworkQueue actionType={actionType} />}
      </CardContent>
    </Card>
  );
};

export default QueueActionWrapper;
