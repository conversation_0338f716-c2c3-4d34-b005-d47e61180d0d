import { useState, useEffect, useCallback } from 'react';
import { Box, Tab, Tabs } from '@mui/material';
import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import GeneralProductTab from './GeneralProductTab';
import ImageProcessingTab from './ImageProcessingTab';
import SkuMappingTab from './SkuMappingTab';
import OrderProcessingTab from './OrderProcessingTab';
import UpsellCrossSellTab from './UpsellCrossSellTab';
import { ProductSku } from '@/types/product-sku';
import ManufacturingVendorTab from './ManufacturingVendorTab';
import NotesTab from './NotesTab';
import PricingAccountingTab from './PricingAccountingTab';
import ShippingFulfillmentTab from './ShippingFulfillmentTab';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { fieldToTabMap } from '@/utils/productValidation';
import ProductInformationTab from './ProductInformationTab';
import apiClient from '@/utils/axios';

interface ProductTabsProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
  errors?: Record<string, string>;
  setErrors?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  setFormValid?: (isValid: boolean) => void;
  originalProductSku?: ProductSku;
  hasChanges?: (hasChanges: boolean) => void;
  onTabChange?: (tab: string) => void;
  parentSkuData?: any[];
  childSkuData?: any[];
  skuRelationshipsUpdated?: number; // Add this prop
}

const ProductTabs = ({
  productSku,
  isEdit = false,
  setProductSku,
  errors = {},
  setErrors = () => {},
  setFormValid = () => {},
  originalProductSku,
  hasChanges = () => {},
  onTabChange,
  parentSkuData = [],
  childSkuData = [],
  skuRelationshipsUpdated = 0, // Default to 0
}: ProductTabsProps) => {
  const [value, setValue] = useState('0');
  const [tabErrors, setTabErrors] = useState<Record<number, boolean>>({});
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [nextTab, setNextTab] = useState<string | null>(null);
  const [forceRenderTimestamp, setForceRenderTimestamp] = useState(Date.now());

  // Add state to track the latest parent/child SKU data
  const [currentParentSkuData, setCurrentParentSkuData] = useState(parentSkuData);
  const [currentChildSkuData, setCurrentChildSkuData] = useState(childSkuData);

  // Fetch updated parent/child SKU data when needed
  const fetchSkuRelationships = useCallback(async () => {
    if (productSku?.id) {
      try {
        const response = await apiClient.get(`/product-sku/${productSku.id}`);
        if (response.data) {
          setCurrentParentSkuData(response.data.parentSku || []);
          setCurrentChildSkuData(response.data.childSku || []);
          setForceRenderTimestamp(Date.now());
        }
      } catch (error) {
        console.error('Failed to fetch SKU relationships:', error);
      }
    }
  }, [productSku?.id]);

  // Update local state when props change
  useEffect(() => {
    setCurrentParentSkuData(parentSkuData);
  }, [parentSkuData]);

  useEffect(() => {
    setCurrentChildSkuData(childSkuData);
  }, [childSkuData]);

  // Fetch updated data when tab changes to General (0)
  useEffect(() => {
    if (value === '0') {
      fetchSkuRelationships();
    }
  }, [value, fetchSkuRelationships]);

  // Fetch updated data when productSku changes
  useEffect(() => {
    if (productSku?.parentSkuIds || productSku?.childSkuIds) {
      fetchSkuRelationships();
    }
  }, [productSku?.parentSkuIds, productSku?.childSkuIds, fetchSkuRelationships]);

  useEffect(() => {
    onTabChange?.(value);
  }, [value, onTabChange]);

  useEffect(() => {
    if (isEdit && value === '9') {
      setValue('0');
      onTabChange?.('0');
    }
  }, [isEdit, value, onTabChange]);

  const tabValueToName: Record<string, string> = {
    '0': 'General',
    '1': 'Product Information',
    '2': 'Image Processing',
    '3': 'SKU Mapping',
    '4': 'Manufacturing',
    '5': 'Order Processing',
    '6': 'Shipping',
    '7': 'Pricing',
    '8': 'Upsell & Cross-Sell',
    '9': 'Notes',
  };

  useEffect(() => {
    if (isEdit && originalProductSku) {
      const hasStateChanges = JSON.stringify(productSku) !== JSON.stringify(originalProductSku);
      hasChanges(hasStateChanges);
    }
  }, [productSku, originalProductSku, hasChanges, isEdit]);

  useEffect(() => {
    const hasTabErrors = Object.values(tabErrors).some(hasError => hasError);
    const hasFieldErrors = Object.keys(errors).length > 0;
    setFormValid(!hasTabErrors && !hasFieldErrors);
  }, [tabErrors, errors, setFormValid]);

  // Check if there are errors in the current tab
  const hasErrorsInCurrentTab = useCallback(
    (tabValue: string) => {
      const currentTabName = tabValueToName[tabValue];

      // Check if any error belongs to the current tab
      return Object.keys(errors).some(fieldName => {
        const tabName = fieldToTabMap[fieldName];
        return tabName === currentTabName;
      });
    },
    [errors],
  );

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    if (hasErrorsInCurrentTab(value)) {
      setNextTab(newValue);
      setConfirmDialogOpen(true);
    } else {
      setValue(newValue);
      onTabChange?.(newValue);
    }
  };

  const handleConfirmTabChange = () => {
    if (nextTab) {
      setValue(nextTab);
      onTabChange?.(nextTab);
      setNextTab(null);
    }
    setConfirmDialogOpen(false);
  };

  const handleTabErrors = useCallback((tabIndex: number, hasErrors: boolean) => {
    setTabErrors(prev => ({
      ...prev,
      [tabIndex]: hasErrors,
    }));
  }, []);

  useEffect(() => {
    if (!isEdit) {
      setForceRenderTimestamp(Date.now());
    }
  }, [isEdit]);

  // Fetch updated data when skuRelationshipsUpdated changes
  useEffect(() => {
    if (skuRelationshipsUpdated > 0 && productSku?.id) {
      fetchSkuRelationships();
    }
  }, [skuRelationshipsUpdated, productSku?.id, fetchSkuRelationships]);

  return (
    <Box sx={{ width: '100%' }}>
      <TabContext value={value}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={value}
            onChange={handleChange}
            aria-label="product tabs"
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '.MuiTabs-scrollButtons': {
                opacity: 1,
                '&.Mui-disabled': {
                  opacity: 0.3,
                },
              },
            }}
          >
            <Tab label="General" value="0" />
            <Tab label="Product Information" value="1" />
            <Tab label="Image Processing" value="2" />
            <Tab label="SKU Mapping" value="3" />
            <Tab label="Manufacturing" value="4" />
            <Tab label="Order Processing" value="5" />
            <Tab label="Shipping" value="6" />
            <Tab label="Pricing" value="7" />
            <Tab label="Upsell & Cross-Sell" value="8" />
            {!isEdit && <Tab label="Notes" value="9" />}
          </Tabs>
        </Box>
        <TabPanel value="0">
          <GeneralProductTab
            key={`general-tab-${forceRenderTimestamp}`}
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
            parentSku={currentParentSkuData}
            childSku={currentChildSkuData}
            setHasFormChanges={hasChanges}
          />
        </TabPanel>
        <TabPanel value="1">
          <ProductInformationTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
          />
        </TabPanel>
        <TabPanel value="2">
          <ImageProcessingTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
            errors={errors}
            setErrors={setErrors}
            setHasErrors={hasErrors => handleTabErrors(2, hasErrors)}
          />
        </TabPanel>
        <TabPanel value="3">
          <SkuMappingTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
            errors={errors}
            setErrors={setErrors}
            setHasErrors={hasErrors => handleTabErrors(3, hasErrors)}
          />
        </TabPanel>
        <TabPanel value="4">
          <ManufacturingVendorTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
          />
        </TabPanel>
        <TabPanel value="5">
          <OrderProcessingTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
          />
        </TabPanel>
        <TabPanel value="6">
          <ShippingFulfillmentTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
          />
        </TabPanel>
        <TabPanel value="7">
          <PricingAccountingTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
          />
        </TabPanel>
        <TabPanel value="8">
          <UpsellCrossSellTab
            productSku={productSku}
            isEdit={isEdit}
            setProductSku={setProductSku}
          />
        </TabPanel>
        <TabPanel value="9">{!isEdit && <NotesTab productSku={productSku} />}</TabPanel>
      </TabContext>

      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Warning: Unsaved Changes"
        message="There are validation errors in the current tab. If you switch tabs now, you may lose some changes. Do you want to continue?"
        confirmLabel="Switch Tab Anyway"
        cancelLabel="Stay on Current Tab"
        confirmColor="primary"
        onConfirm={handleConfirmTabChange}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </Box>
  );
};

export default ProductTabs;
