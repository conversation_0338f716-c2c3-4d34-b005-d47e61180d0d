import React, { useEffect, useState } from 'react';
import {
  Typo<PERSON>,
  <PERSON>,
  TextField,
  Switch,
  Chip,
  Tooltip,
  Paper,
  FormControl,
  Select,
  MenuItem,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { ProductSku } from '@/types/product-sku';
import {
  tabContainerStyle,
  tabTitleStyle,
  rowStyle,
  labelStyle,
  infoIconStyle,
  controlContainerStyle,
} from './styles';
import SearchableSelect from '@/components/SearchableSelect';

interface GeneralProductTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
  parentSku?: Array<{
    id?: string;
    parentSku: {
      id?: string | { key: string; value: string };
      sku: string | null;
    };
  }>;
  childSku?: Array<{
    id?: string;
    childSku: {
      id?: string | { key: string; value: string };
      sku: string | null;
    };
  }>;
  setHasFormChanges?: (hasChanges: boolean) => void;
}

const WORKFLOW_CATEGORIES = [
  { value: 'CROP_IMAGE_ONLY', label: 'Crop Image Only' },
  { value: 'ART_ONLY', label: 'Art Only' },
  { value: 'CUSTOMER_IMAGE_ONLY', label: 'Customer Image Only' },
  { value: 'CROP_IMAGE_AND_TEMPLATE', label: 'Crop Image+Template Placement' },
  { value: 'ART_AND_TEMPLATE', label: 'Art+Template Placement' },
  { value: 'ART_TEMPLATE_AND_APPROVAL', label: 'Art+Template Placement+Approval' },
  { value: 'PLUSH', label: 'Plush' },
  { value: 'ADDON', label: 'Add-on' },
];

const GeneralProductTab = ({
  productSku,
  isEdit = false,
  setProductSku,
  parentSku = [],
  childSku = [],
  setHasFormChanges,
}: GeneralProductTabProps) => {
  const [parentSkuData, setParentSkuData] = useState(parentSku);
  const [childSkuData, setChildSkuData] = useState(childSku);

  useEffect(() => {}, [parentSku]);

  useEffect(() => {}, [childSku]);

  useEffect(() => {
    setParentSkuData(parentSku);
  }, [parentSku]);

  useEffect(() => {
    setChildSkuData(childSku);
  }, [childSku]);

  const handleChange = (key: keyof ProductSku | 'parentSku' | 'childSku', value: any) => {
    if (key === 'parentSku' || key === 'childSku') {
      const valueArray = Array.isArray(value) ? value : [];

      const transformedValue = valueArray.map((item: any) => {
        const id = typeof item === 'object' && item !== null ? item.value : item;
        const sku = typeof item === 'object' && item !== null ? item.key : item;

        if (key === 'parentSku') {
          return {
            id: undefined,
            parentSku: {
              id: id,
              sku: sku,
            },
          };
        } else {
          return {
            id: undefined,
            childSku: {
              id: id,
              sku: sku,
            },
          };
        }
      });

      setProductSku?.(prev => {
        const idKey = `${key}Ids`;
        const allIds = valueArray.map((item: any) =>
          typeof item === 'object' && item !== null ? item.value : item,
        );
        const updated = {
          ...prev,
          [key]: transformedValue,
          [idKey]: allIds,
        };
        if (key === 'parentSku') {
          setParentSkuData(transformedValue as any);
        } else if (key === 'childSku') {
          setChildSkuData(transformedValue as any);
        }
        return updated;
      });
      setHasFormChanges?.(true);
    } else {
      setProductSku?.(prev => {
        const updated = { ...prev, [key]: value };
        return updated;
      });
      setHasFormChanges?.(true);
    }
  };

  const parentSkuField = (
    <Box sx={rowStyle}>
      <Box sx={labelStyle}>
        Parent SKU:
        <Tooltip title="Parent SKU that this product is related to">
          <InfoIcon sx={infoIconStyle} />
        </Tooltip>
      </Box>
      <Box sx={controlContainerStyle}>
        {isEdit ? (
          <SearchableSelect
            attr={{
              key: 'parentSku',
              fetch_db: true,
              filter_key: 'sku',
              type: 'multi_select' as const,
              usage: 'filter' as const,
              backend_key: 'id',
            }}
            value={
              productSku.parentSku && productSku.parentSku.length > 0
                ? productSku.parentSku.map((item: any) => ({
                    key:
                      item.parentSku?.sku || item.key || `SKU-${item.parentSku?.id || item.value}`,
                    value: item.parentSku?.id || item.value,
                  }))
                : (productSku.parentSkuIds || []).map(id => ({
                    key: `SKU-${id}`,
                    value: id,
                  }))
            }
            selectedOptions={
              productSku.parentSku && productSku.parentSku.length > 0
                ? productSku.parentSku.map((item: any) => ({
                    key:
                      item.parentSku?.sku || item.key || `SKU-${item.parentSku?.id || item.value}`,
                    value: item.parentSku?.id || item.value,
                  }))
                : (productSku.parentSkuIds || []).map(id => ({
                    key: `SKU-${id}`,
                    value: id,
                  }))
            }
            handleValueChange={(field: string, newValue: any) => {
              const normalized = (Array.isArray(newValue) ? newValue : []).map((item: any) =>
                typeof item === 'object' && item !== null
                  ? { key: item.key, value: item.value }
                  : { key: `SKU-${item}`, value: item },
              );
              handleChange('parentSku', normalized);
            }}
            multiple={true}
            sx={true}
            customLabel="Parent SKU"
            placeholder="Search for parent SKUs..."
          />
        ) : (
          <Typography>
            {Array.isArray(parentSkuData) && parentSkuData.length > 0
              ? parentSkuData
                  .map(p => p.parentSku?.sku || 'Unknown')
                  .filter(Boolean)
                  .join(', ')
              : 'N/A'}
          </Typography>
        )}
      </Box>
    </Box>
  );

  const childSkuField = (
    <Box sx={rowStyle}>
      <Box sx={labelStyle}>
        Child SKU(s):
        <Tooltip title="Child SKUs that are related to this product">
          <InfoIcon sx={infoIconStyle} />
        </Tooltip>
      </Box>
      <Box sx={controlContainerStyle}>
        {isEdit ? (
          <SearchableSelect
            attr={{
              key: 'childSku',
              fetch_db: true,
              filter_key: 'sku',
              type: 'multi_select' as const,
              usage: 'filter' as const,
              backend_key: 'id',
            }}
            value={
              productSku.childSku && productSku.childSku.length > 0
                ? productSku.childSku.map((item: any) => ({
                    key: item.childSku?.sku || item.key || `SKU-${item.childSku?.id || item.value}`,
                    value: item.childSku?.id || item.value,
                  }))
                : (productSku.childSkuIds || []).map(id => ({
                    key: `SKU-${id}`,
                    value: id,
                  }))
            }
            selectedOptions={
              productSku.childSku && productSku.childSku.length > 0
                ? productSku.childSku.map((item: any) => ({
                    key: item.childSku?.sku || item.key || `SKU-${item.childSku?.id || item.value}`,
                    value: item.childSku?.id || item.value,
                  }))
                : (productSku.childSkuIds || []).map(id => ({
                    key: `SKU-${id}`,
                    value: id,
                  }))
            }
            handleValueChange={(field: string, newValue: any) => {
              const normalized = (Array.isArray(newValue) ? newValue : []).map((item: any) =>
                typeof item === 'object' && item !== null
                  ? { key: item.key, value: item.value }
                  : { key: `SKU-${item}`, value: item },
              );
              handleChange('childSku', normalized);
            }}
            multiple={true}
            sx={true}
            customLabel="Child SKU(s)"
            placeholder="Search for child SKUs..."
          />
        ) : (
          <Typography>
            {Array.isArray(childSkuData) && childSkuData.length > 0
              ? childSkuData
                  .map(c => c.childSku?.sku || 'Unknown')
                  .filter(Boolean)
                  .join(', ')
              : 'N/A'}
          </Typography>
        )}
      </Box>
    </Box>
  );
  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          General Product Information
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Configure basic product details and identification attributes
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            SKU:
            <Tooltip title="Unique identifier for this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            <Typography>{productSku.sku}</Typography>
          </Box>
        </Box>

        {/* Parent SKU field */}
        {parentSkuField}

        {/* Child SKU field */}
        {childSkuField}

        {/* Rush Option field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Rush Option for SKU:
            <Tooltip title="Number of days for rush processing">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <TextField
                type="number"
                value={productSku.rushDays || ''}
                onChange={e => handleChange('rushDays', Number(e.target.value) || null)}
                size="small"
                sx={{ width: 300 }}
                InputProps={{
                  endAdornment: <Typography sx={{ ml: 1 }}>days</Typography>,
                }}
              />
            ) : (
              <Typography>{productSku.rushDays ? `${productSku.rushDays} days` : 'N/A'}</Typography>
            )}
          </Box>
        </Box>

        {/* Workflow category field  */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Workflow Category:
            <Tooltip title="Workflow category">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <FormControl size="small" sx={{ width: 300 }}>
                <Select
                  value={productSku.workflowCategory || ''}
                  onChange={e => handleChange('workflowCategory', e.target.value || null)}
                  displayEmpty
                >
                  <MenuItem value="">
                    <em>Select a category</em>
                  </MenuItem>
                  {WORKFLOW_CATEGORIES.map(category => (
                    <MenuItem key={category.label} value={category.label}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : (
              <Typography>{productSku.workflowCategory || 'N/A'}</Typography>
            )}
          </Box>
        </Box>
        {/* Requires Image Upload field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Requires Image Upload:
            <Tooltip title="Indicates if this product requires customer image uploads">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={Boolean(productSku.requireImageUpload)}
                onChange={e => handleChange('requireImageUpload', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.requireImageUpload ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Rush Available field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Rush Available:
            <Tooltip title="Indicates if rush processing is available for this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <Switch
                checked={Boolean(productSku.hasRush)}
                onChange={e => handleChange('hasRush', e.target.checked)}
              />
            ) : (
              <Typography>{productSku.hasRush ? 'Yes' : 'No'}</Typography>
            )}
          </Box>
        </Box>

        {/* Status field */}
        <Box sx={rowStyle}>
          <Box sx={labelStyle}>
            Status:
            <Tooltip title="Current status of this product">
              <InfoIcon sx={infoIconStyle} />
            </Tooltip>
          </Box>
          <Box sx={controlContainerStyle}>
            {isEdit ? (
              <>
                <Switch
                  checked={Boolean(productSku.isActive)}
                  onChange={e => handleChange('isActive', e.target.checked)}
                />
                <Typography
                  sx={{
                    ml: 1,
                    color: productSku.isActive ? 'success.main' : 'error.main',
                  }}
                >
                  {productSku.isActive ? 'Active' : 'Inactive'}
                </Typography>
              </>
            ) : (
              <Chip
                label={productSku.isActive ? 'Active' : 'Inactive'}
                color={productSku.isActive ? 'success' : 'error'}
                size="small"
                variant="filled"
                sx={{
                  '&.MuiChip-colorSuccess': {
                    backgroundColor: 'var(--mui-palette-success-lightOpacity)',
                    color: 'var(--mui-palette-success-main)',
                  },
                  '&.MuiChip-colorError': {
                    backgroundColor: 'var(--mui-palette-error-lightOpacity)',
                    color: 'var(--mui-palette-error-main)',
                  },
                }}
              />
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default GeneralProductTab;
