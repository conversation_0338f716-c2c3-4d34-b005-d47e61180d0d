import { SxProps, Theme } from "@mui/material";

export const fieldContainerStyle: SxProps<Theme> = {
  display: "flex",
  flexWrap: "wrap",
  alignItems: "center",
  mb: 2,
  gap: 2,
};

export const fieldLabelStyle: SxProps<Theme> = {
  fontWeight: 500,
  width: "220px", 
  flexShrink: 0,
};

export const tabContainerStyle: SxProps<Theme> = {
  p: 6,
};

export const columnContainerStyle: SxProps<Theme> = {
  display: "flex",
  flexDirection: "column",
  gap: 2,
};

export const tabTitleStyle: SxProps<Theme> = {
  mb: 6,
};

export const switchContainerStyle: SxProps<Theme> = {
  display: "flex",
  alignItems: "center",
};

export const rowStyle: SxProps<Theme> = {
  display: "flex",
  alignItems: "center",
    justifyContent: 'space-between',
  gap: 16, 
  mb: 3,
};

export const labelStyle: SxProps<Theme> = {
  display: "flex",
  alignItems: "center",
  width: "400px",
  color: "text.secondary",
  fontWeight: 500,
};

export const infoIconStyle: SxProps<Theme> = {
  fontSize: 16,
  ml: 1,
  color: "action.active",
  opacity: 0.7,
};

export const controlContainerStyle: SxProps<Theme> = {
  ml: 10,
};

export const responsiveRowStyle: SxProps<Theme> = {
  ...rowStyle,
  flexDirection: { xs: "column", sm: "row" },
  alignItems: { xs: "flex-start", sm: "center" },
};

export const responsiveLabelStyle: SxProps<Theme> = {
  ...labelStyle,
  width: { xs: "100%", sm: "400px" },
  mb: { xs: 1, sm: 0 },
};

export const responsiveControlContainerStyle: SxProps<Theme> = {
  ...controlContainerStyle,
  width: { xs: "100%", sm: "auto" },
};
