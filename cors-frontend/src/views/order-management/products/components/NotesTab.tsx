'use client';

import { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  TablePagination,
} from '@mui/material';
import { KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';
import { fetchHistorydata } from '@/actions';
import { ProductSku } from '@/types/product-sku';
import { useRouter, useSearchParams } from 'next/navigation';

interface NotesTabProps {
  productSku: ProductSku;
}

interface Change {
  field: string;
  oldValue: any;
  newValue: any;
}

interface HistoryEntry {
  id: string;
  createdAt: string;
  userName: string | null;
  changes: Change[];
}

const formatValue = (value: any) => {
  if (value === null) return 'null';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

const formatFieldName = (field: string): string => {
  console.log('here is a field', field);
  const excludedFields = ['updatedAt', 'createdAt', 'id'];
  if (excludedFields.includes(field)) return '';

  // Special case for canUpSold ,  products category
  if (field === 'canUpSold') return 'Can Be Upsold';
  if (field === 'products.category') return 'Products Category';

  return field
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

const Row = ({ row }: { row: HistoryEntry }) => {
  const [open, setOpen] = useState(false);

  const filteredChanges = row.changes.filter(
    change => !['updatedAt', 'createdAt', 'id'].includes(change.field),
  );
  return (
    <>
      <TableRow>
        <TableCell>
          <IconButton size="small" onClick={() => setOpen(!open)}>
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
          </IconButton>
        </TableCell>
        <TableCell>{new Date(row.createdAt).toLocaleString()}</TableCell>
        <TableCell>{row.userName || 'System'}</TableCell>
        <TableCell>{filteredChanges.length} Change(s)</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={4}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box margin={2}>
              <Typography variant="subtitle1">Changes</Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Field</TableCell>
                    <TableCell>Old Value</TableCell>
                    <TableCell>New Value</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredChanges.map((change, idx) => (
                    <TableRow key={idx}>
                      <TableCell>{formatFieldName(change.field)}</TableCell>
                      <TableCell>{formatValue(change.oldValue)}</TableCell>
                      <TableCell>{formatValue(change.newValue)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
};

const NotesTab = ({ productSku }: NotesTabProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const pageQuery = parseInt(searchParams.get('page') || '1', 10);
  const limitQuery = parseInt(searchParams.get('limit') || '25', 10);

  const [historyData, setHistoryData] = useState<HistoryEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);

  const page = pageQuery;
  const pageSize = limitQuery;

  useEffect(() => {
    const fetchHistory = async () => {
      setLoading(true);
      try {
        const { data, total } = await fetchHistorydata(productSku.id, page, pageSize);

        const formattedData: HistoryEntry[] = (data ?? []).map((entry: any) => ({
          id: entry.id || crypto.randomUUID(),
          createdAt: entry.createdAt,
          userName: entry.user ? `${entry.user.firstName} ${entry.user.lastName}` : null,
          changes: entry.changes,
        }));

        setHistoryData(formattedData);
        setTotalCount(total ?? 0);
      } catch (error) {
        console.error(error);
        setHistoryData([]);
        setTotalCount(0);
      } finally {
        setLoading(false);
      }
    };

    if (productSku.id) {
      fetchHistory();
    }
  }, [productSku.id, page, pageSize]);

  const updateUrlParams = (newPage: number, newLimit: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', String(newPage + 1));
    params.set('limit', String(newLimit));
    router.replace(`?${params.toString()}`, { scroll: false });
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    updateUrlParams(newPage, pageSize);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10);
    updateUrlParams(0, newLimit);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3 }}>
        Notes History
      </Typography>
      {loading ? (
        <CircularProgress />
      ) : historyData.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            p: 4,
            bgcolor: 'background.paper',
            borderRadius: 1,
          }}
        >
          <Typography variant="body1" color="text.secondary">
            No notes history available
          </Typography>
        </Box>
      ) : (
        <Paper>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell />
                <TableCell>Date & Time</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Changes</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {historyData.map(row => (
                <Row key={row.id} row={row} />
              ))}
            </TableBody>
          </Table>
          <TablePagination
            rowsPerPageOptions={[25, 50, 100, 500]}
            component="div"
            count={totalCount}
            rowsPerPage={pageSize}
            page={pageQuery - 1}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}
    </Box>
  );
};

export default NotesTab;
