import {
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Tooltip,
} from '@mui/material';
import { ProductSku } from '@/types/product-sku';
import InfoIcon from '@mui/icons-material/Info';
import { ProductCategory } from '@/constants/product.constants';
import { tabContainerStyle, tabTitleStyle, infoIconStyle } from './styles';

interface ProductInformationTabProps {
  productSku: ProductSku;
  isEdit?: boolean;
  setProductSku?: React.Dispatch<React.SetStateAction<ProductSku>>;
}

const ProductInformationTab = ({
  productSku,
  isEdit = false,
  setProductSku,
}: ProductInformationTabProps) => {
  const handleProductChange = (index: number, field: string, value: any) => {
    if (isEdit && setProductSku) {
      setProductSku(prev => {
        const updatedProducts = [...(prev.products || [])];
        updatedProducts[index] = {
          ...updatedProducts[index],
          [field]: value,
        };
        return {
          ...prev,
          products: updatedProducts,
        };
      });
    }
  };

  return (
    <Box sx={tabContainerStyle}>
      <Paper elevation={0} sx={{ p: 3, mb: 6, bgcolor: 'background.default' }}>
        <Typography variant="h6" sx={tabTitleStyle}>
          Product Information
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Manage product details including name, category, and description
        </Typography>
      </Paper>

      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell width="25%">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  Product Name
                  <Tooltip title="Name of the product">
                    <InfoIcon sx={{ ...infoIconStyle, ml: 1 }} />
                  </Tooltip>
                </Box>
              </TableCell>
              <TableCell width="20%">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  Category
                  <Tooltip title="Product category">
                    <InfoIcon sx={{ ...infoIconStyle, ml: 1 }} />
                  </Tooltip>
                </Box>
              </TableCell>
              <TableCell width="45%">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  Description
                  <Tooltip title="Detailed description of the product">
                    <InfoIcon sx={{ ...infoIconStyle, ml: 1 }} />
                  </Tooltip>
                </Box>
              </TableCell>
              {/* {isEdit && <TableCell width="10%">Actions</TableCell>} */}
            </TableRow>
          </TableHead>
          <TableBody>
            {productSku.products && productSku.products.length > 0 ? (
              productSku.products.map((product, index) => (
                <TableRow key={product.id || index}>
                  <TableCell>
                    {isEdit ? (
                      <TextField
                        fullWidth
                        size="small"
                        value={product.name || ''}
                        onChange={e => handleProductChange(index, 'name', e.target.value)}
                        placeholder="Enter product name"
                      />
                    ) : (
                      <Typography>{product.name || 'N/A'}</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    {isEdit ? (
                      <FormControl fullWidth size="small">
                        <Select
                          value={product.category || ''}
                          onChange={e => handleProductChange(index, 'category', e.target.value)}
                          displayEmpty
                        >
                          {Object.values(ProductCategory).map(category => (
                            <MenuItem key={category} value={category}>
                              {category}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    ) : (
                      <Typography>{product.category || 'N/A'}</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    {isEdit ? (
                      <TextField
                        fullWidth
                        size="small"
                        multiline
                        rows={2}
                        value={product.description || ''}
                        onChange={e => handleProductChange(index, 'description', e.target.value)}
                        placeholder="Enter product description"
                      />
                    ) : (
                      <Typography>{product.description || 'N/A'}</Typography>
                    )}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={isEdit ? 4 : 3} align="center">
                  <Typography color="text.secondary">No products found</Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ProductInformationTab;
