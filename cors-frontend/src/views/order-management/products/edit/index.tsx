'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import apiClient from '@/utils/axios';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Divider,
  CircularProgress,
  Skeleton,
} from '@mui/material';
import Button from '@/components/Button';
import ProductTabs from '../components/ProductTabs';
import { ProductSku } from '@/types/product-sku';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import { toast } from 'react-toastify';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { getFormattedErrors } from '@/utils/productValidation';

interface ProductSkuEditProps {
  productData: ProductSku;
  isEdit: boolean;
}

const ProductSkuEdit = ({ productData, isEdit }: ProductSkuEditProps) => {
  const router = useRouter();

  // Helper function to ensure numeric values
  const ensureNumber = (value: any): number | null => {
    if (!value) return null;
    const num = Number(value);
    return isNaN(num) ? null : num;
  };

  // Helper to normalize IDs to string arrays
  const normalizeIds = (ids: any) => {
    if (!ids) return [];
    if (Array.isArray(ids)) {
      return ids.map((item: any) => {
        if (typeof item === 'string') return item;
        if (item && typeof item === 'object') {
          if ('value' in item) return item.value;
          if ('id' in item && typeof item.id === 'object' && 'value' in item.id)
            return item.id.value;
          if ('id' in item) return item.id;
        }
        return String(item);
      });
    }
    return [];
  };

  // List of shipping-related numeric fields
  const shippingNumericFields = [
    'shippingWeight',
    'productLength',
    'productWidth',
    'productHeight',
  ] as const;

  // Transform API response data
  const transformApiData = (data: any): ProductSku => {
    const transformed = { ...data };
    shippingNumericFields.forEach(field => {
      const originalValue = data[field];
      const transformedValue = ensureNumber(originalValue);
    });
    // Normalize parentSkuIds and childSkuIds to arrays of strings
    transformed.parentSkuIds = normalizeIds(data.parentSkuIds);
    transformed.childSkuIds = normalizeIds(data.childSkuIds);
    return transformed;
  };

  // Initialize state with transformed data
  const [productSku, setProductSku] = useState<ProductSku>(() => {
    return transformApiData(productData);
  });
  const [originalProductSku, setOriginalProductSku] = useState<ProductSku>(() => {
    return transformApiData(productData);
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isFormValid, setIsFormValid] = useState(true);
  const [loading, setLoading] = useState(false);
  const [hasFormChanges, setHasFormChanges] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState<'cancel' | 'back' | null>(null);
  const [edit, setEdit] = useState(isEdit);
  const [skuRelationshipsUpdated, setSkuRelationshipsUpdated] = useState(0);

  const productSkuRef = useRef(productSku);
  useEffect(() => {
    productSkuRef.current = productSku;
  }, [productSku]);

  // Update state when productData changes
  useEffect(() => {
    const transformedData = transformApiData(productData);
    setProductSku(transformedData);
    setOriginalProductSku(transformedData);
  }, [productData]);

  const handleSave = async () => {
    try {
      if (!isFormValid) {
        const formattedErrors = getFormattedErrors(errors);
        if (formattedErrors.length > 0) {
          const firstError = formattedErrors[0];
          toast.error(
            `Please fix validation error: "${firstError.readableField}" in ${firstError.tab} tab - ${firstError.message}`,
          );
        } else {
          toast.error('Please fix all validation errors before saving');
        }
        return;
      }

      if (!hasFormChanges) {
        toast.info('No changes to save');
        return;
      }

      setLoading(true);

      // Use the latest productSku from the ref
      const latestProductSku = productSkuRef.current;

      // Create a new object for changed fields
      const changedFields: Partial<ProductSku> = {};
      let hasChanges = false;

      // First, handle non-shipping fields
      Object.keys(latestProductSku).forEach(key => {
        if (
          key === 'parentSku' ||
          key === 'childSku' ||
          shippingNumericFields.includes(key as (typeof shippingNumericFields)[number]) ||
          key === 'parentSkuIds' ||
          key === 'childSkuIds'
        ) {
          return;
        }

        const currentValue = latestProductSku[key as keyof ProductSku];
        const originalValue = originalProductSku[key as keyof ProductSku];

        // Skip if values are the same
        if (JSON.stringify(currentValue) === JSON.stringify(originalValue)) return;

        (changedFields as any)[key] = currentValue;
        hasChanges = true;
      });

      // Handle parentSkuIds and childSkuIds specifically (normalize to string arrays)
      const currentParentIds = normalizeIds(latestProductSku.parentSkuIds);
      const originalParentIds = normalizeIds(originalProductSku.parentSkuIds);
      if (JSON.stringify(currentParentIds) !== JSON.stringify(originalParentIds)) {
        (changedFields as any).parentSkuIds = currentParentIds;
        hasChanges = true;
      }

      const currentChildIds = normalizeIds(latestProductSku.childSkuIds);
      const originalChildIds = normalizeIds(originalProductSku.childSkuIds);
      if (JSON.stringify(currentChildIds) !== JSON.stringify(originalChildIds)) {
        (changedFields as any).childSkuIds = currentChildIds;
        hasChanges = true;
      }

      // Handle shipping fields separately
      shippingNumericFields.forEach(field => {
        const currentValue = latestProductSku[field];
        const originalValue = originalProductSku[field];

        // Only include if the value has changed
        if (currentValue !== originalValue) {
          const numValue = ensureNumber(currentValue);
          if (numValue) {
            (changedFields as any)[field] = numValue;
            hasChanges = true;
          }
        }
      });

      if (!hasChanges) {
        toast.info('No changes to save');
        setLoading(false);
        return;
      }

      try {
        let patchPayload;
        if (changedFields.products) {
          patchPayload = { productsToUpdate: changedFields.products };
        } else {
          patchPayload = changedFields;
        }
        const response = await apiClient.patch(`/product-sku/${productData.id}`, patchPayload);
        toast.success('Product SKU updated successfully');

        const latestData = await apiClient.get(`/product-sku/${productData.id}`);
        if (latestData.data) {
          const transformedData = transformApiData(latestData.data);
          setProductSku(transformedData);
          setOriginalProductSku(transformedData);
          setSkuRelationshipsUpdated(prev => prev + 1);
        }

        setHasFormChanges(false);
        setEdit(false);
      } catch (error) {
        toast.error('Failed to update product SKU');
      } finally {
        setLoading(false);
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
      setLoading(false);
    }
  };

  const handleBackClick = () => {
    if (hasFormChanges) {
      setConfirmDialogOpen(true);
      setActionToConfirm('back');
    } else {
      setEdit(!edit);
      setHasFormChanges(false);
    }
  };

  const handleConfirmAction = () => {
    setConfirmDialogOpen(false);
    if (actionToConfirm === 'cancel') {
      setEdit(false);
    } else if (actionToConfirm === 'back') {
      setEdit(false);
      setHasFormChanges(false);
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" marginTop={2} marginBottom={2}>
          Edit Product Details (pims)
        </Typography>
        <Box
          sx={{
            display: 'flex',
            justifyContent: edit ? 'space-between' : 'flex-end',
            alignItems: 'center',
            mb: 4,
            gap: 2,
          }}
        >
          {edit && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                title="Back to View"
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={handleBackClick}
                sx={{ mr: 2 }}
              >
                Back
              </Button>
            </Box>
          )}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              title={edit ? 'Cancel' : 'Edit'}
              variant={edit ? 'outlined' : 'contained'}
              onClick={handleBackClick}
              disabled={loading || !hasFormChanges}
            >
              {edit ? 'Cancel' : 'Edit'}
            </Button>
            <Button
              title={edit ? 'Save' : 'Back'}
              ButtonAction={Actions.EditDetailPage}
              actionTarget={ActionsTarget.PIMS}
              variant={edit ? 'contained' : 'outlined'}
              onClick={
                edit
                  ? handleSave
                  : () => {
                      router.back();
                    }
              }
              disabled={loading || !isFormValid || !hasFormChanges}
            >
              {loading ? (
                <>
                  <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 8 }} />
        {productSku ? (
          <ProductTabs
            productSku={productSku}
            isEdit={edit}
            setProductSku={setProductSku}
            errors={errors}
            setErrors={setErrors}
            setFormValid={setIsFormValid}
            originalProductSku={originalProductSku}
            hasChanges={setHasFormChanges}
            parentSkuData={productData.parentSku}
            childSkuData={productData.childSku}
            skuRelationshipsUpdated={skuRelationshipsUpdated}
          />
        ) : (
          <Skeleton variant="rectangular" height={400} />
        )}
      </CardContent>

      {/* Use the ConfirmationDialog component */}
      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Unsaved Changes"
        message={`You have unsaved changes. If you ${actionToConfirm === 'cancel' ? 'cancel' : 'go back'} now, all your changes will be lost. Do you want to continue?`}
        confirmLabel="Discard Changes"
        cancelLabel="Stay on Page"
        confirmColor="primary"
        onConfirm={handleConfirmAction}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </Card>
  );
};

export default ProductSkuEdit;
