'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { Tab, Tabs, Typography, Box, Chip, Button } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { useSettings } from '@core/hooks/useSettings';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/redux-store';
import { generateDynamicColumns } from '../ProductSkuListTable';
import DataTable from '@/components/Datatable';
import { filterOrder, normalizeFilterValues, fetchOrders } from '@/actions/orders';
import { setOrders, setOrdersFilters } from '@/redux-store/stores/orders.store';
import { useRouter } from 'next/navigation';
import StatusChip from '@/components/StatusChip';
import { toast } from 'react-toastify';
import SyncIcon from '@mui/icons-material/Sync';
import apiClient from '@/utils/axios';
import { Add } from '@mui/icons-material';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

const OrdersWrapper = ({ page = 0, limit = 25, data }: { page?: any; limit: any; data: any }) => {
  const router = useRouter();
  const { updatePageSettings } = useSettings();
  const dispatch = useDispatch<AppDispatch>();
  const ability = useAbility();
  const { unflagged, flagged, filters } = useSelector((state: RootState) => state.orders);
  const ordersTableField = useSelector((state: RootState) => state.common.ordersTableConfig);
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [filtersApplied, setFiltersApplied] = useState(false);
  const [pageState, setPageState] = useState(1);

  useEffect(() => {
    const cleanup = updatePageSettings({ skin: 'default' });

    // Only set server-side data if there are no existing filters
    // This prevents overriding filtered data when returning from detail pages
    if (data && (!filters || filters.length === 0)) {
      dispatch(setOrders(data));
    }

    const url = new URL(window.location.href);
    url.searchParams.set('page', pageState.toString());
    url.searchParams.set('limit', limit.toString());
    window.history.replaceState({}, '', url.toString());

    return cleanup;
  }, [data, dispatch, updatePageSettings, pageState, limit, filters.length]);

  useEffect(() => {
    // Fetch orders on filters, page, limit, or tab change
    const flatFilters = Array.isArray(filters) ? filters.flat() : [];
    const currentPage = Math.max(1, Number(pageState));
    const currentLimit = Number(limit);
    const isFlagged = activeTab === 1;
    const tabLabel = isFlagged ? 'flagged' : 'unflagged';

    const fetchData = async () => {
      if (flatFilters.length > 0) {
        // Use advanced filter endpoint
        const payload = {
          filters: filters.map((group: any) => [
            ...group,
            { attribute: 'flagged', operator: 'eq', value: isFlagged },
          ]),
          page: currentPage,
          limit: currentLimit,
        };
        const result = await filterOrder(payload);
        dispatch(
          setOrders({
            [tabLabel]: {
              data: Array.isArray(result.data) ? [...result.data] : [],
              count: result.count || 0,
            },
          }),
        );
      } else {
        const fq = `flagged:eq:${isFlagged}`;
        const result = await fetchOrders({ page: currentPage, limit: currentLimit, fq });
        dispatch(
          setOrders({
            [tabLabel]: {
              data: Array.isArray(result.data) ? [...result.data] : [],
              count: result.count || 0,
            },
          }),
        );
      }
    };
    fetchData();
  }, [filters, pageState, limit, activeTab, dispatch]);

  // Tab change handler
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleRowClick = (row: any, e?: React.MouseEvent) => {
    if (ability?.can(Actions.EditOrderDetialPage, ActionsTarget.ORDERS)) {
      const path = `/ordermanagement/orders/view/${row.id}`;
      if (e && (e.ctrlKey || e.metaKey)) {
        window.open(path, '_blank', 'noopener,noreferrer');
      } else {
        router.push(path);
      }
    }
  };

  // Process dynamic columns
  const processedDynamicColumns = useMemo(() => {
    return ordersTableField?.fields
      ? generateDynamicColumns(
          ordersTableField.fields.map(field => {
            if (field.key === 'shopifyOrderNumber') {
              // Create a custom cell renderer for shopifyOrderNumber
              return {
                ...field,
                cell: (info: { getValue: () => any; row: any }) => {
                  const value = info.getValue();
                  const canNavigate = ability?.can(
                    Actions.EditOrderDetialPage,
                    ActionsTarget.ORDERS,
                  );

                  return (
                    <Typography
                      sx={{
                        cursor: canNavigate ? 'pointer' : 'default',
                        color: 'primary.main',
                        '&:hover': {
                          textDecoration: canNavigate ? 'underline' : 'none',
                        },
                      }}
                      onClick={() => {
                        if (canNavigate) {
                          router.push(`/ordermanagement/orders/view/${info.row.original.id}`);
                        }
                      }}
                    >
                      {value ?? '-'}
                    </Typography>
                  );
                },
              };
            }

            if (field.key === 'customerFirstName' || field.key === 'customerLastName') {
              return { ...field, hidden: true };
            }

            if (field.key === 'orderDate') {
              return {
                ...field,
                cell: (info: { getValue: () => any }) => {
                  const date = info.getValue();
                  if (!date) return '-';

                  try {
                    const dateObj = new Date(date);
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                  } catch (error) {
                    return date;
                  }
                },
              };
            }

            return field;
          }),
          router,
        )
      : [];
  }, [ordersTableField?.fields, router]);

  const tableColumns = useMemo(
    () => [
      {
        accessorKey: 'shopifyOrderNumber',
        header: 'Order Number',
        enableSorting: false,
        cell: (info: { getValue: () => any; row: any }) => {
          const value = info.getValue();
          const canNavigate = ability?.can(Actions.EditOrderDetialPage, ActionsTarget.ORDERS);
          const path = `/ordermanagement/orders/view/${info.row.original.id}`;

          return (
            <Typography
              sx={{
                cursor: canNavigate ? 'pointer' : 'default',
                color: 'primary.main',
                '&:hover': {
                  textDecoration: canNavigate ? 'underline' : 'none',
                },
              }}
              onClick={e => {
                e.stopPropagation();
                if (canNavigate) {
                  router.push(path);
                }
              }}
            >
              {value ?? '-'}
            </Typography>
          );
        },
      },
      {
        accessorFn: (row: any) =>
          `${row.customerFirstName || ''} ${row.customerLastName || ''}`.trim(),
        id: 'customerName',
        header: 'Customer Name',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => (
          <Typography noWrap sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {info.getValue() || '-'}
          </Typography>
        ),
      },
      {
        accessorKey: 'customerEmail',
        header: 'Email',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => (
          <Typography noWrap sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {info.getValue() || '-'}
          </Typography>
        ),
      },
      {
        accessorKey: 'orderDate',
        header: 'Order Date',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => {
          const date = info.getValue();
          if (!date) return '-';
          try {
            const dateObj = new Date(date);
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
          } catch (error) {
            return '-';
          }
        },
      },
      {
        accessorKey: 'itemCount',
        header: 'No of items',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => <Typography>{info.getValue() || '-'}</Typography>,
      },
      {
        accessorKey: 'priorities',
        header: 'Priorities',
        enableSorting: false,
        meta: {
          filterType: 'multi_select',
        },
        cell: (info: { getValue: () => any }) => {
          const priorities = info.getValue();
          return Array.isArray(priorities) && priorities.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {priorities.map((priority: string, index: number) => (
                <Chip
                  key={`${priority}-${index}`}
                  label={priority}
                  size="small"
                  sx={{
                    backgroundColor: priority.toUpperCase().includes('HOLIDAY')
                      ? '#ffcccc'
                      : priority.toUpperCase().includes('RUSH')
                        ? '#ffe0b2'
                        : '#ccc',
                    color: '#000',
                  }}
                />
              ))}
            </Box>
          ) : (
            <Typography variant="body2">No priorities</Typography>
          );
        },
      },
      {
        accessorFn: (row: any) => {
          if (!row.statusUpdatedAt) return null;
          const statusDate = new Date(row.statusUpdatedAt);
          const currentDate = new Date();
          const statusDateOnly = new Date(statusDate.setHours(0, 0, 0, 0));
          const currentDateOnly = new Date(currentDate.setHours(0, 0, 0, 0));
          const diffTime = Math.abs(currentDateOnly.getTime() - statusDateOnly.getTime());
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          return diffDays;
        },
        id: 'daysSinceStatusUpdate',
        header: 'Days Since Status Update',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => {
          const days = info.getValue();
          if (days === null) return <Typography>-</Typography>;
          let color = 'text.primary';
          if (days > 7) color = 'warning.main';
          if (days > 14) color = 'error.main';

          return <Typography color={color}>{days} days</Typography>;
        },
      },
      {
        accessorKey: 'orderStatus',
        header: 'Status',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => (
          <StatusChip status={info.getValue()} variant="order" size="small" />
        ),
      },
    ],
    [processedDynamicColumns],
  );

  // Get current tab data
  const currentTabData = useMemo(() => {
    return activeTab === 0 ? unflagged?.data || [] : flagged?.data || [];
  }, [activeTab, unflagged, flagged]);

  const currentTabCount = useMemo(() => {
    return activeTab === 0 ? unflagged?.count || 0 : flagged?.count || 0;
  }, [activeTab, unflagged?.count, flagged?.count]);

  // Filter handler
  const handleApplyFilters = async (normalizedFilters: any) => {
    try {
      setLoading(true);
      const isReset = !normalizedFilters || normalizedFilters.length === 0;

      if (isReset) {
        setFiltersApplied(false);
        dispatch(setOrdersFilters([]));
        setPageState(1); // Reset page state
        // Reset page to 1 in the URL
        const url = new URL(window.location.href);
        url.searchParams.set('page', '1');
        window.history.replaceState({}, '', url.toString());
        setLoading(false);
        return null;
      } else {
        setFiltersApplied(true);

        // Dispatch filters to Redux first
        dispatch(setOrdersFilters(normalizedFilters));

        const processedFilters = normalizedFilters
          .map((group: any[]) =>
            group
              .map((filter: any) => {
                if (filter.attribute === 'orderDate') {
                  return {
                    ...filter,
                    operator: 'between',
                    value:
                      filter.value && typeof filter.value === 'object'
                        ? { start: filter.value.start, end: filter.value.end }
                        : { start: '', end: '' },
                  };
                }
                return filter;
              })
              .filter((filter: any) => {
                if (!filter.attribute || !filter.operator) return false;
                if (filter.attribute === 'orderDate') {
                  return (
                    filter.value &&
                    typeof filter.value === 'object' &&
                    filter.value.start &&
                    filter.value.end
                  );
                }
                return filter.value !== undefined && filter.value !== null && filter.value !== '';
              }),
          )
          .filter((group: any[]) => group.length > 0);

        const normalizedProcessedFilters = normalizeFilterValues(processedFilters);

        const unflaggedPayload = {
          filters: normalizedProcessedFilters.map((group: any) => [
            ...group,
            { attribute: 'flagged', operator: 'eq', value: false },
          ]),
          page: Math.max(1, Number(pageState)),
          limit: Number(limit),
        };

        const flaggedPayload = {
          filters: normalizedProcessedFilters.map((group: any) => [
            ...group,
            { attribute: 'flagged', operator: 'eq', value: true },
          ]),
          page: Math.max(1, Number(pageState)),
          limit: Number(limit),
        };

        const [unflaggedResult, flaggedResult] = await Promise.all([
          filterOrder(unflaggedPayload),
          filterOrder(flaggedPayload),
        ]);

        dispatch(
          setOrders({
            unflagged: {
              data: Array.isArray(unflaggedResult.data) ? [...unflaggedResult.data] : [],
              count: unflaggedResult.count || 0,
            },
            flagged: {
              data: Array.isArray(flaggedResult.data) ? [...flaggedResult.data] : [],
              count: flaggedResult.count || 0,
            },
          }),
        );

        setLoading(false);
        return activeTab === 0 ? unflaggedResult : flaggedResult;
      }
    } catch (error) {
      setLoading(false);
      toast.error('Filter operation failed');
      return null;
    }
  };

  const handleSyncOrders = async () => {
    try {
      setLoading(true);
      const response = await apiClient.post('/orders/resync_orders');
      const { message } = response.data;
      toast.success(message || 'Order sync initiated successfully. This may take a few minutes.');
    } catch (error) {
      toast.error('Failed to sync orders. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
          <Typography variant="h4">Orders</Typography>
          <div className="flex justify-center items-center gap-2">
            {activeTab === 0 && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<SyncIcon />}
                onClick={handleSyncOrders}
                disabled={loading}
              >
                Sync Orders
              </Button>
            )}
            <Button
              variant="contained"
              color="primary"
              startIcon={<Add />}
              onClick={() => router.push('./orders/add')}
              disabled={loading}
            >
              Add Order
            </Button>
          </div>
        </Grid>
        <Grid size={{ xs: 12 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="order tabs"
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '.MuiTabs-scrollButtons': {
                opacity: 1,
                '&.Mui-disabled': { opacity: 0.3 },
              },
            }}
          >
            <Tab label="Orders" value={0} />
            <Tab label="Flagged Orders" value={1} />
          </Tabs>

          <DataTable
            key="orders-table-persistent"
            filterFields={ordersTableField.filterDataFields}
            title="Filters Orders"
            storeName="orders"
            onApplyFilters={handleApplyFilters}
            filterCount={filters.flat().length}
            totalCount={currentTabCount}
            enableFilters={true}
            columns={tableColumns}
            data={currentTabData}
            onRowClick={row => handleRowClick(row, window.event as unknown as React.MouseEvent)}
            loading={loading}
            page={Math.max(0, Number(pageState) - 1)}
            limit={Number(limit)}
            endpoint="ordermanagement/orders"
            onLimitChange={(newLimit: number) => {
              const url = new URL(window.location.href);
              url.searchParams.set('limit', newLimit.toString());
              url.searchParams.set('page', '1');
              window.location.href = url.toString();
            }}
            onPageChange={newPage => setPageState(newPage + 1)}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default OrdersWrapper;
