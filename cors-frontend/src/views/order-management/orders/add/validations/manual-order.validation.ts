import * as yup from 'yup';

const getCustomizer = (product: any) => {
  return product?.metadata?.product_customizer
    ? JSON.parse(product.metadata.product_customizer)
    : [];
};

// Helper function to get required product options for a specific pet type
const getRequiredProductOptions = (product: any, petType: string) => {
  const customizer = getCustomizer(product);
  const productOptionsSection = customizer.find((section: any) => section.product_options);

  if (!productOptionsSection?.product_options) return [];

  const requiredOptions: string[] = [];

  productOptionsSection.product_options.forEach((optionSection: any) => {
    if (optionSection[petType]) {
      Object.entries(optionSection[petType]).forEach(([key, value]: [string, any]) => {
        if (value?.display === true) {
          requiredOptions.push(key.toLowerCase());
        }
      });
    }
  });

  return requiredOptions;
};

const baseOrderItemSchema = yup.object().shape({
  sku: yup.object().nullable().required('SKU is required'),
  product: yup.object().nullable().required('Product is required'),
  quantity: yup.number().min(1, 'Quantity must be at least 1').required('Quantity is required'),
});

const customizerOrderItemSchema = baseOrderItemSchema.shape({
  _pet_type: yup.string().required('Pet type is required'),
  _pet_name: yup.string().required('Pet name is required'),
  _pet_age: yup.string().required('Pet age is required'),
  _pet_breed_specie: yup.string().required('Pet breed/specie is required'),
  petImagesCustomizer: yup
    .array()
    .of(
      yup
        .string()
        .required('Pet image URL is required')
        .test('nonEmpty', 'Pet image URL cannot be empty', value => {
          return value !== null && value !== undefined && value.trim() !== '';
        }),
    )
    .min(3, 'At least three images are required')
    .required('Images are required'),
  customizer_questions: yup
    .array()
    .of(
      yup.object().shape({
        label: yup.string().required('Question label is required'),
        value: yup.string().required('Question value is required'),
      }),
    )
    .min(0)
    .optional(),

  uniqueCharacteristics: yup
    .array()
    .of(
      yup.object().shape({
        description: yup
          .string()
          .required('Unique characteristic description is required')
          .max(500, 'Description must be 500 characters or less'),
        image: yup
          .array()
          .of(
            yup
              .string()
              .required('Image URL is required')
              .test('nonEmpty', 'Image URL cannot be empty', value => {
                return value !== null && value !== undefined && value.trim() !== '';
              }),
          )
          .min(1, 'At least one image is required')
          .max(1, 'Only one image is allowed')
          .required('Image is required'),
      }),
    )
    .min(0)
    .optional(),

  left_eye_color: yup.string().when('product', {
    is: (product: any) => {
      const customizer = getCustomizer(product);
      const petDetailSection = customizer.find((section: any) => section.pet_info);
      const eyesExist = petDetailSection?.pet_info?.find((sec: any) => sec?.eye_types);
      return eyesExist ? true : false;
    },
    then: schema => schema.required('Eye color is required'),
    otherwise: schema => schema.optional(),
  }),
  right_eye_color: yup.string().when(['product', 'differentEyeColors'], {
    is: (product: any, differentEyeColors: any) => {
      const customizer = getCustomizer(product);
      const petDetailSection = customizer.find((section: any) => section.pet_info);
      const eyesExist = petDetailSection?.pet_info?.find((sec: any) => sec?.eye_types);
      return eyesExist && differentEyeColors ? true : false;
    },
    then: schema => schema.required('Eye color is required'),
    otherwise: schema => schema.optional(),
  }),
  left_ear_position: yup.string().when(['product', '_pet_type'], {
    is: (product: any, _pet_type: string) => {
      const customizer = getCustomizer(product);
      const petDetailSection = customizer.find((section: any) => section.pet_info);
      const earExist = petDetailSection?.pet_info?.find((sec: any) => _pet_type in sec)?.[_pet_type]
        ?.ear_position?.Left;
      return earExist ? true : false;
    },
    then: schema => schema.required('Left ear position is required'),
    otherwise: schema => schema.optional(),
  }),
  right_ear_position: yup.string().when(['product', '_pet_type'], {
    is: (product: any, _pet_type: string) => {
      const customizer = getCustomizer(product);
      const petDetailSection = customizer.find((section: any) => section.pet_info);
      const earExist = petDetailSection?.pet_info?.find((sec: any) => _pet_type in sec)?.[_pet_type]
        ?.ear_position?.Right;
      return earExist ? true : false;
    },
    then: schema => schema.required('Right ear position is required'),
    otherwise: schema => schema.optional(),
  }),

  // Dynamic product options validation
  productOptions: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string().required('Option name is required'),
        value: yup.string().required('Option value is required'),
      }),
    )
    .when(['product', '_pet_type'], {
      is: (product: any, petType: string) => {
        return product && petType;
      },
      then: schema => {
        return schema.test(
          'dynamic-product-options',
          'All required product options must be selected',
          function (value) {
            const { product, _pet_type: petType } = this.parent;
            const requiredOptions = getRequiredProductOptions(product, petType);

            if (requiredOptions.length === 0) {
              return true; // No required options
            }

            if (!value || value.length === 0) {
              return this.createError({
                message: `Please select all required options: ${requiredOptions.join(', ')}`,
              });
            }

            // Check if all required options are present
            const selectedOptions = value
              .map((opt: any) => opt.name?.toLowerCase())
              .filter(Boolean);
            const missingOptions = requiredOptions.filter(
              required => !selectedOptions.includes(required),
            );

            if (missingOptions.length > 0) {
              return this.createError({
                message: `Missing required options: ${missingOptions.join(', ')}`,
              });
            }

            // Check if all selected options have valid values
            const invalidOptions = value.filter(
              (opt: any) => !opt.value || opt.value.trim() === '',
            );
            if (invalidOptions.length > 0) {
              return this.createError({
                message: 'All selected options must have a value',
              });
            }

            return true;
          },
        );
      },
      otherwise: schema => schema.optional(),
    }),

  otherOptions: yup
    .array()
    .of(
      yup.object().shape({
        name: yup.string().required('Option name is required'),
        value: yup.mixed().required('Option value is required').optional(),
      }),
    )
    .when(['product'], {
      is: (product: any) => {
        const customizer = getCustomizer(product);
        const hasBackgroundOrFrame = customizer.some(
          (section: any) => section.background_options || section.frame_options,
        );
        return hasBackgroundOrFrame;
      },
      then: schema => {
        return schema.test(
          'background-frame-options',
          'Background and frame options must be selected when available',
          function (value) {
            const { product } = this.parent;
            const customizer = getCustomizer(product);

            const hasBackgrounds = customizer.some((section: any) => section.background_options);
            const hasFrames = customizer.some((section: any) => section.frame_options);

            if (!value) value = [];
            const selectedOptions = value.map((opt: any) => opt.name?.toLowerCase());

            if (hasBackgrounds && !selectedOptions.includes('background')) {
              return this.createError({
                message: 'Background option must be selected',
              });
            }

            if (hasFrames && !selectedOptions.includes('frame')) {
              return this.createError({
                message: 'Frame option must be selected',
              });
            }

            return true;
          },
        );
      },
      otherwise: schema => schema.optional(),
    }),

  finalOptions: yup
    .array()
    .of(
      yup
        .object()
        .shape({
          name: yup.string(),
          value: yup.number(),
          variant: yup.string().optional(),
        })
        .test('rush-creation-required', 'Rush Creation option is required', function (value) {
          if (value?.name === '_rush_creation') {
            return value.variant !== undefined && value.variant !== '';
          }
          return true;
        }),
    )
    .when(['product'], {
      is: (product: any) => {
        const customizer = getCustomizer(product);
        return customizer.some(
          (section: any) => section.display && section.label_for === 'Rush Creation',
        );
      },
      then: schema =>
        schema.test('has-rush-creation', 'Rush Creation option is required', function (value) {
          return value?.some((option: any) => option.name === '_rush_creation');
        }),
      otherwise: schema => schema.optional(),
    }),
});

const regularOrderItemSchema = baseOrderItemSchema.shape({
  petImages: yup
    .array()
    .of(yup.string().required('Pet image URL is required'))
    .test('petImagesCount', 'Pet images are required', function (value) {
      const sku = this.parent.sku;
      if (!sku || !sku.sku) return true;
      const petCount = (sku.sku.match(/(\d+)\s*pets?/i) || [null, '1'])[1];
      const requiredCount = parseInt(petCount);
      return (
        value && value.length === requiredCount && value.every(item => typeof item === 'string')
      );
    })
    .required('Pet images are required'),
});

const pdpCustomizerOrderItemSchema = baseOrderItemSchema.shape({
  pdp_pet_images_data: yup
    .array()
    .of(
      yup.object().shape({
        pet_name: yup.string().optional(),
        pet_images: yup
          .array()
          .of(
            yup
              .string()
              .required('Image URL is required')
              .test('nonEmpty', 'Image URL cannot be empty', value => {
                return value !== null && value !== undefined && value.trim() !== '';
              }),
          )
          .min(1, 'At least one image is required')
          .max(1, 'Only one image is allowed')
          .required('Image is required'),
      }),
    )
    .required('Images are required'),
  pdp_swatches: yup.array().of(
    yup.object().shape({
      label: yup.string().required('Swatch label is required'),
      value: yup.string().required('Value is required'),
    }),
  ),
});

export const orderFormSchema = yup.object().shape({
  firstName: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .required('First name is required'),
  lastName: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .required('Last name is required'),
  shippingAddress: yup
    .string()
    .min(5, 'Address must be at least 5 characters')
    .required('Shipping address is required'),
  customerEmail: yup.string().email('Invalid email address').required('Email is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  country: yup.object().shape({
    label: yup.string().required('Country is required'),
    code: yup.string().required('Country code is required'),
  }),
  phoneNumber: yup
    .string()
    .min(10, 'Phone number must be at least 10 digits')
    .required('Phone number is required'),
  zipCode: yup
    .string()
    .matches(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code')
    .required('ZIP code is required'),
  newOrderReason: yup.string().required('New order reason is required'),

  orderItems: yup
    .array()
    .of(
      yup.lazy(value => {
        const isCustomizer = value?.product?.metadata?.product_customizer;
        const isPdpCustomizer = value?.product?.metadata?.pdp_customizer;

        return isCustomizer
          ? customizerOrderItemSchema
          : isPdpCustomizer
            ? pdpCustomizerOrderItemSchema
            : regularOrderItemSchema;
      }),
    )
    .min(1, 'At least one order item is required'),
});

export type OrderFormSchemaType = yup.InferType<typeof orderFormSchema> & {
  orderItems: Array<any>;
};
