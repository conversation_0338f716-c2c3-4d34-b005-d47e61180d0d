'use client';
import React from 'react';
import Grid from '@mui/material/Grid2';
import { Typography, Box, RadioGroup, FormHelperText, Radio } from '@mui/material';
import { Control, Controller, useWatch } from 'react-hook-form';
import { OrderFormSchemaType } from '../../validations/manual-order.validation';

interface ProductOptionsSectionProps {
  optionSection: any;
  index: number;
  control: Control<OrderFormSchemaType>;
  errorsMap: any;
}

const ProductOptionsSection = ({
  optionSection,
  index,
  control,
  errorsMap,
}: ProductOptionsSectionProps) => {
  const petTypeField = useWatch({
    control,
    name: `orderItems.${index}._pet_type`,
  });
  return (
    <>
      {optionSection.main_heading && optionSection[petTypeField] && (
        <Grid size={12}>
          <Typography variant="h4" sx={{ my: 5 }}>
            {optionSection.main_heading}
          </Typography>
        </Grid>
      )}

      {optionSection[petTypeField] &&
        Object.entries(optionSection[petTypeField]).map(
          ([key, value]: [string, any], sectionIndex: number) =>
            value.display && (
              <Grid size={12} key={key}>
                <Typography variant="h5" className="mb-4 mt-6">
                  {key}
                </Typography>
                <Controller
                  name={`orderItems.${index}.productOptions`}
                  control={control}
                  render={({ field }) => {
                    const existingOption = field.value?.find(
                      (opt: { name: string; value: string }) => opt.name === key.toLowerCase(),
                    );

                    return (
                      <RadioGroup
                        value={existingOption?.value || ''}
                        onChange={e => {
                          const newOption = {
                            name: key.toLowerCase(),
                            value: e.target.value,
                          };

                          const currentOptions = field.value || [];
                          const filteredOptions = currentOptions.filter(
                            (opt: { name: string; value: string }) =>
                              opt.name !== key.toLowerCase(),
                          );

                          field.onChange([...filteredOptions, newOption]);
                        }}
                        sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}
                      >
                        {value.images_and_positions.map(
                          (
                            option: Record<'src' | 'name' | 'value', string>,
                            optionIndex: number,
                          ) => (
                            <Box key={`${optionIndex}`} sx={{ textAlign: 'center' }}>
                              <Box
                                component="label"
                                sx={{
                                  cursor: 'pointer',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  width: '100px',
                                  height: 'auto',
                                  position: 'relative',
                                  '&:hover': {
                                    '& .MuiBox-root': {
                                      boxShadow: '0 0 10px rgba(0,0,0,0.2)',
                                    },
                                  },
                                }}
                              >
                                <Radio
                                  value={option.value}
                                  checked={existingOption?.value === option.value}
                                  sx={{
                                    display: 'none',
                                  }}
                                />
                                <Box
                                  component="img"
                                  src={option.src}
                                  alt={option.name}
                                  sx={{
                                    width: 100,
                                    height: 100,
                                    border: theme =>
                                      `2px solid ${
                                        existingOption?.value === option.value
                                          ? theme.palette.primary.main
                                          : theme.palette.grey[300]
                                      }`,
                                    borderRadius: 2,
                                    p: 1,
                                    transition: 'all 0.2s ease-in-out',
                                    '&:hover': {
                                      transform: 'scale(1.05)',
                                    },
                                  }}
                                />
                                <Typography
                                  sx={{
                                    mt: 1,
                                    color: theme =>
                                      existingOption?.value === option.value
                                        ? theme.palette.primary.main
                                        : 'inherit',
                                    fontWeight: existingOption?.value === option.value ? 600 : 400,
                                  }}
                                >
                                  {option.name}
                                </Typography>
                              </Box>
                            </Box>
                          ),
                        )}
                      </RadioGroup>
                    );
                  }}
                />
              </Grid>
            ),
        )}
      {optionSection[petTypeField] && (
        <Grid size={12}>
          <FormHelperText error>{errorsMap?.productOptions?.message}</FormHelperText>
        </Grid>
      )}
    </>
  );
};

export default ProductOptionsSection;
