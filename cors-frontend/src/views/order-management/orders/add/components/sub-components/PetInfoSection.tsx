'use client';
import React from 'react';
import Grid from '@mui/material/Grid2';
import {
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { Control, Controller, useWatch } from 'react-hook-form';
import { OrderFormSchemaType } from '../../validations/manual-order.validation';

interface PetInfoSectionProps {
  infoSection: any;
  index: number;
  control: Control<OrderFormSchemaType>;
  errorsMap: any;
}

const PetInfoSection = ({ infoSection, index, control, errorsMap }: PetInfoSectionProps) => {
  const hasDifferentEyeColors = useWatch({
    control,
    name: `orderItems.${index}.differentEyeColors`,
  });
  const petTypeField = useWatch({
    control,
    name: `orderItems.${index}._pet_type`,
  });
  return (
    <>
      {infoSection.main_heading && (
        <Grid size={12}>
          <Typography variant="h4" sx={{ my: 5 }}>
            {infoSection.main_heading}
          </Typography>
        </Grid>
      )}
      {infoSection.eye_types && (
        <>
          <Grid size={hasDifferentEyeColors ? 6 : 12}>
            <FormControl fullWidth error={!!errorsMap?.left_eye_color}>
              <InputLabel>{hasDifferentEyeColors ? 'Left Eye Color' : 'Eye Color'}</InputLabel>
              <Controller
                name={`orderItems.${index}.left_eye_color`}
                control={control}
                rules={{ required: 'Eye color is required' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    value={field.value || ''}
                    label={hasDifferentEyeColors ? 'Left Eye Color' : 'Eye Color'}
                  >
                    {infoSection.eye_types.map(
                      (eye: Record<'value' | 'label' | 'color', string>, eyeIndex: number) => (
                        <MenuItem key={eyeIndex} value={eye.value}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: '50%',
                                backgroundColor: eye.color,
                              }}
                            />
                            {eye.label}
                          </Box>
                        </MenuItem>
                      ),
                    )}
                  </Select>
                )}
              />
              <FormHelperText>{errorsMap?.left_eye_color?.message}</FormHelperText>
            </FormControl>
          </Grid>
          {hasDifferentEyeColors && (
            <Grid size={6}>
              <FormControl fullWidth error={!!errorsMap?.right_eye_color}>
                <InputLabel>Right Eye Color</InputLabel>
                <Controller
                  name={`orderItems.${index}.right_eye_color`}
                  control={control}
                  rules={{ required: 'Right eye color is required' }}
                  render={({ field }) => (
                    <Select {...field} value={field.value || ''} label="Right Eye Color">
                      {infoSection.eye_types.map(
                        (eye: Record<'label' | 'value' | 'color', string>, eyeIndex: number) => (
                          <MenuItem key={eyeIndex} value={eye.value}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box
                                sx={{
                                  width: 16,
                                  height: 16,
                                  borderRadius: '50%',
                                  backgroundColor: eye.color,
                                }}
                              />
                              {eye.label}
                            </Box>
                          </MenuItem>
                        ),
                      )}
                    </Select>
                  )}
                />
                <FormHelperText>{errorsMap?.right_eye_color?.message}</FormHelperText>
              </FormControl>
            </Grid>
          )}

          <Grid size={12}>
            <FormControlLabel
              control={
                <Controller
                  name={`orderItems.${index}.differentEyeColors`}
                  control={control}
                  defaultValue={false}
                  render={({ field: { onChange, value } }) => (
                    <Checkbox checked={value} onChange={e => onChange(e.target.checked)} />
                  )}
                />
              }
              label="Different eye colors"
            />
          </Grid>
        </>
      )}
      {infoSection[petTypeField]?.ear_position &&
        Object.keys(infoSection[petTypeField]?.ear_position)?.length > 0 && (
          <>
            <Grid size={12}>
              <Typography variant="h5" gutterBottom>
                {infoSection.dog.ear_positions_heading}
              </Typography>
            </Grid>
            <Grid size={{ xs: 12, lg: 6 }}>
              <Typography variant="h6" gutterBottom>
                Left Ear Position
              </Typography>
              <Controller
                name={`orderItems.${index}.left_ear_position`}
                control={control}
                rules={{ required: 'Left ear position is required' }}
                render={({ field }) => (
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {infoSection.dog.ear_position.Left.map(
                      (position: Record<'src' | 'alt' | 'value', string>, i: number) => (
                        <Box key={i} sx={{ textAlign: 'center' }}>
                          <Box
                            component="label"
                            sx={{
                              cursor: 'pointer',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              width: '100px',
                              height: 'auto',
                              position: 'relative',
                              '&:hover': {
                                '& .MuiBox-root': {
                                  boxShadow: '0 0 10px rgba(0,0,0,0.2)',
                                },
                              },
                            }}
                          >
                            <input
                              type="radio"
                              {...field}
                              value={position.value}
                              checked={field.value === position.value}
                              style={{ display: 'none' }}
                            />
                            <Box
                              component="img"
                              src={position.src}
                              alt={position.alt}
                              sx={{
                                width: 100,
                                height: 100,
                                border: theme =>
                                  `2px solid ${field.value === position.value ? theme.palette.primary.main : theme.palette.grey[300]}`,
                                borderRadius: 2,
                                p: 1,
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                  transform: 'scale(1.05)',
                                },
                              }}
                            />
                            <Typography
                              sx={{
                                mt: 1,
                                color: theme =>
                                  field.value === position.value
                                    ? theme.palette.primary.main
                                    : 'inherit',
                                fontWeight: field.value === position.value ? 600 : 400,
                              }}
                            >
                              {position.alt}
                            </Typography>
                          </Box>
                        </Box>
                      ),
                    )}
                  </Box>
                )}
              />
              <FormHelperText error>{errorsMap?.left_ear_position?.message}</FormHelperText>
            </Grid>

            <Grid size={{ xs: 12, lg: 6 }}>
              <Typography variant="h6" gutterBottom>
                Right Ear Position
              </Typography>
              <Controller
                name={`orderItems.${index}.right_ear_position`}
                control={control}
                rules={{ required: 'Right ear position is required' }}
                render={({ field }) => (
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {infoSection.dog.ear_position.Right.map(
                      (position: Record<'src' | 'alt' | 'value', string>, i: number) => (
                        <Box key={i} sx={{ textAlign: 'center' }}>
                          <Box
                            component="label"
                            sx={{
                              cursor: 'pointer',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              width: '100px',
                              height: 'auto',
                              position: 'relative',
                              '&:hover': {
                                '& .MuiBox-root': {
                                  boxShadow: '0 0 10px rgba(0,0,0,0.2)',
                                },
                              },
                            }}
                          >
                            <input
                              type="radio"
                              {...field}
                              value={position.value}
                              checked={field.value === position.value}
                              style={{ display: 'none' }}
                            />
                            <Box
                              component="img"
                              src={position.src}
                              alt={position.alt}
                              sx={{
                                width: 100,
                                height: 100,
                                border: theme =>
                                  `2px solid ${field.value === position.value ? theme.palette.primary.main : theme.palette.grey[300]}`,
                                borderRadius: 2,
                                p: 1,
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                  transform: 'scale(1.05)',
                                },
                              }}
                            />
                            <Typography
                              sx={{
                                mt: 1,
                                color: theme =>
                                  field.value === position.value
                                    ? theme.palette.primary.main
                                    : 'inherit',
                                fontWeight: field.value === position.value ? 600 : 400,
                              }}
                            >
                              {position.alt}
                            </Typography>
                          </Box>
                        </Box>
                      ),
                    )}
                  </Box>
                )}
              />
              <FormHelperText error>{errorsMap?.right_ear_position?.message}</FormHelperText>
            </Grid>
          </>
        )}
    </>
  );
};

export default PetInfoSection;
