'use client';
import React from 'react';
import Grid from '@mui/material/Grid2';
import { Typography, Box, RadioGroup, FormHelperText, Card, Radio } from '@mui/material';
import { Control, Controller } from 'react-hook-form';
import { OrderFormSchemaType } from '../../validations/manual-order.validation';

interface FrameOptionsSectionProps {
  section: any;
  index: number;
  control: Control<OrderFormSchemaType>;
  errorsMap: any;
}

const FrameOptionsSection = ({ section, index, control, errorsMap }: FrameOptionsSectionProps) => {
  return (
    <>
      <Grid size={12}>
        <Typography variant="h6" gutterBottom>
          {section.frame_heading}
        </Typography>

        <Grid container spacing={2}>
          <Grid size={12}>
            <Controller
              name={`orderItems.${index}.otherOptions`}
              control={control}
              defaultValue={[]}
              render={({ field }) => {
                const currentOptions = field.value || [];
                const existingFrameOption = currentOptions.find(
                  (opt: { name: string }) => opt.name === 'frame',
                );

                return (
                  <RadioGroup
                    value={existingFrameOption?.value || ''}
                    onChange={e => {
                      const newOption = {
                        name: 'frame',
                        value: e.target.value,
                      };

                      let newOptions;
                      if (currentOptions.length === 0) {
                        newOptions = [newOption];
                      } else {
                        const filteredOptions = currentOptions.filter(
                          (opt: { name: string }) => opt.name !== 'frame',
                        );
                        newOptions = [...filteredOptions, newOption];
                      }

                      field.onChange(newOptions);
                    }}
                    sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}
                  >
                    {section.frame_options.map(
                      (option: {
                        display: boolean;
                        label: string;
                        image: string;
                        value: number;
                        tooltip: string;
                        tooltip_icon: string;
                      }) =>
                        option.display && (
                          <Box
                            key={option.value}
                            sx={{
                              cursor: 'pointer',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              width: '100px',
                              height: 'auto',
                              position: 'relative',
                              '&:hover': {
                                '& .MuiBox-root': {
                                  boxShadow: '0 0 10px rgba(0,0,0,0.2)',
                                },
                              },
                            }}
                            onClick={() => {
                              const newOption = {
                                name: 'frame',
                                value: option.value,
                              };
                              let newOptions;
                              if (currentOptions.length === 0) {
                                newOptions = [newOption];
                              } else {
                                const filteredOptions = currentOptions.filter(
                                  (opt: { name: string }) => opt.name !== 'frame',
                                );
                                newOptions = [...filteredOptions, newOption];
                              }
                              field.onChange(newOptions);
                            }}
                          >
                            <Radio
                              value={option.value}
                              checked={existingFrameOption?.value === option.value}
                              style={{ display: 'none' }}
                            />
                            <Box
                              component="img"
                              src={option.image}
                              alt={option.label}
                              sx={{
                                width: 100,
                                height: 100,
                                border: theme =>
                                  `2px solid ${existingFrameOption?.value === option.value ? theme.palette.primary.main : theme.palette.grey[300]}`,
                                borderRadius: 2,
                                p: 1,
                                transition: 'all 0.2s ease-in-out',
                                '&:hover': {
                                  transform: 'scale(1.05)',
                                },
                              }}
                            />
                            <Typography
                              sx={{
                                mt: 1,
                                color: theme =>
                                  existingFrameOption?.value === option.value
                                    ? theme.palette.primary.main
                                    : 'inherit',
                                fontWeight: existingFrameOption?.value === option.value ? 600 : 400,
                              }}
                            >
                              {option.label}
                            </Typography>
                          </Box>
                        ),
                    )}
                  </RadioGroup>
                );
              }}
            />
          </Grid>

          <Grid size={12}>
            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
              {section.backgrounds_heading}
            </Typography>
            <Controller
              name={`orderItems.${index}.otherOptions`}
              control={control}
              defaultValue={[]}
              render={({ field }) => {
                const currentOptions = field.value || [];
                const existingBackgroundOption = currentOptions.find(
                  (opt: { name: string }) => opt.name === 'background',
                );

                return (
                  <RadioGroup
                    value={existingBackgroundOption?.value || ''}
                    onChange={e => {
                      const newOption = {
                        name: 'background',
                        value: e.target.value,
                      };
                      let newOptions;
                      if (currentOptions.length === 0) {
                        newOptions = [newOption];
                      } else {
                        const filteredOptions = currentOptions.filter(
                          (opt: { name: string }) => opt.name !== 'background',
                        );
                        newOptions = [...filteredOptions, newOption];
                      }

                      field.onChange(newOptions);
                    }}
                    sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}
                  >
                    {section.background_options.map(
                      (option: { display: boolean; label: string; image: string }) =>
                        option.display && (
                          <Box
                            key={option.label}
                            sx={{
                              cursor: 'pointer',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              width: '100px',
                              height: 'auto',
                              position: 'relative',
                              '&:hover': {
                                '& .MuiBox-root': {
                                  boxShadow: '0 0 10px rgba(0,0,0,0.2)',
                                },
                              },
                            }}
                          >
                            <Box
                              sx={{
                                cursor: 'pointer',
                                textAlign: 'center',
                              }}
                              onClick={() => {
                                const newOption = {
                                  name: 'background',
                                  value: option.label,
                                };
                                let newOptions;
                                if (currentOptions.length === 0) {
                                  newOptions = [newOption];
                                } else {
                                  const filteredOptions = currentOptions.filter(
                                    (opt: { name: string }) => opt.name !== 'background',
                                  );
                                  newOptions = [...filteredOptions, newOption];
                                }
                                field.onChange(newOptions);
                              }}
                            >
                              <Radio
                                value={option.label}
                                checked={existingBackgroundOption?.value === option.label}
                                style={{ display: 'none' }}
                              />
                              <Box
                                component="img"
                                src={option.image}
                                alt={option.label}
                                sx={{
                                  width: 100,
                                  height: 100,
                                  border: theme =>
                                    `2px solid ${existingBackgroundOption?.value === option.label ? theme.palette.primary.main : theme.palette.grey[300]}`,
                                  borderRadius: 2,
                                  p: 1,
                                  transition: 'all 0.2s ease-in-out',
                                  '&:hover': {
                                    transform: 'scale(1.05)',
                                  },
                                }}
                              />
                              <Typography
                                sx={{
                                  mt: 1,
                                  color: theme =>
                                    existingBackgroundOption?.value === option.label
                                      ? theme.palette.primary.main
                                      : 'inherit',
                                  fontWeight:
                                    existingBackgroundOption?.value === option.label ? 600 : 400,
                                }}
                              >
                                {option.label}
                              </Typography>
                            </Box>
                          </Box>
                        ),
                    )}
                  </RadioGroup>
                );
              }}
            />
          </Grid>
        </Grid>
      </Grid>
      <Grid size={12}>
        <FormHelperText error>{errorsMap?.otherOptions?.message}</FormHelperText>
      </Grid>
    </>
  );
};

export default FrameOptionsSection;
