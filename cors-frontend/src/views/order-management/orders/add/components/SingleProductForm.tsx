import React from 'react';
import Grid from '@mui/material/Grid2';
import { Box, Card, Checkbox, FormHelperText, TextField, Typography } from '@mui/material';
import { Control, Controller, FieldErrors, UseFormSetValue, useWatch } from 'react-hook-form';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { AddonType, SingleProduct, SingleSKU } from '@/types/manual-order.type';
import { OrderFormSchemaType } from '../validations/manual-order.validation';
import { getPetsCountFromSKU } from '@/utils/manual-order.utils';

const SingleProductForm = ({
  selectedProduct,
  selectedSKU,
  index,
  control,
  errors,
  setValue,
}: {
  selectedProduct: SingleProduct;
  selectedSKU: SingleSKU;
  index: number;
  control: Control<OrderFormSchemaType>;
  errors: FieldErrors<OrderFormSchemaType>;
  setValue: UseFormSetValue<OrderFormSchemaType>;
}) => {
  const orderItemsWatch = useWatch({
    control,
    name: 'orderItems',
  });
  return (
    <>
      {selectedProduct && selectedSKU && (
        <Grid container spacing={4}>
          <Grid size={{ xs: 11, lg: 4 }}>
            <Controller
              name={`orderItems.${index}.quantity`}
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  type="number"
                  label="Quantity"
                  error={!!errors.orderItems?.[index]?.quantity}
                  helperText={errors.orderItems?.[index]?.quantity?.message}
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              )}
            />
          </Grid>
          <Grid size={12}>
            <Typography variant="h5" sx={{ mt: 2, mb: 1 }}>
              Product Details
            </Typography>
          </Grid>
          <Grid size={{ xs: 12, lg: 4 }}>
            <Typography sx={{ my: 2, fontSize: '16px' }}>
              <strong>Product Name:</strong> {selectedProduct?.name || ' -'}
            </Typography>
          </Grid>
          {selectedSKU?.shopifyNativeVariant?.map((variant: { name: string; value: string }) => (
            <Grid size={{ xs: 12, lg: 4 }} key={variant?.name}>
              <Typography sx={{ my: 2, fontSize: '16px' }}>
                <strong>{variant?.name}:</strong> {variant.value || ' -'}
              </Typography>
            </Grid>
          ))}

          {selectedSKU?.shopifyCustomVariant?.map((variant: { name: string; value: string }) => (
            <Grid size={{ xs: 12, lg: 4 }} key={variant?.name}>
              <Typography sx={{ my: 2, fontSize: '16px' }}>
                <strong>{variant?.name}:</strong> {variant.value || ' -'}
              </Typography>
            </Grid>
          ))}

          <Grid size={12}>
            <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
              Pet Images (Required: {getPetsCountFromSKU(selectedSKU.sku)}{' '}
              {getPetsCountFromSKU(selectedSKU.sku) === 1 ? 'image' : 'images'})
            </Typography>
          </Grid>

          <ImageUploadField
            control={control}
            errors={errors}
            setValue={setValue}
            name={`orderItems.${index}.petImages`}
            minImages={getPetsCountFromSKU(selectedSKU.sku)}
            maxImages={getPetsCountFromSKU(selectedSKU.sku)}
            formValue={orderItemsWatch[index]?.petImages || []}
          />

          <Grid size={12}>
            {(errors.orderItems?.[index] as any)?.petImages && (
              <FormHelperText error>
                {(errors.orderItems?.[index] as any).petImages.message}
              </FormHelperText>
            )}
          </Grid>
          <Grid size={12}>
            <Typography variant="h5" sx={{ my: 3 }}>
              Extras
            </Typography>
          </Grid>

          {JSON.parse(selectedProduct?.metadata?.extras || '{}')
            ?.extras_products?.filter(
              (e: AddonType) =>
                !orderItemsWatch.some((item: any) =>
                  item?.identifier?.some(
                    (identifier: any) =>
                      identifier.id === e.id &&
                      selectedProduct?.timestampkey !== item?.product?.timestampkey &&
                      selectedSKU?.addonData?.find((data: any) => data?.variantId == identifier?.id)
                        ?.addonLevel === 'order',
                  ),
                ),
            )
            .map((e: AddonType) => (
              <Grid size={{ xs: 12, lg: 6 }} sx={{ mt: 2 }} key={e.identifier}>
                <Controller
                  name={`orderItems.${index}.identifier`}
                  control={control}
                  defaultValue={[]}
                  render={({ field }) => (
                    <Card
                      sx={{ p: 2, mb: 2 }}
                      className="border-2 border-gray-300 min-h-[150px] h-full"
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" gutterBottom>
                            {e.label}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            {e.description || 'No description available'}
                          </Typography>
                          <Typography variant="subtitle1" color="primary">
                            ${e.price || '0.00'}
                          </Typography>
                        </Box>
                        <Checkbox
                          checked={field.value.some(
                            (item: any) => item.identifier === e.identifier,
                          )}
                          onChange={event => {
                            const updatedIdentifiers = event.target.checked
                              ? [
                                  ...field.value,
                                  {
                                    ...e,
                                    timestampkey: selectedProduct?.timestampkey,
                                  },
                                ]
                              : field.value.filter(
                                  (item: AddonType) => item.identifier !== e.identifier,
                                );
                            field.onChange(updatedIdentifiers);
                          }}
                        />
                      </Box>
                    </Card>
                  )}
                />
              </Grid>
            ))}
        </Grid>
      )}
    </>
  );
};

export default SingleProductForm;
