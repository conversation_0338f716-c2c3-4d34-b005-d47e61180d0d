import React from 'react';
import Grid from '@mui/material/Grid2';
import { Autocomplete, TextField, Typography } from '@mui/material';
import { Control, Controller, FieldErrors, useFormContext, UseFormRegister } from 'react-hook-form';
import { OrderFormSchemaType } from '../validations/manual-order.validation';
import { countries, renderCountryOption } from '@/constants/countries.constants';

const CommonUserDetailsForm = ({
  control,
  errors,
  register,
}: {
  control: Control<OrderFormSchemaType>;
  errors: FieldErrors<OrderFormSchemaType>;
  register: UseFormRegister<OrderFormSchemaType>;
}) => {
  return (
    <div>
      {' '}
      <Typography variant="h4" sx={{ my: 5 }}>
        Customer Details
      </Typography>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, lg: 6 }}>
          <TextField
            fullWidth
            label="First Name"
            {...register('firstName')}
            error={!!errors.firstName}
            helperText={errors.firstName?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 6 }}>
          <TextField
            fullWidth
            label="Last Name"
            {...register('lastName')}
            error={!!errors.lastName}
            helperText={errors.lastName?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 6 }}>
          <TextField
            fullWidth
            label="Customer Email"
            type="email"
            {...register('customerEmail')}
            error={!!errors.customerEmail}
            helperText={errors.customerEmail?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 6 }}>
          <TextField
            fullWidth
            label="Phone Number"
            {...register('phoneNumber')}
            error={!!errors.phoneNumber}
            helperText={errors.phoneNumber?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 12 }}>
          <TextField
            fullWidth
            label="Shipping Address"
            {...register('shippingAddress')}
            error={!!errors.shippingAddress}
            helperText={errors.shippingAddress?.message}
          />
        </Grid>

        <Grid size={{ xs: 12, lg: 3 }}>
          <TextField
            fullWidth
            label="City"
            {...register('city')}
            error={!!errors.city}
            helperText={errors.city?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 3 }}>
          <TextField
            fullWidth
            label="State"
            {...register('state')}
            error={!!errors.state}
            helperText={errors.state?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 3 }}>
          <TextField
            fullWidth
            label="ZIP Code"
            {...register('zipCode')}
            error={!!errors.zipCode}
            helperText={errors.zipCode?.message}
          />
        </Grid>
        <Grid size={{ xs: 12, lg: 3 }}>
          <Controller
            name="country"
            control={control}
            render={({ field }) => (
              <Autocomplete
                id="country-select"
                options={countries}
                value={field.value || {}}
                onChange={(_, newValue) => {
                  field.onChange({
                    label: newValue?.label,
                    code: newValue?.code,
                  });
                }}
                autoHighlight
                getOptionLabel={option => option.label || ''}
                renderOption={renderCountryOption}
                renderInput={params => (
                  <TextField
                    {...params}
                    label="Choose a country"
                    error={!!errors.country}
                    helperText={errors.country?.label?.message}
                    slotProps={{
                      htmlInput: {
                        ...params.inputProps,
                        autoComplete: 'new-password',
                      },
                    }}
                  />
                )}
              />
            )}
          />
        </Grid>

        <Grid size={12}>
          <TextField
            fullWidth
            multiline
            rows={2}
            label="New Order Reason"
            {...register('newOrderReason')}
            error={!!errors.newOrderReason}
            helperText={errors.newOrderReason?.message}
          />
        </Grid>
      </Grid>
    </div>
  );
};

export default CommonUserDetailsForm;
