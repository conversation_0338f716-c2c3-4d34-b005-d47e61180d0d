import { useState, useRef } from 'react';
import {
  Typography,
  Box,
  Paper,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  Divider,
  IconButton,
  Avatar,
} from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import AddIcon from '@mui/icons-material/Add';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import ImageIcon from '@mui/icons-material/Image';
import DeleteIcon from '@mui/icons-material/Delete';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import { format } from 'date-fns';
import { ArtworkRequestsSectionProps } from '@/types/artwork-request.types';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import CustomDialog from '@/components/CustomDialog';

const ArtworkRequestsSection = ({ requests = [], onAddRequest }: ArtworkRequestsSectionProps) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Maximum number of images allowed
  const MAX_IMAGES = 3;

  // Track changes
  const checkForChanges = () => {
    return message.trim() !== '' || uploadedImages.length > 0;
  };

  const handleOpenDialog = () => {
    setDialogOpen(true);
    setHasChanges(false);
  };

  const handleCloseDialog = () => {
    if (checkForChanges()) {
      setConfirmDialogOpen(true);
    } else {
      resetAndClose();
    }
  };

  const resetAndClose = () => {
    setDialogOpen(false);
    setMessage('');
    setUploadedImages([]);
    setHasChanges(false);
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    setHasChanges(true);
  };

  // Modified to limit uploads to MAX_IMAGES
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      const remainingSlots = MAX_IMAGES - uploadedImages.length;

      if (remainingSlots <= 0) {
        // No slots available
        return;
      }

      // Only take as many files as we have slots for
      const filesToAdd = newFiles.slice(0, remainingSlots);

      setUploadedImages(prev => [...prev, ...filesToAdd]);
      setHasChanges(true);
    }
  };

  const handleRemoveImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
    setHasChanges(true);
  };

  // Add preview handler
  const handlePreviewImage = (file: File) => {
    setPreviewImage(URL.createObjectURL(file));
  };

  const handleClosePreview = () => {
    setPreviewImage(null);
  };

  const handleSubmit = () => {
    if (message.trim() || uploadedImages.length > 0) {
      onAddRequest(message, uploadedImages);
      resetAndClose();
    }
  };

  // Add confirmation dialog handler
  const handleConfirmCancel = () => {
    setConfirmDialogOpen(false);
    resetAndClose();
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.paper' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1" fontWeight="bold">
          Artwork Requests
        </Typography>
        <Button variant="contained" startIcon={<AddIcon />} size="small" onClick={handleOpenDialog}>
          Add Request
        </Button>
      </Box>

      {requests.length === 0 ? (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          No artwork requests have been added yet.
        </Typography>
      ) : (
        <>
          {/* Request Log */}
          <List sx={{ mb: 2 }}>
            {requests.map((request, index) => (
              <Box key={request.id}>
                <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Avatar
                          src={request.createdBy.avatar}
                          alt={request.createdBy.name}
                          sx={{ width: 24, height: 24 }}
                        >
                          {request.createdBy.name.charAt(0)}
                        </Avatar>
                        <Typography variant="subtitle2">{request.createdBy.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(request.createdAt)}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Typography
                        variant="body2"
                        color="text.primary"
                        sx={{ whiteSpace: 'pre-wrap' }}
                      >
                        {request.message}
                      </Typography>
                    }
                  />
                </ListItem>

                {/* Photos for this request */}
                {request.photos && request.photos.length > 0 && (
                  <Box sx={{ ml: 2, mb: 2 }}>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ display: 'block', mb: 1 }}
                    >
                      Revision Photos:
                    </Typography>
                    <Grid2 container spacing={1}>
                      {request.photos.map(photo => (
                        <Grid2 size={{ xs: 4, sm: 3, md: 2 }} key={photo.id}>
                          <Box
                            component="a"
                            href={photo.fileUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            sx={{
                              display: 'block',
                              width: 80,
                              height: 80,
                              borderRadius: 1,
                              overflow: 'hidden',
                              position: 'relative',
                              '&:hover': {
                                '& .overlay': {
                                  opacity: 1,
                                },
                              },
                            }}
                          >
                            <Box
                              component="img"
                              src={photo.thumbnailUrl}
                              alt={photo.fileName}
                              sx={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                              }}
                            />
                            <Box
                              className="overlay"
                              sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                bgcolor: 'rgba(0,0,0,0.5)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                opacity: 0,
                                transition: 'opacity 0.2s',
                              }}
                            >
                              <ImageIcon sx={{ color: 'white' }} />
                            </Box>
                          </Box>
                        </Grid2>
                      ))}
                    </Grid2>
                  </Box>
                )}

                {index < requests.length - 1 && <Divider sx={{ my: 2 }} />}
              </Box>
            ))}
          </List>
        </>
      )}

      {/* Add Request Dialog */}
      <CustomDialog
        open={dialogOpen}
        title="Add Artwork Request"
        confirmLabel="Submit Request"
        cancelLabel="Cancel"
        confirmColor="primary"
        disableConfirm={!message.trim() && uploadedImages.length === 0}
        onConfirm={handleSubmit}
        onCancel={handleCloseDialog}
        maxWidth="md"
      >
        <Box sx={{ mt: 2 }}>
          <TextField
            autoFocus
            margin="dense"
            id="message"
            label="Message"
            fullWidth
            multiline
            rows={4}
            value={message}
            onChange={handleMessageChange}
            variant="outlined"
            placeholder="Enter instructions for the artist..."
            sx={{ mb: 3 }}
          />

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Upload Reference Images
            </Typography>
            <Box
              sx={{
                border: '1px dashed #ccc',
                p: 3,
                textAlign: 'center',
                borderRadius: 1,
                mb: 2,
              }}
            >
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="image-upload-button"
                type="file"
                multiple
                onChange={handleImageUpload}
                ref={fileInputRef}
                disabled={uploadedImages.length >= MAX_IMAGES}
              />
              <label htmlFor="image-upload-button">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  disabled={uploadedImages.length >= MAX_IMAGES}
                >
                  Upload Image
                </Button>
              </label>
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                You can upload up to {MAX_IMAGES} reference images
              </Typography>
              {uploadedImages.length >= MAX_IMAGES && (
                <Typography variant="caption" color="error" display="block" sx={{ mt: 1 }}>
                  Maximum number of images reached ({MAX_IMAGES})
                </Typography>
              )}
            </Box>

            {/* Display uploaded images with increased size and zoom button */}
            {uploadedImages.length > 0 && (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                {uploadedImages.map((file, index) => (
                  <Box
                    key={index}
                    sx={{
                      position: 'relative',
                      width: 200,
                      height: 200,
                      border: '1px solid #eee',
                      borderRadius: 1,
                      overflow: 'hidden',
                    }}
                  >
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`Upload ${index}`}
                      style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                    />
                    <Box sx={{ position: 'absolute', top: 0, right: 0, display: 'flex' }}>
                      <IconButton
                        size="small"
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.7)',
                          '&:hover': {
                            bgcolor: 'rgba(255,255,255,0.9)',
                          },
                          m: 0.5,
                        }}
                        onClick={() => handleRemoveImage(index)}
                      >
                        <DeleteIcon fontSize="small" color="error" />
                      </IconButton>
                    </Box>
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 8,
                        right: 8,
                        bgcolor: 'rgba(0,0,0,0.5)',
                        color: 'white',
                        borderRadius: '50%',
                        width: 32,
                        height: 32,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <ZoomInIcon fontSize="small" />
                    </Box>
                  </Box>
                ))}
              </Box>
            )}
          </Box>
        </Box>
      </CustomDialog>

      {/* Add ImagePreviewDialog component */}
      <ImagePreviewDialog
        open={!!previewImage}
        onClose={handleClosePreview}
        imageUrl={previewImage}
        title="Image Preview"
      />

      {/* Add ConfirmationDialog component */}
      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have unsaved changes. Are you sure you want to discard them?"
        confirmLabel="Discard"
        cancelLabel="Continue Editing"
        confirmColor="error"
        onConfirm={handleConfirmCancel}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </Paper>
  );
};

export default ArtworkRequestsSection;
