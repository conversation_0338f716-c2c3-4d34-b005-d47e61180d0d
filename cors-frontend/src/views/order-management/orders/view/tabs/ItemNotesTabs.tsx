import { useState } from 'react';
import {
  Typo<PERSON>,
  Box,
  Tabs,
  Tab,
  Paper,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import AddIcon from '@mui/icons-material/Add';
import apiClient from '@/utils/axios';

interface OrderItem {
  id: string;
  name: string;
  itemNumber: string;
  status?: string;
  cancelReason?: {
    reason?: string;
    timestamp?: string;
    username?: string;
  };
}

interface ItemNotesTabsProps {
  orderData: {
    orderNumber: string;
    items: OrderItem[];
    id?: string;
  };
  itemNotes: Record<string, string[]>;
  setItemNotes: React.Dispatch<React.SetStateAction<Record<string, string[]>>>;
}

const ItemNotesTabs = ({ orderData, itemNotes, setItemNotes }: ItemNotesTabsProps) => {
  const [activeNoteTab, setActiveNoteTab] = useState('0');
  const [newNote, setNewNote] = useState('');
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [nextTabValue, setNextTabValue] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNoteTabChange = (event: React.SyntheticEvent, newValue: string) => {
    if (newNote.trim()) {
      setNextTabValue(newValue);
      setConfirmDialogOpen(true);
    } else {
      setActiveNoteTab(newValue);
    }
  };

  const handleConfirmTabChange = () => {
    setConfirmDialogOpen(false);
    if (nextTabValue !== null) {
      setActiveNoteTab(nextTabValue);
      setNextTabValue(null);
      setNewNote('');
    }
  };

  const handleCancelTabChange = () => {
    setConfirmDialogOpen(false);
    setNextTabValue(null);
  };

  const handleAddNote = async () => {
    if (newNote.trim()) {
      const itemId = orderData.items[parseInt(activeNoteTab)].id;
      setIsSubmitting(true);

      try {
        await apiClient.put(`/orders/line-items/${itemId}`, {
          notes: [...(itemNotes[itemId] || []), newNote.trim()],
        });

        setItemNotes(prev => ({
          ...prev,
          [itemId]: [...(prev[itemId] || []), newNote.trim()],
        }));

        setNewNote('');
      } catch (error) {
        console.error('Failed to save note:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && event.ctrlKey) {
      handleAddNote();
    }
  };

  return (
    <>
      <TabContext value={activeNoteTab}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeNoteTab}
            onChange={handleNoteTabChange}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
          >
            {orderData.items &&
              orderData.items.map((item, index: number) => (
                <Tab key={item.id} label={`${item.itemNumber}`} value={index.toString()} />
              ))}
          </Tabs>
        </Box>

        {orderData.items &&
          orderData.items.map((item, index: number) => (
            <TabPanel key={item.id} value={index.toString()}>
              <Box sx={{ mb: 2, mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Item #{item.itemNumber} - SKU: {item.name || 'Not Provided'}
                </Typography>

                {/* Cancellation Details */}
                {item.status === 'cancelled' && item.cancelReason && (
                  <Box sx={{ mb: 3, p: 2, borderRadius: 1 }}>
                    <Typography variant="body1" fontWeight="bold" gutterBottom>
                      Cancellation Details
                    </Typography>
                    <Typography>
                      <strong>Reason:</strong> {item.cancelReason.reason || 'Not specified'}
                    </Typography>
                    {item.cancelReason.timestamp && (
                      <Typography>
                        <strong>Date:</strong>{' '}
                        {new Date(item.cancelReason.timestamp).toLocaleString(undefined, {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </Typography>
                    )}
                    {item.cancelReason.username && (
                      <Typography>
                        <strong>Cancelled By:</strong> {item.cancelReason.username}
                      </Typography>
                    )}
                  </Box>
                )}

                {/* Notes list */}
                {itemNotes[item.id] && itemNotes[item.id].length > 0 ? (
                  <Box sx={{ mb: 2 }}>
                    {itemNotes[item.id].map((note, noteIndex) => (
                      <Paper key={noteIndex} sx={{ p: 2, mb: 1 }}>
                        <Typography>{note}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Added on {new Date().toLocaleString()}
                        </Typography>
                      </Paper>
                    ))}
                  </Box>
                ) : (
                  <Typography color="text.secondary" sx={{ mb: 2 }}>
                    No Notes for this item yet.
                  </Typography>
                )}

                {/* Add note form */}
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <TextField
                    label="Add a note"
                    multiline
                    rows={2}
                    value={newNote}
                    onChange={e => setNewNote(e.target.value)}
                    onKeyDown={handleKeyPress}
                    fullWidth
                    variant="outlined"
                    size="small"
                    sx={{ mr: 1 }}
                    placeholder="Type your note here (Ctrl+Enter to submit)"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleAddNote}
                    disabled={!newNote.trim() || isSubmitting}
                    sx={{ mt: 1 }}
                  >
                    {isSubmitting ? 'Saving...' : 'Add Note'}
                  </Button>
                </Box>
              </Box>
            </TabPanel>
          ))}
      </TabContext>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onClose={handleCancelTabChange}>
        <DialogTitle>Unsaved Note</DialogTitle>
        <DialogContent>
          You have an unsaved note. Switching tabs will discard your changes. Do you want to
          continue?
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelTabChange}>Cancel</Button>
          <Button onClick={handleConfirmTabChange} color="primary">
            Switch Tab
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ItemNotesTabs;
