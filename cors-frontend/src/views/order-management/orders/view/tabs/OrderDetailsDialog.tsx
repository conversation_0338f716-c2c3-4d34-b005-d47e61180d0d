import {
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
} from '@mui/material/';
import Grid2 from '@mui/material/Grid2';
import CloseIcon from '@mui/icons-material/Close';
import ItemNotesTabs from './ItemNotesTabs';
import StatusChip from '@/components/StatusChip';
import { OrderDetailsDialogProps } from '@/types/order-details.types';
import { useEffect } from 'react';

const OrderDetailsDialog = ({
  open,
  onClose,
  orderData,
  itemNotes,
  setItemNotes,
}: OrderDetailsDialogProps) => {
  useEffect(() => {
    if (orderData?.lineItems) {
      const initialNotes: Record<string, string[]> = {};
      orderData.lineItems.forEach((item: any) => {
        if (item.id && item.notes) {
          initialNotes[item.id] = item.notes;
        }
      });
      setItemNotes(initialNotes);
    }
  }, [orderData, setItemNotes]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      sx={{
        '& .MuiDialog-paper': {
          overflow: 'visible',
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Changed from Typography variant="h6" to span to avoid nesting h-tags */}
          <span>Order Details - #{orderData?.shopifyOrderNumber}</span>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          {/* Use component="div" to avoid nesting h-tags */}
          <Typography variant="h6" component="div" gutterBottom>
            Order Status
          </Typography>
          <StatusChip status={orderData?.orderStatus} />
        </Box>

        <Grid2 container spacing={4}>
          <Grid2 size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Customer Shipping Address
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Typography>
                {orderData?.shippingAddress?.first_name +
                  ' ' +
                  orderData?.shippingAddress?.last_name}
              </Typography>
              <Typography>{orderData?.shippingAddress?.phone}</Typography>

              <Typography>
                {orderData?.shippingAddress?.address1 + ' ' + orderData?.shippingAddress?.address2}
              </Typography>
              <Typography>
                {orderData?.shippingAddress?.city}, {orderData?.shippingAddress?.province}{' '}
                {orderData?.shippingAddress?.zip}
              </Typography>
              <Typography>{orderData?.shippingAddress?.company}</Typography>

              <Typography>{orderData?.shippingAddress?.country}</Typography>
            </Paper>
          </Grid2>

          <Grid2 size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Customer Billing Address
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Typography>
                {orderData?.billingAddress?.first_name + ' ' + orderData?.billingAddress?.last_name}
              </Typography>
              <Typography>{orderData?.billingAddress?.phone}</Typography>
              <Typography>
                {orderData?.billingAddress?.address1 + ' ' + orderData?.billingAddress?.address2}
              </Typography>
              <Typography>
                {orderData?.billingAddress?.city}, {orderData?.billingAddress?.province}{' '}
                {orderData?.billingAddress?.zip}
              </Typography>
              <Typography>{orderData?.billingAddress?.company}</Typography>
              <Typography>{orderData?.billingAddress?.country}</Typography>
            </Paper>
          </Grid2>

          <Grid2 size={{ xs: 12 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Payment Information
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Grid2 container spacing={2}>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Discount</Typography>
                  <Typography>{orderData?.paymentInformation?.discount}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Shipping Price</Typography>
                  <Typography>{orderData?.paymentInformation?.shipping_price}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">SubTotal Price</Typography>
                  <Typography>{orderData?.paymentInformation?.subtotal_price}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Tax</Typography>
                  <Typography>{orderData?.paymentInformation?.tax}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Total Amount</Typography>
                  <Typography>${orderData?.paymentInformation?.total_price}</Typography>
                </Grid2>
              </Grid2>
            </Paper>
          </Grid2>

          <Grid2 size={{ xs: 12 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Item SKUs and Quantities
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Item #</TableCell>
                    <TableCell>SKU</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell>Fulfillment Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {orderData?.lineItems &&
                    orderData.lineItems.map((item: any, index: number) => (
                      <TableRow key={item.id}>
                        <TableCell>{item?.itemNumber}</TableCell>
                        <TableCell>{item?.productSku?.sku || 'Not Provided'}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell>
                          <StatusChip status={orderData?.orderStatus} />
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid2>

          <Grid2 size={{ xs: 12 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Notes
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <ItemNotesTabs
                orderData={{
                  id: orderData?.id,
                  orderNumber: orderData?.shopifyOrderNumber,
                  items: orderData?.lineItems?.map((item: any) => ({
                    id: item?.id,
                    name: item?.productSku?.sku,
                    itemNumber: item?.itemNumber,
                    status: item?.status,
                    cancelReason: item?.cancelReason
                      ? item.cancelReason
                      : {
                          reason: item.cancelReason?.reason,
                          timestamp: item?.cancelReason?.timestamp,
                          username: item.cancelReason?.username,
                        },
                  })),
                }}
                itemNotes={itemNotes}
                setItemNotes={setItemNotes}
              />
            </Paper>
          </Grid2>
        </Grid2>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderDetailsDialog;
