import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  IconButton,
  Checkbox,
  Paper,
  Divider,
  Chip,
  Tooltip,
} from '@mui/material';
import Grid2 from '@mui/material/Grid2';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { ISSUE_CATEGORIES } from '@/constants/customer-contact.constants';
import { CustomerContactDialogProps, Question } from '@/types/customer-contact.types';
import ImageUploadSection from '@/components/ImageUploadSection';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';

const CustomerContactDialog = ({
  open,
  onClose,
  onSend,
  customerImages,
  artFiles,
  itemId,
}: CustomerContactDialogProps) => {
  const [questions, setQuestions] = useState<Question[]>([
    {
      id: '1',
      issueCategory: '',
      selectedImages: [],
      referenceImages: [],
      message: '',
    },
  ]);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const handleIssueChange = (questionId: string, value: string) => {
    setQuestions(prev =>
      prev.map(q => {
        if (q.id === questionId) {
          // Find the default message for this issue category
          const category = ISSUE_CATEGORIES.find(cat => cat.value === value);
          return {
            ...q,
            issueCategory: value,
            message: category?.defaultMessage || q.message,
          };
        }
        return q;
      }),
    );
  };

  const handleMessageChange = (questionId: string, value: string) => {
    setQuestions(prev => prev.map(q => (q.id === questionId ? { ...q, message: value } : q)));
  };

  const handleImageSelect = (questionId: string, imageId: string) => {
    setQuestions(prev =>
      prev.map(q => {
        if (q.id === questionId) {
          const selectedImages = [...q.selectedImages];
          const index = selectedImages.indexOf(imageId);

          if (index === -1) {
            selectedImages.push(imageId);
          } else {
            selectedImages.splice(index, 1);
          }

          return { ...q, selectedImages };
        }
        return q;
      }),
    );
  };

  const handleAddImages = (questionId: string, newImages: File[]) => {
    setQuestions(prev =>
      prev.map(q => {
        if (q.id === questionId) {
          return {
            ...q,
            referenceImages: [...q.referenceImages, ...newImages],
          };
        }
        return q;
      }),
    );
  };

  const handleRemoveImage = (questionId: string, index: number) => {
    setQuestions(prev =>
      prev.map(q => {
        if (q.id === questionId) {
          const newFiles = [...q.referenceImages];
          newFiles.splice(index, 1);
          return { ...q, referenceImages: newFiles };
        }
        return q;
      }),
    );
  };

  const handlePreviewImage = (file: File) => {
    setPreviewImage(URL.createObjectURL(file));
  };

  const handleClosePreview = () => {
    setPreviewImage(null);
  };

  const handleAddQuestion = () => {
    const newQuestionId = (questions.length + 1).toString();
    setQuestions(prev => [
      ...prev,
      {
        id: newQuestionId,
        issueCategory: '',
        selectedImages: [],
        referenceImages: [],
        message: '',
      },
    ]);
  };

  const handleSend = () => {
    // Filter out any empty questions
    const validQuestions = questions.filter(q => q.issueCategory && q.message);
    if (validQuestions.length > 0) {
      onSend(validQuestions);
    }
  };

  const isFormValid = () => {
    return questions.some(q => q.issueCategory && q.message);
  };

  const handleDeleteQuestion = (questionId: string) => {
    // Don't allow deleting if there's only one question
    if (questions.length <= 1) return;

    setQuestions(prev => {
      // Remove the question
      const filteredQuestions = prev.filter(q => q.id !== questionId);

      // Renumber the remaining questions sequentially
      return filteredQuestions.map((q, index) => ({
        ...q,
        id: (index + 1).toString(),
      }));
    });
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            overflow: 'visible',
          },
        }}
      >
        <DialogTitle>
          <Typography variant="h6" component="div">
            Customer Contact Needed
          </Typography>
        </DialogTitle>
        <DialogContent>
          {questions.map((question, index) => (
            <Box key={question.id} sx={{ mb: 4, position: 'relative' }}>
              {index > 0 && <Divider sx={{ my: 3 }} />}

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2,
                }}
              >
                <Typography variant="h6" component="div">
                  Question/Issue {index + 1}
                </Typography>

                {questions.length > 1 && (
                  <Tooltip title="Delete question">
                    <IconButton
                      color="error"
                      onClick={() => handleDeleteQuestion(question.id)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>

              {/* Issue Category Dropdown */}
              <FormControl fullWidth margin="normal">
                <InputLabel id={`issue-label-${question.id}`}>Issue</InputLabel>
                <Select
                  labelId={`issue-label-${question.id}`}
                  value={question.issueCategory}
                  label="Issue"
                  onChange={e => handleIssueChange(question.id, e.target.value)}
                >
                  {ISSUE_CATEGORIES.map(category => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Image Selection Section */}
              {(customerImages.length > 0 || artFiles.length > 0) && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Does it apply to any submitted images? If so, select below:
                  </Typography>

                  <Grid2 container spacing={2}>
                    {/* Customer Images */}
                    {customerImages.map(image => (
                      <Grid2 size={{ xs: 6, sm: 4, md: 3 }} key={image.id}>
                        <Paper
                          elevation={1}
                          sx={{
                            p: 1,
                            border: question.selectedImages.includes(image.id)
                              ? '2px solid #1976d2'
                              : '1px solid #e0e0e0',
                            borderRadius: 1,
                            cursor: 'pointer',
                            '&:hover': {
                              borderColor: '#1976d2',
                            },
                          }}
                          onClick={() => handleImageSelect(question.id, image.id)}
                        >
                          <Box sx={{ position: 'relative' }}>
                            <Box
                              component="img"
                              src={image.thumbnailUrl}
                              alt={image.fileName}
                              sx={{
                                width: '100%',
                                height: 100,
                                objectFit: 'contain',
                                mb: 1,
                              }}
                            />
                            <Checkbox
                              checked={question.selectedImages.includes(image.id)}
                              sx={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                p: 0.5,
                                bgcolor: 'rgba(255,255,255,0.7)',
                                borderRadius: '50%',
                              }}
                            />
                          </Box>
                          <Typography variant="caption" noWrap>
                            {image.fileName}
                          </Typography>
                          <Chip
                            label="Customer Image"
                            size="small"
                            sx={{ mt: 1, fontSize: '0.625rem' }}
                          />
                        </Paper>
                      </Grid2>
                    ))}

                    {/* Art Files */}
                    {artFiles.map(file => (
                      <Grid2 size={{ xs: 6, sm: 4, md: 3 }} key={file.id}>
                        <Paper
                          elevation={1}
                          sx={{
                            p: 1,
                            border: question.selectedImages.includes(file.id)
                              ? '2px solid #1976d2'
                              : '1px solid #e0e0e0',
                            borderRadius: 1,
                            cursor: 'pointer',
                            '&:hover': {
                              borderColor: '#1976d2',
                            },
                          }}
                          onClick={() => handleImageSelect(question.id, file.id)}
                        >
                          <Box sx={{ position: 'relative' }}>
                            <Box
                              component="img"
                              src={file.thumbnailUrl}
                              alt={file.fileName}
                              sx={{
                                width: '100%',
                                height: 100,
                                objectFit: 'contain',
                                mb: 1,
                              }}
                            />
                            <Checkbox
                              checked={question.selectedImages.includes(file.id)}
                              sx={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                p: 0.5,
                                bgcolor: 'rgba(255,255,255,0.7)',
                                borderRadius: '50%',
                              }}
                            />
                          </Box>
                          <Typography variant="caption" noWrap>
                            {file.fileName}
                          </Typography>
                          <Chip
                            label="Completed Artfile"
                            size="small"
                            sx={{ mt: 1, fontSize: '0.625rem' }}
                          />
                        </Paper>
                      </Grid2>
                    ))}
                  </Grid2>
                </Box>
              )}

              {/* Reference Image Upload using ImageUploadSection */}
              <Box sx={{ mt: 3 }}>
                <ImageUploadSection
                  images={question.referenceImages}
                  onAddImages={newImages => handleAddImages(question.id, newImages)}
                  onRemoveImage={index => handleRemoveImage(question.id, index)}
                  onPreviewImage={handlePreviewImage}
                  maxImages={5}
                  title="Upload Reference Images"
                  helperText="You can upload up to 5 reference images (Maximum size: 5MB each)"
                  showPetLabels={false}
                />
              </Box>

              {/* Message Field */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Message
                </Typography>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  value={question.message}
                  onChange={e => handleMessageChange(question.id, e.target.value)}
                  placeholder="Enter your message to the customer..."
                  variant="outlined"
                />
              </Box>
            </Box>
          ))}
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3, justifyContent: 'space-between' }}>
          <Button variant="outlined" startIcon={<AddIcon />} onClick={handleAddQuestion}>
            Add {questions.length + 1}
            {questions.length === 0
              ? 'st'
              : questions.length === 1
                ? 'nd'
                : questions.length === 2
                  ? 'rd'
                  : 'th'}{' '}
            Question
          </Button>

          <Button variant="contained" onClick={handleSend} disabled={!isFormValid()}>
            Send Message
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Preview Dialog */}
      <ImagePreviewDialog
        open={!!previewImage}
        onClose={handleClosePreview}
        imageUrl={previewImage}
        title="Reference Image Preview"
      />
    </>
  );
};

export default CustomerContactDialog;
