import { useState, useEffect } from 'react';
import { Box, TextField, Typography } from '@mui/material';
import CustomDialog from '@/components/CustomDialog';
import ConfirmationDialog from '@/components/ConfirmationDialog';

interface FlagItemDialogProps {
  open: boolean;
  onClose: () => void;
  currentItem: any;
  onFlag: (flagReason: string, flagged: boolean) => void;
}

const FlagItemDialog = ({ open, onClose, currentItem, onFlag }: FlagItemDialogProps) => {
  const [flagNotes, setFlagNotes] = useState(currentItem?.flagReason || '');
  const [flagReasonError, setFlagReasonError] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const isFlagged = currentItem?.flagged || false;

  useEffect(() => {
    if (open) {
      setFlagNotes(currentItem?.flagReason || '');
      setFlagReasonError(false);
    }
  }, [open, currentItem?.flagReason]);

  const handleFlagItem = () => {
    if (!currentItem || !currentItem.id) {
      return;
    }
    if (isFlagged) {
      // Unflag the item
      onFlag('', false);
    } else {
      // Flag the item
      if (!flagNotes) {
        setFlagReasonError(true);
        return;
      }
      onFlag(flagNotes, true);
    }
  };

  const handleClose = () => {
    if (!isFlagged && flagNotes) {
      setConfirmDialogOpen(true);
    } else {
      onClose();
    }
  };

  const handleConfirmClose = () => {
    setConfirmDialogOpen(false);
    onClose();
  };

  return (
    <>
      <CustomDialog
        open={open}
        title={isFlagged ? 'Unflag Item' : 'Flag Item'}
        confirmLabel={isFlagged ? 'Unflag Item' : 'Flag Item'}
        cancelLabel="Cancel"
        confirmColor={isFlagged ? 'error' : 'warning'}
        disableConfirm={!isFlagged && !flagNotes}
        onConfirm={handleFlagItem}
        onCancel={handleClose}
      >
        <Box
          sx={{
            mt: 2,
            width: '100%',
            overflow: 'visible',
            '& .MuiFormControl-root': {
              width: '100%',
            },
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            Item: {currentItem?.itemNumber || 'N/A'}
          </Typography>
          <Typography variant="subtitle2" gutterBottom>
            SKU: {currentItem?.productSku?.sku || 'Not Provided'}
          </Typography>
          <TextField
            fullWidth
            label={isFlagged ? 'Current Flag Reason' : 'Flagged Reason'}
            multiline
            rows={4}
            value={flagNotes}
            onChange={e => setFlagNotes(e.target.value)}
            placeholder={
              isFlagged ? 'No reason provided' : 'Add any additional details about the flag...'
            }
            sx={{ mb: 3, mt: 3 }}
            error={!isFlagged && flagReasonError}
            helperText={!isFlagged && flagReasonError ? 'Please provide a reason for flagging' : ''}
            disabled={isFlagged}
          />
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ display: 'block', wordBreak: 'break-word' }}
          >
            {isFlagged
              ? 'Unflagging this item will mark the entire order as unflagged and it will appear in the unFlagged Orders list.'
              : 'Flagging this item will mark the entire order as flagged and it will appear in the Flagged Orders list.'}
          </Typography>
        </Box>
      </CustomDialog>

      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have entered flag information. Are you sure you want to close without flagging the item?"
        confirmLabel="Discard"
        cancelLabel="Continue Flagging"
        confirmColor="error"
        onConfirm={handleConfirmClose}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </>
  );
};

export default FlagItemDialog;
