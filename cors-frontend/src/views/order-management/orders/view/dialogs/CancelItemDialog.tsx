import { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography,
} from '@mui/material';
import CustomDialog from '@/components/CustomDialog';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { useSelector } from 'react-redux';
import { CancelItemDialogProps } from '@/types/cancel-item.types';
import { RootState } from '@/redux-store';
import { FieldsType, Options } from '@/redux-store/stores/common.store';

const CancelItemDialog = ({ open, onClose, currentItem, onCancel }: CancelItemDialogProps) => {
  const [cancelReason, setCancelReason] = useState('');
  const [cancelReasonError, setCancelReasonError] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const commonState = useSelector((state: RootState) => state.common);

  let cancelReasonField = commonState.ordersTableConfig?.allFields?.find(
    (field: FieldsType) => field.key === 'lineItems.cancelReason',
  );

  if (!cancelReasonField && commonState.simpleFields?.allFields) {
    cancelReasonField = commonState.simpleFields.allFields.find(
      (field: FieldsType) => field && field.key === 'lineItems.cancelReason',
    );
  }

  const cancelReasons = cancelReasonField?.options?.map((option: Options) => option.value) || [];

  useEffect(() => {
    if (open) {
      setCancelReason('');
      setCancelReasonError(false);
    }
  }, [open]);

  const handleCancelItem = () => {
    if (!cancelReason) {
      setCancelReasonError(true);
      return;
    }
    onCancel(cancelReason);
  };

  const handleClose = () => {
    if (cancelReason) {
      setConfirmDialogOpen(true);
    } else {
      onClose();
    }
  };

  const handleConfirmClose = () => {
    setConfirmDialogOpen(false);
    onClose();
  };

  return (
    <>
      <CustomDialog
        open={open}
        title="Cancel Item"
        confirmLabel="Cancel Item"
        cancelLabel="Close"
        confirmColor="error"
        disableConfirm={!cancelReason}
        onConfirm={handleCancelItem}
        onCancel={handleClose}
      >
        <Box sx={{ mt: 2, mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Item SKU: {currentItem?.productSku?.sku || 'Not Provided'}
          </Typography>
          <Typography variant="subtitle1" gutterBottom>
            Quantity: {currentItem?.quantity || '0'}
          </Typography>

          <FormControl fullWidth sx={{ mb: 3 }} error={cancelReasonError}>
            <InputLabel id="cancel-reason-label">Cancel Reason</InputLabel>
            <Select
              labelId="cancel-reason-label"
              value={cancelReason}
              label="Cancel Reason"
              onChange={e => {
                setCancelReason(e.target.value);
                setCancelReasonError(false);
              }}
            >
              {cancelReasons.length > 0 ? (
                cancelReasons.map((reason: any) => (
                  <MenuItem key={reason} value={reason}>
                    {reason}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled value="">
                  No cancel reasons available
                </MenuItem>
              )}
            </Select>
            {cancelReasonError && (
              <FormHelperText>Please select a cancellation reason</FormHelperText>
            )}
          </FormControl>

          <Typography variant="caption" color="text.secondary">
            Note: This will only cancel the item in CORS. You will need to manually cancel it in
            Shopify.
          </Typography>
        </Box>
      </CustomDialog>

      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have selected a cancellation reason. Are you sure you want to close without cancelling the item?"
        confirmLabel="Discard"
        cancelLabel="Continue"
        confirmColor="error"
        onConfirm={handleConfirmClose}
        onCancel={() => setConfirmDialogOpen(false)}
      />
    </>
  );
};

export default CancelItemDialog;
