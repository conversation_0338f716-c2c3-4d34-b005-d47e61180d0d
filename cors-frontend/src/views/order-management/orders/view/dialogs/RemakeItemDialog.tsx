import { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  ListItemText,
  CircularProgress,
  Typography,
} from '@mui/material';
import CustomDialog from '@/components/CustomDialog';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import ImageUploadSection from '@/components/ImageUploadSection';
import SearchableSelect from '@/components/SearchableSelect';
import { RemakeItemDialogProps } from '@/types/remake-item.types';
import { toast } from 'react-toastify';
import { uploadImage } from './EditItemDialog';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux-store';

const RemakeItemDialog = ({ open, onClose, currentItem, onSubmit }: RemakeItemDialogProps) => {
  const [selectedRemakeReasons, setSelectedRemakeReasons] = useState<string[]>([]);
  const [selectedDetailedReasons, setSelectedDetailedReasons] = useState<string[]>([]);
  const [remakeSku, setRemakeSku] = useState('');
  const [selectedSkus, setSelectedSkus] = useState<Array<{ key: string; value: string }>>([]);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [remakeReasonError, setRemakeReasonError] = useState(false);
  const [detailedReasonError, setDetailedReasonError] = useState(false);
  const [imageUploadError, setImageUploadError] = useState(false);
  const [availableDetailedReasons, setAvailableDetailedReasons] = useState<string[]>([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [maxImages, setMaxImages] = useState<number>(5);
  const commonState = useSelector((state: RootState) => state.common);

  let remakeReasonField;
  if (commonState.simpleFields?.allFields) {
    remakeReasonField = commonState.simpleFields.allFields.find(
      field => field && field.key === 'remakeReason',
    );
  }

  const remakeReasons = remakeReasonField?.options?.map(option => option.value) || [];

  let detailedRemakeReasonField;
  if (commonState.simpleFields?.allFields) {
    detailedRemakeReasonField = commonState.simpleFields.allFields.find(
      field => field && field.key === 'detailedRemakeReason',
    );
  }

  const detailedRemakeReasons = detailedRemakeReasonField?.remakeReason || {};

  const extractPetsCount = (skuKey: string): number => {
    const petsMatch = skuKey.match(/(\d+)PETS/i);
    if (petsMatch && petsMatch[1]) {
      const count = parseInt(petsMatch[1], 10);
      return isNaN(count) ? 1 : count;
    }
    return 1;
  };

  useEffect(() => {
    if (open && currentItem) {
      const skuKey = currentItem?.productSku?.sku || '';
      setRemakeSku(currentItem?.productSku?.id || '');
      setSelectedSkus(
        currentItem?.productSku?.id
          ? [{ key: skuKey, value: currentItem?.productSku?.id || '' }]
          : [],
      );
      setSelectedRemakeReasons([]);
      setSelectedDetailedReasons([]);
      setUploadedImages([]);
      setUploadedImageUrls([]);
      setRemakeReasonError(false);
      setDetailedReasonError(false);
      setImageUploadError(false);
      setAvailableDetailedReasons([]);
      setMaxImages(extractPetsCount(skuKey));
    }
  }, [open, currentItem]);

  const hasFormValues =
    selectedRemakeReasons.length > 0 ||
    selectedDetailedReasons.length > 0 ||
    uploadedImages.length > 0 ||
    (remakeSku && remakeSku !== currentItem?.productSku?.id);

  const handleCancel = () => {
    if (hasFormValues) {
      setConfirmDialogOpen(true);
    } else {
      onClose();
    }
  };

  const handleConfirmCancel = () => {
    setConfirmDialogOpen(false);
    onClose();
  };

  const handleRemakeReasonChange = (event: any) => {
    const selectedReasons = event.target.value as string[];
    setSelectedRemakeReasons(selectedReasons);
    setRemakeReasonError(false);

    // Update available detailed reasons based on selected remake reasons
    const newDetailedReasons = selectedReasons.flatMap(
      reason => detailedRemakeReasons[reason] || [],
    );
    setAvailableDetailedReasons(newDetailedReasons);

    // Clear detailed reasons if they're no longer valid
    setSelectedDetailedReasons(prev => prev.filter(reason => newDetailedReasons.includes(reason)));
  };

  const handleDetailedReasonChange = (event: any) => {
    setSelectedDetailedReasons(event.target.value as string[]);
    setDetailedReasonError(false);
  };

  // Handlers for the ImageUploadSection component with image uploading
  const handleAddImages = async (newImages: File[]) => {
    if (newImages.length === 0) return;

    setIsUploading(true);
    setImageUploadError(false);

    try {
      // Calculate how many more images we can add
      const remainingSlots = maxImages - uploadedImages.length;
      const imagesToUpload = newImages.slice(0, remainingSlots);

      // Upload each image and get URLs
      const uploadPromises = imagesToUpload.map(file => uploadImage(file));
      const urls = await Promise.all(uploadPromises);

      // Update state with new images and their URLs
      setUploadedImages(prev => [...prev, ...imagesToUpload]);
      setUploadedImageUrls(prev => [...prev, ...urls]);

      toast.success(`${imagesToUpload.length} image(s) uploaded successfully`);
    } catch (error) {
      toast.error('Failed to upload images. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
    setUploadedImageUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handlePreviewImage = (file: File) => {
    // Find the corresponding URL for this file
    const index = uploadedImages.findIndex(img => img === file);
    if (index !== -1 && uploadedImageUrls[index]) {
      setPreviewImage(uploadedImageUrls[index]);
    } else {
      // Fallback to local URL if server URL not available
      setPreviewImage(URL.createObjectURL(file));
    }
  };

  const handleClosePreview = () => {
    setPreviewImage(null);
  };

  const handleSubmitRemake = () => {
    // Validate form
    let hasError = false;

    if (selectedRemakeReasons.length === 0) {
      setRemakeReasonError(true);
      hasError = true;
    }

    if (selectedDetailedReasons.length === 0) {
      setDetailedReasonError(true);
      hasError = true;
    }

    if (uploadedImages.length === 0) {
      setImageUploadError(true);
      hasError = true;
    }

    if (hasError) return;

    onSubmit({
      remakeReasons: selectedRemakeReasons,
      detailedReasons: selectedDetailedReasons,
      sku: remakeSku,
      images: uploadedImages,
      imageUrls: uploadedImageUrls,
    });
  };

  // Check if form is valid for enabling submit button
  const isFormValid =
    selectedRemakeReasons.length > 0 &&
    selectedDetailedReasons.length > 0 &&
    uploadedImages.length > 0 &&
    remakeSku;

  return (
    <>
      <CustomDialog
        open={open}
        title="Remake Item"
        confirmLabel="Submit"
        cancelLabel="Cancel"
        confirmColor="primary"
        disableConfirm={!isFormValid || isUploading}
        onConfirm={handleSubmitRemake}
        onCancel={handleCancel}
        maxWidth="md"
      >
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ mb: 3 }}>
            Original Item SKU: {currentItem?.productSku?.sku || 'Not available'}
          </Typography>

          {/* SearchableSelect for SKU selection */}
          <FormControl fullWidth sx={{ mb: 3 }}>
            <SearchableSelect
              attr={{
                key: 'sku',
                label: 'Remake Item SKU',
                fetch_db: true,
                filter_key: 'sku',
                type: 'multi_select',
                usage: 'filter',
                backend_key: 'id',
                secondary_key: 'sku',
              }}
              value={selectedSkus}
              handleValueChange={(field: string, newValue: any) => {
                if (Array.isArray(newValue)) {
                  if (newValue.length > 1) {
                    setAlertDialogOpen(true);
                    return;
                  }
                  setSelectedSkus(newValue);
                  if (newValue.length > 0) {
                    const firstItem = newValue[0];
                    setRemakeSku(firstItem.value);

                    setMaxImages(extractPetsCount(firstItem.key));
                  } else {
                    setRemakeSku('');
                    setMaxImages(1);
                  }
                }
              }}
              multiple={true}
              serachUrl="product-sku"
              sx={true}
            />
          </FormControl>

          <FormControl fullWidth sx={{ mb: 3 }} error={remakeReasonError}>
            <InputLabel id="remake-reason-label">Remake Reason</InputLabel>
            <Select
              labelId="remake-reason-label"
              multiple
              value={selectedRemakeReasons}
              label="Remake Reason"
              onChange={handleRemakeReasonChange}
              renderValue={selected => (selected as string[]).join(', ')}
            >
              {remakeReasons.length > 0 ? (
                remakeReasons.map(reason => (
                  <MenuItem key={reason.toString()} value={reason.toString()}>
                    <Checkbox checked={selectedRemakeReasons.indexOf(String(reason)) > -1} />
                    <ListItemText primary={reason} />
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled value="">
                  No remake reasons available
                </MenuItem>
              )}
            </Select>
            {remakeReasonError && (
              <FormHelperText>Please select at least one remake reason</FormHelperText>
            )}
          </FormControl>

          <FormControl fullWidth sx={{ mb: 3 }} error={detailedReasonError}>
            <InputLabel id="detailed-reason-label">Detailed Remake Reason</InputLabel>
            <Select
              labelId="detailed-reason-label"
              multiple
              value={selectedDetailedReasons}
              label="Detailed Remake Reason"
              onChange={handleDetailedReasonChange}
              renderValue={selected => (selected as string[]).join(', ')}
              disabled={availableDetailedReasons.length === 0}
            >
              {availableDetailedReasons.map(reason => (
                <MenuItem key={reason} value={reason}>
                  <Checkbox checked={selectedDetailedReasons.indexOf(reason) > -1} />
                  <ListItemText primary={reason} />
                </MenuItem>
              ))}
            </Select>
            {detailedReasonError && (
              <FormHelperText>Please select at least one detailed reason</FormHelperText>
            )}
          </FormControl>

          {/* Image Upload Section with pet labels */}
          <ImageUploadSection
            images={uploadedImages}
            onAddImages={handleAddImages}
            onRemoveImage={handleRemoveImage}
            onPreviewImage={handlePreviewImage}
            maxImages={maxImages}
            error={imageUploadError}
            errorMessage="Please upload at least one image"
            title="Upload Images (Required)"
            helperText={
              maxImages === 1
                ? 'You can upload 1 image for this item'
                : `You can upload up to ${maxImages} images for this item`
            }
            showPetLabels={true}
          />

          {isUploading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <CircularProgress size={24} />
              <Typography variant="body2" sx={{ ml: 1 }}>
                Uploading images...
              </Typography>
            </Box>
          )}
        </Box>
      </CustomDialog>

      <ImagePreviewDialog
        open={!!previewImage}
        onClose={handleClosePreview}
        imageUrl={previewImage}
        title="Image Preview"
      />

      <ConfirmationDialog
        open={confirmDialogOpen}
        title="Discard Changes"
        message="You have entered remake information. Are you sure you want to close without submitting?"
        confirmLabel="Discard"
        cancelLabel="Continue Editing"
        confirmColor="error"
        onConfirm={handleConfirmCancel}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      <ConfirmationDialog
        open={alertDialogOpen}
        title="Selection Limit"
        message="Only one item can be selected at a time. Please remove the current selection before adding a new one."
        confirmLabel="OK"
        cancelLabel=""
        confirmColor="primary"
        onConfirm={() => setAlertDialogOpen(false)}
        onCancel={() => setAlertDialogOpen(false)}
      />
    </>
  );
};

export default RemakeItemDialog;
