'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Typography, Card, CardContent, Button, Box, Divider, Tab, Tabs } from '@mui/material';
import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import { useSettings } from '@/@core/hooks/useSettings';
import LoadingView from '@/components/LoadingView';
import OrderDetailsDialog from './tabs/OrderDetailsDialog';
import OrderDetailsTab from './tabs/OrderDetailsTab';

const OrderView = ({ orderData }: { orderData: any }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [itemNotes, setItemNotes] = useState<Record<string, string[]>>({});
  const [activeTab, setActiveTab] = useState('0');

  let updatePageSettings;
  try {
    const { updatePageSettings: updateSettings } = useSettings();
    updatePageSettings = updateSettings;
  } catch (error) {
    updatePageSettings = () => {};
  }

  // Set page settings once on component mount
  useEffect(() => {
    if (updatePageSettings) {
      return updatePageSettings({
        skin: 'default',
      });
    }
  }, [updatePageSettings]);

  const handleBack = () => {
    setLoading(true);
    router.push('/ordermanagement/orders');
  };

  const handleOpenDetails = () => {
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  if (loading) {
    return <LoadingView />;
  }

  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">Order #{orderData.shopifyOrderNumber}</Typography>
          <Box>
            <Button variant="contained" color="primary" onClick={handleOpenDetails} sx={{ mr: 2 }}>
              Order Details
            </Button>
            <Button variant="outlined" color="secondary" onClick={handleBack}>
              Back to Orders
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {/* Order Tabs Component */}
        <TabContext value={activeTab}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="order tabs"
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
            >
              <Tab label=" Main Order Details" value="0" />
              {/* <Tab label="Order History" value="1" /> */}
            </Tabs>
          </Box>
          <TabPanel value="0">
            <OrderDetailsTab orderData={orderData} />
          </TabPanel>
          {/* <TabPanel value="1">
            <OrderHistoryTab orderData={orderData} />
          </TabPanel> */}
        </TabContext>

        {/* Order Details Dialog */}
        <OrderDetailsDialog
          open={detailsOpen}
          onClose={handleCloseDetails}
          orderData={orderData}
          itemNotes={itemNotes}
          setItemNotes={setItemNotes}
        />
      </CardContent>
    </Card>
  );
};

export default OrderView;
