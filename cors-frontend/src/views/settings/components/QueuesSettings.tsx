'use client';
import { <PERSON>, Button, TextField, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { useForm, Resolver } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from 'react';
import { TabQueues } from '@/types/queues.types';
import { toast } from 'react-toastify';
import LoadingView from '@/components/LoadingView';
import useApiCall from '@/hooks/useApiCall';

interface QueueSettingsForm {
  queues: Array<{
    id: string;
    maxItemsToAssign: number;
    name: string;
  }>;
}

const validationSchema = yup.object().shape({
  queues: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.string().required('Queue ID is required'),
        maxItemsToAssign: yup
          .number()
          .required('Number of Items is required')
          .min(1, 'Number of Items must be at least 1'),
        name: yup.string().required('Queue Name is required'),
      }),
    )
    .optional(),
});

const QueuesSettings = () => {
  const {
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = useForm<QueueSettingsForm>({
    resolver: yupResolver(validationSchema) as unknown as Resolver<QueueSettingsForm>,
  });

  const { isLoading: isUpdating, makeRequest: updateQueues } = useApiCall<TabQueues[]>(
    '/workflow-queues/update-settings',
    'put',
  );

  const {
    data: queues,
    isLoading: isFetching,
    makeRequest: fetchQueues,
  } = useApiCall<TabQueues[]>('/workflow-queues/all', 'get', true);

  const onSubmit = async (data: QueueSettingsForm) => {
    const response = await updateQueues({ body: data?.queues });
    if (response) {
      fetchQueues();
      toast.success('Queues updated successfully');
    }
  };

  useEffect(() => {
    if (queues) {
      setValue(
        'queues',
        queues?.map(queue => ({
          id: queue.id,
          maxItemsToAssign: queue.maxItemsToAssign || 0,
          name: queue.name,
        })),
      );
    }
  }, [queues]);

  if (isFetching) {
    return <LoadingView />;
  }
  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
      <Typography variant="h6" sx={{ mb: 4 }}>
        Number of Images to be assigned in Queues
      </Typography>

      <Grid container spacing={3}>
        {queues?.map((queue, index) => (
          <Grid key={queue.id} size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label={queue.name}
              type="number"
              error={!!errors.queues?.[index]?.maxItemsToAssign}
              helperText={errors.queues?.[index]?.maxItemsToAssign?.message}
              {...register(`queues.${index}.maxItemsToAssign`, {
                setValueAs: value => (value === '' ? 0 : Number(value)),
              })}
            />
          </Grid>
        ))}

        <Grid size={{ xs: 12 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <Button type="submit" variant="contained" color="primary" disabled={isUpdating}>
              {isUpdating ? 'Saving...' : 'Save Changes'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default QueuesSettings;
