'use client';

import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import { useRouter } from 'next/navigation';

import UserListTable from './UserListTable';
import { UsersType } from '@/types/userTypes';
import { Actions, ActionsTarget } from '@/libs/casl/ability';
import Button from '@/components/Button';

const UserList = ({ userData, count }: { userData?: UsersType[]; count: number }) => {
  const router = useRouter();

  const handleAddUser = () => {
    router.push('/users/add');
  };

  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
        <Typography variant="h4">Users List</Typography>

        <Button
          onClick={handleAddUser}
          ButtonAction={Actions.CreateUsers}
          actionTarget={ActionsTarget.UserManagment}
          variant="contained"
          size="medium"
          title="Add User"
        />
      </Grid>

      <Grid size={{ xs: 12 }}>
        <UserListTable tableData={userData} count={count} />
      </Grid>
    </Grid>
  );
};

export default UserList;
