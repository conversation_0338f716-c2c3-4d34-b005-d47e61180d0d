'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import { useForm, Controller } from 'react-hook-form';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import FormHelperText from '@mui/material/FormHelperText';
import Divider from '@mui/material/Divider';
import { toast } from 'react-toastify';
import InputAdornment from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import OutlinedInput from '@mui/material/OutlinedInput';
import Box from '@mui/material/Box';
import apiClient from '@/utils/axios';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import { Role, UserFormData } from '@/types/userTypes';
import Autocomplete from '@mui/material/Autocomplete';
import { useDebounce } from 'react-use';
import { validatePasswordWithError } from '@/utils/validation';

const UserAddForm = ({
  roles,
  userData,
  isEdit = false,
}: {
  roles: Role[];
  userData?: UserFormData;
  isEdit?: boolean;
}) => {
  const router = useRouter();
  const { data: session, update } = useSession();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [openTransferDialog, setOpenTransferDialog] = useState(false);
  const [originalUserData] = useState(userData);
  const [rolesList, setRolesList] = useState<Role[]>(roles || []);
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [loadingRoles, setLoadingRoles] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const currentUserHasOwnerRole = session?.user?.roles?.some(
    role => role.name.toLowerCase() === 'owner',
  );

  const userHasOwnerRole =
    userData?.roleId &&
    roles.some(role => role.id === userData.roleId && role.name.toLowerCase() === 'owner');

  const showTransferButton =
    isEdit && currentUserHasOwnerRole && !userHasOwnerRole && session?.user?.id !== userData?.id;

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<UserFormData>({
    defaultValues: {
      id: userData?.id || undefined,
      firstName: userData?.firstName || '',
      lastName: userData?.lastName || '',
      email: userData?.email || '',
      password: isEdit ? '********' : '',
      roleId: userData?.roleId || '',
      isActive: userData?.isActive !== undefined ? userData.isActive : true,
    },
  });

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);

    const { isValid, error } = validatePasswordWithError(newPassword);
    setPasswordError(error || '');
  };

  const onSubmit = async (data: UserFormData) => {
    if (!session?.user?.token) {
      toast.error('Authentication required');
      return;
    }

    if (!isEdit) {
      const { isValid, error } = validatePasswordWithError(password);
      if (!isValid) {
        setPasswordError(error || '');
        return;
      }
    }

    setLoading(true);

    try {
      if (isEdit && userData?.id) {
        // Create an object with only the changed fields
        const changedFields: Partial<UserFormData> = {};

        // Compare each field with the original data
        if (data.firstName !== originalUserData?.firstName) {
          changedFields.firstName = data.firstName;
        }
        if (data.lastName !== originalUserData?.lastName) {
          changedFields.lastName = data.lastName;
        }
        if (data.email !== originalUserData?.email) {
          changedFields.email = data.email;
        }
        if (data.roleId !== originalUserData?.roleId) {
          changedFields.roleId = data.roleId;
        }
        if (data.isActive !== originalUserData?.isActive) {
          changedFields.isActive = data.isActive;
        }

        // Add roleIds for the API
        const updateData = {
          ...changedFields,
          roleIds: changedFields.roleId ? [changedFields.roleId] : undefined,
        };

        // Only send the request if there are changes
        if (Object.keys(changedFields).length > 0) {
          await apiClient.put(`/users/${userData.id}`, updateData);
          toast.success('User updated successfully');
          router.push('/users');
        } else {
          toast.info('No changes detected');
          router.push('/users');
        }
      } else if (isEdit) {
        toast.error('User ID is missing. Cannot update user.');
        return;
      } else {
        // For new users, send all fields
        const newUserData = {
          ...data,
          roleIds: [data.roleId],
        };

        await apiClient.post('/users', newUserData);
        toast.success('User created successfully');
        router.push('/users');
      }
    } catch (error: any) {
      console.error(`Failed to ${isEdit ? 'update' : 'create'} user:`, error);

      // Check if the error is about email uniqueness
      if (error.response?.data?.message?.includes('Email must be unique')) {
        toast.error('Email address already exists. Please try a new email address.');
      } else {
        // Default error message
        toast.error(`Failed to ${isEdit ? 'update' : 'create'} user. Please try again.`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (isDirty) {
      setOpenDialog(true);
    } else {
      router.push('/users');
    }
  };

  const handleDialogConfirm = () => {
    setOpenDialog(false);
    router.push('/users');
  };

  const handleTransferOwnership = async () => {
    if (!userData?.id || !session?.user?.token) {
      toast.error('User ID or authentication is missing');
      return;
    }

    setLoading(true);

    try {
      await apiClient.put(`/users/transfer-ownership?newOwnerId=${userData.id}`);
      toast.success('Ownership transferred successfully. You will be logged out.');
      localStorage.removeItem('userData');
      setTimeout(() => {
        signOut({ callbackUrl: '/login?message=ownership-transferred' });
      }, 1500);
    } catch (error) {
      console.error('Failed to transfer ownership:', error);
      toast.error('Failed to transfer ownership. Please try again.');
      setLoading(false);
      setOpenTransferDialog(false);
    }
  };

  const fetchRoles = async (pageNum = 1, q = '') => {
    try {
      setLoadingRoles(true);
      const { data } = await apiClient.get('/roles', {
        params: {
          page: pageNum,
          limit: 25,
          q: q ? `name:like:${q}` : undefined,
        },
      });

      const newRoles = data?.data || [];
      setRolesList(prev => (pageNum === 1 ? newRoles : [...prev, ...newRoles]));
      setHasMore(newRoles.length === 25);
    } catch (error) {
      console.error('Error loading roles:', error);
    } finally {
      setLoadingRoles(false);
    }
  };

  useDebounce(
    () => {
      setPage(1);
      fetchRoles(1, search);
    },
    500,
    [search],
  );

  return (
    <>
      <Card>
        <CardHeader title={isEdit ? 'Edit User' : 'Add New User'} />
        <Divider />
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={5}>
              <Grid size={{ xs: 12 }} sx={{ mt: errors.firstName ? 3 : 0 }}>
                <Controller
                  name="firstName"
                  control={control}
                  rules={{ required: 'First name is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="First Name"
                      placeholder="John"
                      error={Boolean(errors.firstName)}
                      helperText={errors.firstName?.message}
                      sx={{ mb: 5 }}
                      slotProps={{
                        formHelperText: { sx: { mt: 4 } },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12 }} sx={{ mt: errors.lastName ? 3 : 0 }}>
                <Controller
                  name="lastName"
                  control={control}
                  rules={{ required: 'Last name is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Last Name"
                      placeholder="Doe"
                      error={Boolean(errors.lastName)}
                      helperText={errors.lastName?.message}
                      sx={{ mb: 5 }}
                      slotProps={{
                        formHelperText: { sx: { mt: 4 } },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12 }} sx={{ mt: errors.email ? 3 : 0 }}>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      type="email"
                      label="Email"
                      placeholder="<EMAIL>"
                      error={Boolean(errors.email)}
                      helperText={errors.email?.message}
                      sx={{ mb: 5 }}
                      slotProps={{
                        formHelperText: { sx: { mt: 4 } },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12 }} sx={{ mt: errors.password || passwordError ? 3 : 0 }}>
                <FormControl fullWidth sx={{ mb: 5 }}>
                  <InputLabel
                    htmlFor="password"
                    error={Boolean(errors.password) || Boolean(passwordError)}
                  >
                    Password
                  </InputLabel>
                  <Controller
                    name="password"
                    control={control}
                    rules={{ required: isEdit ? false : true }}
                    render={({ field }) => (
                      <OutlinedInput
                        {...field}
                        label={`Password`}
                        id="password"
                        error={Boolean(errors.password) || Boolean(passwordError)}
                        type={showPassword ? 'text' : 'password'}
                        disabled={isEdit}
                        value={password}
                        onChange={e => {
                          const inputEvent = e as React.ChangeEvent<HTMLInputElement>;
                          field.onChange(e);
                          handlePasswordChange(inputEvent);
                        }}
                        inputProps={{
                          autoComplete: 'new-password',
                        }}
                        endAdornment={
                          !isEdit ? (
                            <InputAdornment position="end">
                              <IconButton
                                edge="end"
                                onClick={handleTogglePasswordVisibility}
                                onMouseDown={e => e.preventDefault()}
                                aria-label="toggle password visibility"
                              >
                                <i className={showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} />
                              </IconButton>
                            </InputAdornment>
                          ) : null
                        }
                      />
                    )}
                  />
                  {(errors.password || passwordError) && (
                    <FormHelperText error sx={{ mt: 4 }}>
                      {errors.password?.message || passwordError}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid size={{ xs: 12 }} sx={{ mt: errors.roleId ? 3 : 0 }}>
                <Controller
                  name="roleId"
                  control={control}
                  rules={{ required: 'Role is required' }}
                  render={({ field: { onChange, value, ...field } }) => (
                    <FormControl fullWidth error={Boolean(errors.roleId)} sx={{ mb: 5 }}>
                      <Autocomplete
                        id="role-select"
                        options={rolesList}
                        loading={loadingRoles}
                        getOptionLabel={option => option.name}
                        value={rolesList.find(role => role.id === value) || null}
                        onChange={(_, newValue) => {
                          onChange(newValue ? newValue.id : '');
                        }}
                        onInputChange={(_, value) => {
                          setSearch(value);
                        }}
                        renderInput={params => (
                          <TextField
                            {...params}
                            label="Role"
                            error={Boolean(errors.roleId)}
                            placeholder="Search for a role"
                            slotProps={{
                              formHelperText: { sx: { mt: 4 } },
                            }}
                          />
                        )}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        renderOption={(props, option) => (
                          <li {...props} key={option.id}>
                            {option.name}
                          </li>
                        )}
                        slotProps={{
                          listbox: {
                            onScroll: event => {
                              const listboxNode = event.currentTarget;
                              if (
                                listboxNode.scrollTop + listboxNode.clientHeight >=
                                  listboxNode.scrollHeight - 1 &&
                                hasMore &&
                                !loadingRoles
                              ) {
                                const nextPage = page + 1;
                                setPage(nextPage);
                                fetchRoles(nextPage, search);
                              }
                            },
                            style: { maxHeight: '200px', overflow: 'auto' },
                          }
                        }}
                      />

                      {errors.roleId && (
                        <FormHelperText sx={{ mt: 4 }}>{errors.roleId.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid size={{ xs: 12 }} className="flex justify-end gap-4">
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 4, gap: 4 }}>
                  <Button variant="outlined" onClick={handleCancel} sx={{ ml: 2 }}>
                    Cancel
                  </Button>

                  {showTransferButton && (
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => setOpenTransferDialog(true)}
                    >
                      Transfer Ownership
                    </Button>
                  )}
                  <Button type="submit" variant="contained" disabled={loading}>
                    {loading ? 'Processing...' : isEdit ? 'Update User' : 'Create User'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>

      <ConfirmationDialog
        open={openDialog}
        title="Unsaved Changes"
        message="You have unsaved changes. Are you sure you want to leave without saving?"
        confirmLabel="Leave"
        confirmColor="primary"
        onConfirm={handleDialogConfirm}
        onCancel={() => setOpenDialog(false)}
      />

      <ConfirmationDialog
        open={openTransferDialog}
        title="Transfer Ownership"
        message={
          <>
            <p>Are you sure you want to transfer ownership to this user?</p>
            <p className="mt-2 text-red-500 font-bold">
              Warning: This action cannot be undone. You will lose your Owner privileges.
            </p>
          </>
        }
        confirmLabel="Transfer"
        confirmColor="error"
        loading={loading}
        onConfirm={handleTransferOwnership}
        onCancel={() => setOpenTransferDialog(false)}
      />
    </>
  );
};

export default UserAddForm;
