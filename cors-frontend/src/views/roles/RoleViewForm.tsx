'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Chip from '@mui/material/Chip';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid2';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

import { RolePermission, RolesData } from '@/types/roleTypes';

const RoleViewForm = ({
  permissions,
  roleData,
}: {
  permissions: RolePermission[];
  roleData: RolesData;
}) => {
  const router = useRouter();
  const [selectedPermissions, setSelectedPermissions] = useState<Record<string, string[]>>({});
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    if (!roleData) return;

    const permMap: Record<string, string[]> = {};

    if (roleData.rolePermissions && Array.isArray(roleData.rolePermissions)) {
      roleData.rolePermissions.forEach(perm => {
        if (perm.resource && Array.isArray(perm.actions)) {
          permMap[perm.resource] = [...perm.actions];
        }
      });
    }

    setSelectedPermissions(permMap);

    const timer = setTimeout(() => {
      setInitialLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [roleData]);

  const handleBack = () => {
    router.push('/roles');
  };

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <CircularProgress />
      </div>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">View Role Details</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            {roleData.name !== 'Owner' && (
              <RoleProtected action={Actions.EditRole} actionTarget={ActionsTarget.RoleManagement}>
                <Button
                  variant="contained"
                  onClick={() => router.push(`/roles/edit/${roleData.id}`)}
                >
                  Edit Role
                </Button>
              </RoleProtected>
            )}
            <Button variant="outlined" onClick={handleBack}>
              Back to Roles
            </Button>
          </Box>
        </Box>

        <Grid container spacing={4}>
          <Grid>
            <Typography>
              <strong>Name:</strong> {roleData.name}
            </Typography>
            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
              <Typography component="span">
                <strong>Status:</strong>
              </Typography>
              <Chip
                label={roleData.isActive ? 'Active' : 'InActive'}
                color={roleData.isActive ? 'success' : roleData.isActive ? 'warning' : 'error'}
                size="small"
                sx={{ ml: 1 }}
              />
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4 }} />

        <Typography variant="h6" sx={{ mb: 3 }}>
          Role Permissions
        </Typography>

        <Grid container spacing={4}>
          {permissions.map(permission => (
            <Grid key={permission.resource}>
              <Card variant="outlined" sx={{ mb: 4 }}>
                <CardContent>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    {permission.resource.charAt(0).toUpperCase() + permission.resource.slice(1)}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {permission.actions.map(action => {
                      const isSelected = selectedPermissions[permission.resource]?.includes(action);
                      return (
                        <FormControlLabel
                          key={action}
                          control={<Checkbox checked={isSelected} disabled size="small" />}
                          label={action}
                        />
                      );
                    })}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default RoleViewForm;
