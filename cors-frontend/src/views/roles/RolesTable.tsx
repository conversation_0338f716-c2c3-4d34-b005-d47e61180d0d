'use client';
import { useState, useMemo, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'react-toastify';
import apiClient from '@/utils/axios';
import CircularProgress from '@mui/material/CircularProgress';
import { useRouter } from 'next/navigation';
import Chip from '@mui/material/Chip';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { createColumnHelper } from '@tanstack/react-table';
import type { ColumnDef } from '@tanstack/react-table';
import { UsersType } from '@/types/userTypes';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import DataTable from '@/components/Datatable';
import { RolesData, UserStatusType } from '@/types/roleTypes';
import { RoleProtected } from '@/components/ProtectedRoleWrapper';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

const RolesTable = ({ tableData, count }: { tableData?: UsersType[]; count: number }) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [data, setData] = useState<RolesData[]>(tableData as unknown as RolesData[]);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<RolesData>();
  const [selectedRole, setSelectedRole] = useState<RolesData | null>(null);
  const [loading, setLoading] = useState(false);

  const handleNavigation = (e: React.MouseEvent, path: string) => {
    if (e.ctrlKey || e.metaKey) {
      window.open(path, '_blank');
    } else {
      router.push(path);
    }
  };

  useEffect(() => {
    setData(tableData as unknown as RolesData[]);
  }, [tableData]);

  const refreshRoleData = async (roleId: number) => {
    try {
      const response = await apiClient.get(`/roles/${roleId}`);
      setData(prevData => prevData.map(role => (role.id === roleId ? response.data : role)));
    } catch (error) {
      console.error('Failed to refresh role data:', error);
    }
  };

  const handleStatusChange = (role: RolesData) => {
    setSelectedRole(role);
    setOpenStatusModal(true);
  };

  const handleConfirmStatusChange = async () => {
    if (!selectedRole || !session?.user?.token) return;

    setLoading(true);

    try {
      await apiClient.patch(`/roles/${selectedRole.id}`, {
        id: selectedRole.id,
        isActive: !selectedRole.isActive,
      });

      await refreshRoleData(selectedRole?.id as number);

      toast.success(`Role ${selectedRole.isActive ? 'deactivated' : 'activated'} successfully`);
      setOpenStatusModal(false);
    } catch (error) {
      console.error('Failed to update role status:', error);
      toast.error('Failed to update role status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = (role: RolesData) => {
    setRoleToDelete(role);
    setOpenDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!roleToDelete || !session?.user?.token) return;

    setLoading(true);

    try {
      await apiClient.delete(`/roles/${roleToDelete.id}`);
      setData(prevData => prevData.filter(role => role.id !== roleToDelete.id));
      toast.success(`Role "${roleToDelete.name}" deleted successfully`);
      setOpenDeleteModal(false);
    } catch (error) {
      console.error('Failed to delete role:', error);
      toast.error('Failed to delete role. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const columnHelper = createColumnHelper<RolesData>();

  const columns = useMemo<ColumnDef<RolesData>[]>(
    () => [
      columnHelper.accessor('name', {
        enableColumnFilter: true,
        header: 'Role',
        cell: info => <Typography color="text.primary">{info.getValue()}</Typography>,
        enableSorting: true,
      }) as ColumnDef<RolesData>,
      columnHelper.accessor('permissionCount', {
        header: 'Permissions',
        enableColumnFilter: true,
        meta: { filterType: 'num' },
        cell: info => {
          return <Chip label={`${info.getValue()} Actions`} size="small" color="primary" />;
        },
        enableSorting: true,
      }) as ColumnDef<RolesData>,
      columnHelper.accessor('isActive', {
        header: 'Status',
        cell: ({ row }) => {
          const isActive = row.original?.isActive;
          const statusText = isActive ? 'Active' : 'Inactive';
          const statusColor = isActive ? 'success' : 'error';

          return (
            <div className="flex items-center gap-3">
              <Chip
                variant="tonal"
                label={statusText}
                size="small"
                color={statusColor}
                className="capitalize"
              />
            </div>
          );
        },
        enableColumnFilter: true,
        meta: {
          filterType: 'select',
          isBoolean: true,
          booleanOptions: { Active: true, Inactive: false },
        },

        enableSorting: true,
      }) as ColumnDef<RolesData>,
      columnHelper.display({
        id: 'action',
        header: 'Actions',
        enableColumnFilter: false,
        cell: ({ row }) => {
          const isOwnerRole = row.original.name.toLowerCase() === 'owner';

          return (
            <div className="flex items-center">
              <RoleProtected
                action={Actions.ViewRoleDetail}
                actionTarget={ActionsTarget.RoleManagement}
              >
                <IconButton
                  onClick={e => {
                    handleNavigation(e, `/roles/view/${row.original.id}`);
                  }}
                >
                  <i className="ri-eye-line text-textSecondary" />
                </IconButton>
              </RoleProtected>
              <RoleProtected action={Actions.EditRole} actionTarget={ActionsTarget.RoleManagement}>
                <IconButton
                  onClick={e => {
                    if (!isOwnerRole) {
                      handleNavigation(e, `/roles/edit/${row.original.id}`);
                    }
                  }}
                  disabled={isOwnerRole}
                  sx={{ opacity: isOwnerRole ? 0.5 : 1 }}
                >
                  <i className="ri-edit-line text-textSecondary" />
                </IconButton>
              </RoleProtected>
              <RoleProtected
                action={Actions.DeleteRole}
                actionTarget={ActionsTarget.RoleManagement}
              >
                <IconButton
                  onClick={() => {
                    if (!isOwnerRole) {
                      handleDeleteRole(row?.original);
                    }
                  }}
                  sx={{ ml: 1, opacity: isOwnerRole ? 0.5 : 1 }}
                  disabled={isOwnerRole}
                >
                  <i className="ri-delete-bin-line" />
                </IconButton>
              </RoleProtected>
              <RoleProtected
                action={row.original.isActive ? Actions.DeactivateRole : Actions.ActivateRole}
                actionTarget={ActionsTarget.RoleManagement}
              >
                <Button
                  variant="outlined"
                  size="small"
                  color={row.original.isActive ? 'error' : 'success'}
                  onClick={() => {
                    if (!isOwnerRole) {
                      handleStatusChange(row.original);
                    }
                  }}
                  sx={{ ml: 1, opacity: isOwnerRole ? 0.5 : 1 }}
                  disabled={isOwnerRole}
                >
                  {row.original.isActive ? 'Deactivate' : 'Activate'}
                </Button>
              </RoleProtected>
            </div>
          );
        },
      }),
    ],
    [router],
  );

  return (
    <>
      <DataTable
        columns={columns}
        loading={loading}
        data={data}
        endpoint="roles"
        totalCount={count}
        globalSearch
      />

      <Dialog
        open={openStatusModal}
        onClose={() => !loading && setOpenStatusModal(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">Confirm Status Change</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {selectedRole && (
              <>
                Are you sure you want to {selectedRole.isActive ? 'deactivate' : 'activate'} the
                role "{selectedRole.name}"?
              </>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenStatusModal(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmStatusChange}
            autoFocus
            disabled={loading}
            color={selectedRole?.isActive ? 'error' : 'success'}
          >
            {loading ? (
              <>
                <CircularProgress size={20} color="inherit" className="mie-2" />
                Processing...
              </>
            ) : (
              'Confirm'
            )}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDeleteModal}
        onClose={() => !loading && setOpenDeleteModal(false)}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            {roleToDelete && (
              <>
                Are you sure you want to delete the role "{roleToDelete.name}"? This action cannot
                be undone.
              </>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteModal(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} autoFocus disabled={loading} color="error">
            {loading ? (
              <>
                <CircularProgress size={20} color="inherit" className="mie-2" />
                Processing...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default RolesTable;
