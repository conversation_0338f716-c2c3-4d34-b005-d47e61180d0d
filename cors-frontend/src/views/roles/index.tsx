
"use client";
import Grid from "@mui/material/Grid2";
import Typography from "@mui/material/Typography";
import RolesTable from "./RolesTable";
import { UsersType } from "@/types/userTypes";
import Button from "@/components/Button";
import { Actions, ActionsTarget } from "@/libs/casl/ability";
import { useRouter } from "next/navigation";

const Roles = ({
  userData,
  count,
}: {
  userData?: UsersType[];
  count: number;
}) => {
  const router = useRouter();

  const handleAddRole = () => {
    router.push('/roles/add');
  };

  return (
    <Grid container spacing={3}>
      <Grid
        size={{ xs: 12 }}
        className="flex justify-between items-center mb-2"
      >
        <Typography variant="h4">Roles List</Typography>
        <Button
          title="Add Role"
          variant="contained"
          size="medium"
          ButtonAction={Actions.CreateRole}
          actionTarget={ActionsTarget.RoleManagement}
          onClick={handleAddRole}
        />
      </Grid>

      <Grid size={{ xs: 12 }}>
        <RolesTable tableData={userData} count={count} />
      </Grid>
    </Grid>
  );
};

export default Roles;
