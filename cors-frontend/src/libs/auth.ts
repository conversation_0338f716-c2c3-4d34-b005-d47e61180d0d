import CredentialProvider from 'next-auth/providers/credentials'
import type { NextAuthOptions } from 'next-auth'
import { jwtDecode } from 'jwt-decode'
import apiClient from '@/utils/axios'
import { Role } from './casl/ability'
import axios from 'axios'
import { getSession } from 'next-auth/react'

interface DecodedToken {
  exp: number
  [key: string]: any
}

let failedQueue: any[] = [];


const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

const logout: () => void = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth-token');
    localStorage.removeItem('refresh-token');
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    window.location.href = '/login';
  }
};

const refreshTokenn = async (refreshToken: string) => {
  try {
    const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}auth/refresh`, {
      refresh: refreshToken,
    });

    if (response.data.access_token && typeof window !== 'undefined') {
      localStorage.setItem('auth-token', response.data.access_token);
      if (response.data.refresh_token) {
        localStorage.setItem('refresh-token', response.data.refresh_token);
      }
    }

    return response?.data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};

export const authOptions: NextAuthOptions = {


  // ** Configure one or more authentication providers
  // ** Please refer to https://next-auth.js.org/configuration/options#providers for more `providers` options
  providers: [
    CredentialProvider({
      // ** The name to display on the sign in form (e.g. 'Sign in with...')
      // ** For more details on Credentials Provider, visit https://next-auth.js.org/providers/credentials
      name: 'Credentials',
      type: 'credentials',

      /*
       * As we are using our own Sign-in page, we do not need to change
       * username or password attributes manually in following credentials object.
       */
      credentials: {},
      async authorize(credentials) {
        // Type guard for credentials
        if (credentials && (credentials as any).accessToken && (credentials as any).refreshToken) {
          // Optionally decode the access token to get user info
          const tokenPayload = jwtDecode<DecodedToken>((credentials as any).accessToken);
          return {
            id: tokenPayload.sub,
            email: tokenPayload.email,
            roles: tokenPayload.roles,
            token: (credentials as any).accessToken,
            refreshToken: (credentials as any).refreshToken,
            ...tokenPayload
          };
        }

        // Normal login flow
        const { email, password } = credentials as { email: string; password: string };
        try {
          // Use authService to call your backend API
          const data = await apiClient.post('/auth/login', { email, password });

          if (data?.data.access_token && data?.data.refresh_token) {
            const tokenPayload = jwtDecode<DecodedToken>(data.data.access_token);
            return {
              id: tokenPayload.sub,
              email: tokenPayload.email,
              roles: tokenPayload.roles,
              token: data.data.access_token,
              refreshToken: data.data.refresh_token,
              ...tokenPayload
            };
          }

          return null;
        } catch (e: any) {
          console.error('Auth error:', e);
          if (e.response && e.response.data) {
            const errorMessage = e.response.data.message || 'Authentication failed';
            throw new Error(JSON.stringify({ message: [errorMessage] }));
          }
          throw new Error(JSON.stringify({ message: [e.message || 'Authentication failed'] }));
        }
      }
    }),
    // ** ...add more providers here
  ],

  // ** Please refer to https://next-auth.js.org/configuration/options#session for more `session` options
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60 // ** 30 days
  },
  pages: {
    signIn: '/login'
  },
  callbacks: {
    async jwt({ token, user ,session,account}) {
      const now = Date.now()
      if (user) {
        const decodedToken = jwtDecode<DecodedToken>((user as any).token);
        token.id = user.id
        token.name = user.name
        token.email = user.email
        token.token = (user as any).token // Store the access token
        token.refreshToken = (user as any).refreshToken // Store refresh token
        token.accessTokenExpires = decodedToken.exp * 1000,
        token.roles = decodedToken.roles
      }

      if (
        token.accessTokenExpires &&
        typeof token.accessTokenExpires === 'number' &&
        now > token.accessTokenExpires &&
        token.refreshToken
      ) {
        // Use refreshToken from token object, not from getSession
        const refreshTokenValue = token.refreshToken;
        if (refreshTokenValue) {
          const refreshedTokens = await refreshTokenn(refreshTokenValue as string);
          if (!refreshedTokens) throw new Error('Failed to refresh token');
          const newValues = jwtDecode<DecodedToken>(refreshedTokens.access_token);

          token.token = refreshedTokens.access_token;
          token.accessTokenExpires = newValues.exp * 1000;
          token.refreshToken = refreshedTokens.refresh_token || token.refreshToken;
        } else {
          throw new Error('No refresh token found in token');
        }
      }

      // Token has expired but we're not implementing refresh yet
      // Return token with error flag to handle in session callback
      return token;
      
    },

    async session({ session, token }) {
      if (session.user) {
        // Add custom params to user in session
        session.user.token = token.token as string
        session.user.refreshToken = token.refreshToken as string
        session.user.id = token.sub as string,
        session.user.roles= token.roles as Role[]
      }

      // Add error field to session if token refresh failed
      if (token.error) {
        (session as any).error = token.error
      }

      return session
    }
  }
}

