"use client";
import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/redux-store';
import { clearFilters as clearOrdersFilters } from '@/redux-store/stores/orders.store';
import { clearFilters as clearProductsFilters } from '@/redux-store/stores/ProductSku.store';

export const useRouteBasedFilterClearing = () => {
  const pathname = usePathname();
  const dispatch = useDispatch<AppDispatch>();
  const prevPathRef = useRef('');

  useEffect(() => {
    const currentPath = pathname;
    const prevPath = prevPathRef.current;
    
    // Check if we're on products or orders pages
    const isProductsPage = (path: string) => {
      return path.startsWith('/ordermanagement/products') && !path.includes('/view/') && !path.includes('/edit');
    };
    
    const isOrdersPage = (path: string) => {
      return path.startsWith('/ordermanagement/orders') && !path.includes('/view/') && !path.includes('/edit');
    };
    
    const isProductOrOrderDetailPage = (path: string) => {
      return path.includes('/view/') || path.includes('/edit');
    };
    
    // If switching from products to orders, clear products filters only
    if (prevPath && isProductsPage(prevPath) && isOrdersPage(currentPath)) {
      dispatch(clearProductsFilters());
    }
    // If switching from orders to products, clear orders filters only
    if (prevPath && isOrdersPage(prevPath) && isProductsPage(currentPath)) {
      dispatch(clearOrdersFilters());
    }
    // If leaving products (not to a detail/edit page), clear products filters
    if (prevPath && isProductsPage(prevPath) && !isProductsPage(currentPath) && !isProductOrOrderDetailPage(currentPath)) {
      dispatch(clearProductsFilters());
    }
    // If leaving orders (not to a detail/edit page), clear orders filters
    if (prevPath && isOrdersPage(prevPath) && !isOrdersPage(currentPath) && !isProductOrOrderDetailPage(currentPath)) {
      dispatch(clearOrdersFilters());
    }
    
    prevPathRef.current = currentPath;
  }, [pathname, dispatch]);
}; 