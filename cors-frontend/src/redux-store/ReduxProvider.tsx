"use client";

import { ReactNode, useEffect } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";

// import { store } from '@/redux-store'
import { useRouter } from "next/navigation";
import { persistor, store } from ".";

interface ReduxProviderProps {
  children: ReactNode;
}

const ReduxProvider = ({ children }: ReduxProviderProps) => {
  const router = useRouter();

  // Check for token on initial load (client-side only)
  useEffect(() => {
    // This will run only on the client side
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("auth-token");

      // If no token and on a protected route, redirect to login
      if (!token && window.location.pathname.startsWith("/home")) {
        router.push("/login");
      }
    }
  }, [router]);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
};

export default ReduxProvider;
