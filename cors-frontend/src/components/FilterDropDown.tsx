import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Divider } from '@mui/material';
import Grid from '@mui/material/Grid2';
import DeleteIcon from '@mui/icons-material/Delete';
import { FieldsType, Options, Filter } from '@/redux-store/stores/common.store';
import React, { useCallback, useState } from 'react';
import apiClient from '@/utils/axios';
import CustomSelect, { Option as CustomSelectOption } from './CustomSelect';
import FilterValueField from './FilterValueField';
import { useDebounce } from 'react-use';
import { fetchShopifyVariants } from '@/actions';
import { apiCall } from '@/actions/common.actions';
import { SingleSKU } from '@/types/manual-order.type';
import debounce from 'lodash/debounce';

const filterEqData: any = {
  all: [
    { label: 'is', value: 'eq' },
    { label: 'is not', value: 'ne' },
    { label: 'contains', value: 'like' },
  ],
  number: [
    { label: 'is', value: 'eq' },
    { label: 'is not', value: 'ne' },
  ],
  date: [{ label: 'between', value: 'between' }],
  status: [
    { label: 'is', value: 'eq' },
    { label: 'is not', value: 'ne' },
  ],
  shopifyNativeVariant: [
    { label: 'is', value: 'json-eq' },
    { label: 'is not', value: 'json-ne' },
    { label: 'contains', value: 'json-like' },
  ],
};

interface FilterGroupProps {
  index: number;
  filter: Filter;
  onChange: (index: number, field: keyof Filter, value: any, subIndex: number) => void;
  onRemove: (index: number, subIndex: number) => void;
  subIndex: number;
  columnsConfig: FieldsType[];
}

interface FilterComponentProps {
  filters: Filter[][];
  columnsConfig: FieldsType[];
  handleAdd: (index?: number) => void;
  handleChange: (index: number, field: keyof Filter, value: any, subIndex: number) => void;
  handleRemove: (index: number, subIndex: number) => void;
}

const FilterGroup: React.FC<FilterGroupProps> = ({
  index,
  filter,
  onChange,
  onRemove,
  subIndex,
  columnsConfig,
}) => {
  const [shopifyVariants, setShopifyVariants] = useState<{ key: string; value: string }[]>([]);
  const [shopifyVariantsLoading, setShopifyVariantsLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [skuOptions, setSkuOptions] = useState<{ label: string; value: string }[]>([]);
  const [orderOptions, setOrderOptions] = useState<{ value: string; label: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const attributeValue = typeof filter.attribute === 'string' ? filter.attribute : '';
  const mainKey = attributeValue.split('.')[0] ?? attributeValue;
  let data = columnsConfig?.find(val => val.key === filter.attribute);
  if (!data) {
    data = columnsConfig?.find(val => val.key === mainKey);
  }

  // --- Shopify Order Number async fetch ---
  const fetchOrders = async (term: string) => {
    if (!term || term.length < 2) {
      setOrderOptions([]);
      return;
    }
    try {
      setIsLoading(true);
      const urlParams = new URLSearchParams(window.location.search);
      const page = urlParams.get('page') || '1';
      const limit = urlParams.get('limit') || '25';
      const queryValue = `shopifyOrderNumber:like:${term}`;
      const encodedQuery = encodeURIComponent(queryValue);
      const response = await apiClient.get(`/orders?q=${encodedQuery}&page=${page}&limit=${limit}`);
      if (response.data && response.data.data) {
        const orders = response.data.data.map((order: any) => ({
          value: order.shopifyOrderNumber,
          label: `${order.shopifyOrderNumber}`,
        }));
        setOrderOptions(orders);
      }
    } catch (error) {
      setOrderOptions([]);
    } finally {
      setIsLoading(false);
    }
  };
  useDebounce(
    () => {
      if (filter.attribute === 'shopifyOrderNumber' && searchTerm) {
        fetchOrders(searchTerm);
      }
    },
    2000,
    [searchTerm],
  );

  // --- Shopify Native Variant async fetch ---
  const fetchShopifyVariantsHandler = async () => {
    setShopifyVariantsLoading(true);
    try {
      const response = await fetchShopifyVariants();
      const formattedData = Object.entries(response)
        .map(([key, values]) =>
          (values as string[]).map(value => ({
            key,
            value,
          })),
        )
        .flat();
      setShopifyVariants(formattedData);
    } catch (error) {
      setShopifyVariants([]);
    } finally {
      setShopifyVariantsLoading(false);
    }
  };

  // --- SKU, parentSku, childSku async fetch ---
  const debouncedFetchSKUs = useCallback(
    debounce(async (inputValue: string) => {
      if (!inputValue) {
        setSkuOptions([]);
        return;
      }
      setIsLoading(true);
      try {
        const skus = await apiCall<{ data: SingleSKU[] }>(
          'get',
          `/product-sku?q=sku:like:${inputValue}`,
        );
        setSkuOptions(
          (skus?.data || []).map((elem: SingleSKU) => {
            if (
              filter.attribute === 'parentSku' ||
              filter.attribute === 'parentSku.parentSku' ||
              filter.attribute === 'childSku' ||
              filter.attribute === 'childSku.childSku'
            ) {
              return {
                label: elem?.sku ?? '',
                value: elem?.id ?? '',
              };
            } else {
              return {
                label: elem?.sku ?? '',
                value: elem?.sku ?? '',
              };
            }
          }),
        );
      } catch (error) {
        setSkuOptions([]);
      } finally {
        setIsLoading(false);
      }
    }, 1000),
    [filter.attribute],
  );

  // --- Products async fetch ---
  const debouncedFetchProducts = useCallback(
    debounce(async (inputValue: string) => {
      if (!inputValue) {
        setSkuOptions([]);
        return;
      }
      setIsLoading(true);
      try {
        const response = await apiClient.get(`/product-sku/products?q=${inputValue}`);
        setSkuOptions(
          (response.data.data || []).map((product: { name: any }) => ({
            label: product.name,
            value: product.name,
          })),
        );
      } catch (error) {
        setSkuOptions([]);
      } finally {
        setIsLoading(false);
      }
    }, 1000),
    [],
  );

  // --- Value normalization for multi_select ---
  let value: any = '';
  if (data?.type === 'multi_select') {
    if (Array.isArray(filter.value)) {
      value = filter.value as (string | number | boolean)[];
    } else if (filter.value !== undefined && filter.value !== null && filter.value !== '') {
      value = [filter.value as string | number | boolean];
    } else {
      value = [];
    }
  } else {
    value = filter.value;
  }

  // --- Pass correct async options and handlers to FilterValueField ---
  let asyncOptions = undefined;
  let loading = false;
  let onSearch: ((term: string) => void) | undefined = undefined;
  if (filter.attribute === 'shopifyOrderNumber') {
    asyncOptions = orderOptions.map(opt => ({ label: opt.label, value: opt.value }));
    loading = isLoading;
    onSearch = setSearchTerm;
  } else if (filter.attribute === 'shopifyNativeVariant') {
    asyncOptions = shopifyVariants.map(opt => ({ label: opt.key, value: opt.value }));
    loading = shopifyVariantsLoading;
    onSearch = fetchShopifyVariantsHandler;
  } else if (
    filter.attribute === 'sku' ||
    filter.attribute === 'parentSku' ||
    filter.attribute === 'parentSku.parentSku' ||
    filter.attribute === 'childSku' ||
    filter.attribute === 'childSku.childSku'
  ) {
    asyncOptions = skuOptions.map(opt => ({ label: opt.label, value: opt.value }));
    loading = isLoading;
    onSearch = debouncedFetchSKUs;
  } else if (filter.attribute === 'products' || filter.attribute === 'products.name') {
    asyncOptions = skuOptions.map(opt => ({ label: opt.label, value: opt.value }));
    loading = isLoading;
    onSearch = debouncedFetchProducts;
  }

  return (
    <Grid container spacing={4}>
      <Grid size={12} display="flex" alignItems="center" justifyContent="space-between">
        <Typography fontSize={14} fontWeight={500}>
          Filter {index + 1 + '.' + subIndex}
        </Typography>
        <IconButton color="error" onClick={() => onRemove(index, subIndex)}>
          <DeleteIcon />
        </IconButton>
      </Grid>
      <Grid size={6}>
        <CustomSelect
          value={
            filter.attribute === 'products.name'
              ? 'products'
              : filter.attribute === 'parentSku.parentSku'
                ? 'parentSku'
                : filter.attribute === 'childSku.childSku'
                  ? 'childSku'
                  : filter.attribute || ''
          }
          onChange={val => {
            onChange(index, 'value', '', subIndex);
            const data = columnsConfig?.find(v => v.key === val);
            if (val === 'shopifyNativeVariant') {
              fetchShopifyVariantsHandler();
            }
            let attr = data?.key;
            if (attr === 'products') attr = 'products.name';
            if (attr === 'parentSku') attr = 'parentSku.parentSku';
            if (attr === 'childSku') attr = 'childSku.childSku';
            onChange(index, 'attribute', attr, subIndex);
          }}
          label="Attribute"
          options={columnsConfig.map(col => ({
            key: col.key,
            label: col.label ? String(col.label) : String(col.key),
          }))}
        />
      </Grid>
      <Grid size={6}>
        <CustomSelect
          value={filter.operator}
          onChange={val => onChange(index, 'operator', val, subIndex)}
          label="Operator"
          options={(() => {
            if (filter.attribute === 'orderDate') return filterEqData.date;
            if (filter.attribute === 'priorities') return filterEqData.number;
            if (filter.attribute === 'orderStatus') return filterEqData.number;
            if (filter.attribute === 'shopifyOrderNumber') return filterEqData.number;
            if (filter.attribute === 'shopifyNativeVariant')
              return filterEqData.shopifyNativeVariant;
            if (filter.attribute === 'products.category') return filterEqData.number;
            return filterEqData[data?.type as 'number' | 'all'] ?? filterEqData.all;
          })().map((op: { label: string; value: string }) => ({ key: op.value, label: op.label }))}
        />
      </Grid>
      <Grid size={12}>
        {data && (
          <FilterValueField
            data={data as any}
            value={value}
            onChange={val => onChange(index, 'value', val, subIndex)}
            label={data.label || 'Value'}
            asyncOptions={asyncOptions}
            isLoading={loading}
            onSearch={onSearch}
          />
        )}
      </Grid>
    </Grid>
  );
};

const FilterComponent: React.FC<FilterComponentProps> = ({
  filters,
  columnsConfig,
  handleAdd,
  handleChange,
  handleRemove,
}) => {
  return (
    <div>
      <Stack spacing={1}>
        {filters.map((filter, index) => (
          <React.Fragment key={`filter-group-${index}`}>
            {filter.map((attributes, subIndex) => (
              <React.Fragment key={`filter-item-${index}-${subIndex}`}>
                <FilterGroup
                  subIndex={subIndex}
                  index={index}
                  filter={attributes}
                  columnsConfig={columnsConfig}
                  onChange={handleChange}
                  onRemove={handleRemove}
                />
              </React.Fragment>
            ))}
            <div className="flex justify-end">
              <Button
                onClick={() => handleAdd(index)}
                variant="text"
                sx={{ width: 'fit-content', margin: '10px 0 10px 0' }}
              >
                AND
              </Button>
            </div>
            <Divider sx={{ marginTop: 2, marginBottom: 2 }} />
          </React.Fragment>
        ))}
      </Stack>
      <div className="flex justify-center">
        <Button
          onClick={() => handleAdd()}
          variant="outlined"
          sx={{ width: 'fit-content', margin: '10px 0 10px 0' }}
        >
          OR
        </Button>
      </div>
    </div>
  );
};

export default FilterComponent;
