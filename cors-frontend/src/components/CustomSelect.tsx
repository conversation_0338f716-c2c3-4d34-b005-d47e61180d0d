import { FormControl, InputLabel, Select, MenuItem, OutlinedInput, Box, Chip } from '@mui/material';
import React from 'react';
import type { FilterValueFieldSelectValue } from '@/types/filter-value-field.types';

export interface Option {
  key: string | number | boolean;
  label: string;
}

interface CustomSelectProps {
  value: FilterValueFieldSelectValue;
  onChange: (value: FilterValueFieldSelectValue) => void;
  options: Option[];
  label: string;
  multiple?: boolean;
  renderOption?: (option: Option) => React.ReactNode;
  [key: string]: any;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  value,
  onChange,
  options,
  label,
  multiple = false,
  renderOption,
  ...props
}) => (
  <FormControl fullWidth size="small">
    <InputLabel>{label}</InputLabel>
    <Select
      multiple={multiple}
      value={
        multiple
          ? Array.isArray(value)
            ? value.map(v => (typeof v === 'boolean' ? String(v) : v))
            : []
          : typeof value === 'boolean'
            ? String(value)
            : value
      }
      onChange={e => {
        let v = e.target.value;
        if (multiple) {
          v = (v as string[]).map(val => (val === 'true' ? true : val === 'false' ? false : val));
        } else {
          v = v === 'true' ? 'true' : v === 'false' ? 'false' : v;
        }
        onChange(v);
      }}
      label={label}
      input={<OutlinedInput label={label} />}
      renderValue={selected =>
        multiple ? (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {(selected as (string | number | boolean)[]).map(val => {
              const found = options.find(opt => String(opt.key) === String(val));
              return <Chip key={String(val)} label={found ? found.label : String(val)} />;
            })}
          </Box>
        ) : (
          (options.find(opt => String(opt.key) === String(selected))?.label ?? '')
        )
      }
      {...props}
    >
      {options.map(option => (
        <MenuItem key={String(option.key)} value={String(option.key)}>
          {renderOption ? renderOption(option) : option.label}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
);

export default CustomSelect;
