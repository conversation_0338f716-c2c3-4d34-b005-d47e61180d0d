import { ActionsTarget, ActionsType, AppAbility } from '@/libs/casl/ability';
import { useAbility } from '@/libs/casl/AbilityContext';
import React, { ReactElement } from 'react';

interface ProtectedProps {
  children: ReactElement;
  actionTarget?: ActionsTarget;
  action?: ActionsType | undefined;
}

export const RoleProtected = ({ action, actionTarget, children }: ProtectedProps) => {
  const ability: AppAbility | null = useAbility();

  const disabled = action && actionTarget ? !ability?.can(action, actionTarget) : false;
  const className =
    action && actionTarget && !ability?.can(action, actionTarget) && 'pointer-events-none';

  return React.cloneElement(children, { disabled, className });
};
