'use client';
import { CloudDownload } from '@mui/icons-material';
import { Box, Card, CardContent, CardMedia, IconButton, Typography } from '@mui/material';
import { handleDownload } from '../../services/queues.services';

const SingleImageViewCard = ({
  imageUrl,
  title,
  downloadUrl,
  isDanger = false,
  imageName,
}: {
  imageUrl: string;
  title: string;
  downloadUrl: string;
  isDanger?: boolean;
  imageName?: string;
}) => {
  return (
    <Card
      sx={{
        height: '100%',
        transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
        py: '10px',

        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: '0 12px 24px rgba(0,0,0,0.15)',
        },
      }}
    >
      <CardMedia
        component="img"
        height="300"
        image={imageUrl}
        alt="Mountain Landscape"
        sx={{
          objectFit: 'none',
          maxHeight: '300px',
          backgroundColor: 'transparent',
          backdropFilter: 'blur(1px)',
          my: 4,
        }}
      />
      <CardContent
        sx={{
          textAlign: 'center',
          pb: 3,
          borderTop: '1px solid rgba(231, 227, 252, 0.12)',
          mt: 2,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: 5, justifyContent: 'left' }}>
            <Typography variant="h6" color={isDanger ? '#ff4c51' : 'primary'} textAlign="left">
              {title}
            </Typography>
            <Typography variant="body1" color="text.secondary" textAlign="left">
              {imageName}
            </Typography>
          </div>

          <IconButton
            title="Download"
            size="small"
            onClick={() => handleDownload(downloadUrl, `${title}.png`)}
            sx={{
              color: 'var(--mui-palette-primary-main)',
              fontSize: '1.5rem',
            }}
          >
            <CloudDownload />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SingleImageViewCard;
