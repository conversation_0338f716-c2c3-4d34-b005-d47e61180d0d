'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Alert,
  Paper,
  Avatar,
  CardMedia,
  TextField,
  LinearProgress,
} from '@mui/material';
import {
  Support,
  Info,
  CloudUpload,
  Delete,
  CheckCircle,
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import PublicStatusChip from './PublicStatusChip';
import { PublicOrderStatus } from '@/types/public-order-status.types';

interface CustomerContactViewProps {
  orderData: PublicOrderStatus;
}

const CustomerContactView: React.FC<CustomerContactViewProps> = ({ orderData }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [customerMessage, setCustomerMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragOver, setDragOver] = useState(false);

  // Get the first line item with selected image/message
  const contactItem = orderData.lineItems.find(item => item.selectedImage);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        return;
      }
      
      setSelectedFile(file);
      
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileChange({ target: { files: [file] } } as any);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const handleDeleteImage = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const handleSubmit = async () => {
    if (!customerMessage.trim()) {
      toast.error('Please provide your response message');
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Your response has been submitted successfully!');
      
      // Reset form
      handleDeleteImage();
      setCustomerMessage('');
    } catch (error) {
      toast.error('Failed to submit response. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Header Card */}
      <Card 
        elevation={3} 
        sx={{ 
          mb: 3,
          background: 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)',
          color: 'white'
        }}
      >
        <CardContent sx={{ py: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
              <Support fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold">
                Customer Contact Needed
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                Order #{orderData.shopifyOrderNumber}
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Order Status:
            </Typography>
            <PublicStatusChip status={orderData.orderStatus} variant="order" size="small" />
            
            {contactItem && (
              <>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Item #{contactItem.itemNumber}:
                </Typography>
                <PublicStatusChip status={contactItem.status} variant="lineItem" size="small" />
              </>
            )}
          </Box>
        </CardContent>
      </Card>

      {isSubmitting && <LinearProgress sx={{ mb: 2 }} />}

      {/* Team Message Section */}
      {contactItem?.selectedImage && (
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <Info color="info" />
              <Typography variant="h6" fontWeight="bold" color="info.main">
                Message from Our Team
              </Typography>
            </Box>
            
            <Card 
              sx={{ 
                maxWidth: 400, 
                mb: 2,
                borderRadius: 2,
                overflow: 'hidden'
              }}
            >
              <CardMedia
                component="img"
                height="250"
                image={contactItem.selectedImage.url}
                alt="Reference image"
                sx={{ objectFit: 'cover' }}
              />
            </Card>
            
            {contactItem.selectedImage.message && (
              <Alert 
                severity="info" 
                sx={{ 
                  borderRadius: 2,
                  '& .MuiAlert-message': {
                    width: '100%'
                  }
                }}
              >
                <Typography variant="body2">
                  <strong>Team Message:</strong>
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {contactItem.selectedImage.message}
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Response Message Section */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Your Response
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            placeholder="Please provide your response, clarifications, or any additional information requested by our team..."
            value={customerMessage}
            onChange={(e) => setCustomerMessage(e.target.value)}
            required
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2
              }
            }}
          />
        </CardContent>
      </Card>

      {/* Optional File Upload Section */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <CloudUpload color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Attach Image (Optional)
            </Typography>
          </Box>

          {/* Image Preview */}
          {previewUrl && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Attached Image:
              </Typography>
              <Box position="relative" display="inline-block">
                <Card sx={{ maxWidth: 300, borderRadius: 2 }}>
                  <CardMedia
                    component="img"
                    height="200"
                    image={previewUrl}
                    alt="Attached image"
                    sx={{ objectFit: 'cover' }}
                  />
                </Card>
                <Button
                  variant="contained"
                  color="error"
                  size="small"
                  startIcon={<Delete />}
                  onClick={handleDeleteImage}
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    minWidth: 'auto',
                    borderRadius: 2
                  }}
                >
                  Remove
                </Button>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {selectedFile?.name} ({((selectedFile?.size || 0) / 1024 / 1024).toFixed(2)} MB)
              </Typography>
            </Box>
          )}

          {/* Upload Zone */}
          {!previewUrl && (
            <Paper
              variant="outlined"
              sx={{
                p: 3,
                textAlign: 'center',
                borderStyle: 'dashed',
                borderWidth: 2,
                borderColor: dragOver ? 'primary.main' : 'grey.300',
                bgcolor: dragOver ? 'primary.50' : 'grey.50',
                borderRadius: 2,
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'primary.50'
                }
              }}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={() => document.getElementById('contact-file-input')?.click()}
            >
              <input
                id="contact-file-input"
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                style={{ display: 'none' }}
              />
              
              <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Drop image here (optional)
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                or click to browse files
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Supports: JPG, PNG, GIF (Max 10MB)
              </Typography>
            </Paper>
          )}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Box textAlign="center">
        <Button
          variant="contained"
          size="large"
          onClick={handleSubmit}
          disabled={isSubmitting || !customerMessage.trim()}
          sx={{
            minWidth: 200,
            py: 1.5,
            borderRadius: 2,
            fontSize: '1.1rem'
          }}
        >
          {isSubmitting ? 'Submitting...' : 'Send Response'}
        </Button>
      </Box>
    </Box>
  );
};

export default CustomerContactView;
