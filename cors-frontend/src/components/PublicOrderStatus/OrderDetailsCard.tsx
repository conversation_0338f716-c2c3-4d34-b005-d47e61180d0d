'use client';

import React from 'react';
import { Card, CardContent, Typography, Box, Paper, Stack, Avatar } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { CalendarToday, Person, LocationOn, Inventory } from '@mui/icons-material';
import { format } from 'date-fns';
import PublicStatusChip from './PublicStatusChip';
import { PublicOrderStatus } from '@/types/public-order-status.types';

interface OrderDetailsCardProps {
  orderData: PublicOrderStatus;
}

const OrderDetailsCard: React.FC<OrderDetailsCardProps> = ({ orderData }) => {
  console.log('orderData', orderData);
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Box sx={{ mb: 4 }}>
      {/* Header Card */}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <CardContent sx={{ py: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
              <Inventory fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold">
                Order #{orderData.shopifyOrderNumber}
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                Track your order progress
              </Typography>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Current Status:
            </Typography>
            <PublicStatusChip status={orderData.orderStatus} variant="order" size="medium" />
          </Box>
        </CardContent>
      </Card>

      {/* Details Grid */}
      <Grid container spacing={3}>
        {/* Order Information */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <CalendarToday color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Order Information
                </Typography>
              </Box>

              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Order Date
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {formatDate(orderData.orderDate)}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Last Updated
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {formatDate(orderData.statusUpdatedAt)}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Total Items
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {orderData.itemCount} {orderData.itemCount === 1 ? 'item' : 'items'}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Customer Information */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <Person color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Customer Information
                </Typography>
              </Box>

              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Customer Name
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {orderData.customerFirstName} {orderData.customerLastName}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Email Address
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {orderData.customerEmail}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Shipping Address */}
        {orderData.shippingAddress && (
          <Grid size={{ xs: 12 }}>
            <Card elevation={2}>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <LocationOn color="primary" />
                  <Typography variant="h6" fontWeight="bold">
                    Shipping Address
                  </Typography>
                </Box>

                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    // bgcolor: 'grey.50',
                    borderRadius: 2,
                  }}
                >
                  <Typography variant="body1" fontWeight={500} gutterBottom>
                    {orderData.shippingAddress.address1}
                  </Typography>
                  {orderData.shippingAddress.address2 && (
                    <Typography variant="body1" gutterBottom>
                      {orderData.shippingAddress.address2}
                    </Typography>
                  )}
                  <Typography variant="body1">
                    {orderData.shippingAddress.city}, {orderData.shippingAddress.province}{' '}
                    {orderData.shippingAddress.province_code}
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {orderData.shippingAddress.country}
                  </Typography>
                </Paper>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default OrderDetailsCard;
