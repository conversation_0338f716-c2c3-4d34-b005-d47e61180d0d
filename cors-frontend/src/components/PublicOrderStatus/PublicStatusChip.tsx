'use client'

import React from 'react';
import Chip from '@mui/material/Chip';
import { 
  ORDER_STATUS_LABELS, 
  ORDER_STATUS_COLORS, 
  LINE_ITEM_STATUS_LABELS, 
  LINE_ITEM_STATUS_COLORS,
  OrderStatusType 
} from '@/types/public-order-status.types';

interface PublicStatusChipProps {
  status: string;
  variant: 'order' | 'lineItem';
  size?: 'small' | 'medium';
}

const PublicStatusChip: React.FC<PublicStatusChipProps> = ({ 
  status, 
  variant, 
  size = 'medium' 
}) => {
  const getStatusLabel = () => {
    if (variant === 'order') {
      return ORDER_STATUS_LABELS[status as OrderStatusType] || status;
    }
    return LINE_ITEM_STATUS_LABELS[status] || status;
  };

  const getStatusColor = () => {
    if (variant === 'order') {
      return ORDER_STATUS_COLORS[status as OrderStatusType] || 'default';
    }
    return LINE_ITEM_STATUS_COLORS[status] || 'default';
  };

  return (
    <Chip
      label={getStatusLabel()}
      color={getStatusColor()}
      size={size}
      variant="filled"
      sx={{
        fontWeight: 500,
        textTransform: 'capitalize'
      }}
    />
  );
};

export default PublicStatusChip;
