import { useState, useEffect, useRef } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  type FilterFn,
  type SortingState,
  RowData,
  Row,
} from '@tanstack/react-table';

import { useSearchParams, useRouter } from 'next/navigation';

import { rankItem } from '@tanstack/match-sorter-utils';
import TablePagination from '@mui/material/TablePagination';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Box from '@mui/material/Box';
import tableStyles from '@core/styles/table.module.css';
import classnames from 'classnames';
import Tooltip from '@mui/material/Tooltip';
import { Badge, Button, IconButton, Stack } from '@mui/material';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import BulkUpdateModal from '../Modals/BulkUpdateModal';
import FilterModal from '../Modals/FilterModal';
import { DataTableProps } from './types';
import { useDebounce } from 'react-use';
import { toast } from 'react-toastify';
import { RootState } from '@/redux-store';
import { useSelector } from 'react-redux';

declare module '@tanstack/table-core' {
  interface FilterFns {
    fuzzy: FilterFn<unknown>;
  }
  interface FilterMeta {
    itemRank: ReturnType<typeof rankItem>;
  }
  interface ColumnMeta<TData extends RowData, TValue> {
    showSortingIcon?: boolean;
    filterType?: string;
    options?: string[];
    isBoolean?: boolean;
    booleanOptions?: { [key: string]: boolean };
    optKey?: string;
  }
}

const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta({ itemRank });
  return itemRank.passed;
};

const blaclistForLikeQuery = ['num', 'bool'];

export default function DataTable<T>({
  data,
  columns,
  loading = false,
  title,
  onRowClick,
  searchPlaceholder,
  endpoint,
  enableFilters = false,
  globalSearch = false,
  initialPageSize = 25,
  initialPageIndex = 0,
  totalCount = 0,
  enableBulkUpdate = false,
  onBulkApply,
  reloadData,
  bulkDataFields,
  filterFields,
  storeName,
  onApplyFilters,
  filterCount,
  onPageChange,
  onLimitChange,
  onPaginationUpdate,
  page,
  limit,
}: DataTableProps<T> & { onPageChange?: (page: number) => void }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [pageIndex, setPageIndex] = useState(initialPageIndex);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [sorting, setSorting] = useState<SortingState>([]);
  const params = new URLSearchParams(searchParams.toString());
  const [globalFilter, setGlobalFilter] = useState(extractGlobalSearchFromParams());
  const [bulkUpdateModal, setBulkUpdateModal] = useState(false);
  const [filterModal, setFilterModal] = useState(false);
  const [rowSelection, setRowSelection] = useState({});
  const pageRef = useRef<null | number>(null);

  useDebounce(() => genericSearch(), 500, [globalFilter]);
  useEffect(() => {
    // Initialize pagination state from URL parameters
    const pageParam = searchParams.get('page');
    const limitParam = searchParams.get('limit');

    if (pageParam) {
      setPageIndex(Number(pageParam) - 1);
    }
    if (limitParam) {
      setPageSize(Number(limitParam));
    }
  }, [searchParams]);

  useEffect(() => {
    // Update URL when sorting, pagination, or page size changes

    if (pageRef.current === pageIndex) return;
    pageRef.current = pageIndex;
    params.set('page', String(pageIndex + 1));
    params.set('limit', String(pageSize));
    const sortQuery = sorting
      .map(
        ({ id, desc }) => `${id === 'roles' ? 'entity.roles.name' : id}:${desc ? 'desc' : 'asc'}`,
      )
      .join(';');

    if (sortQuery) {
      params.set('sort', sortQuery);
    } else {
      params.delete('sort');
    }
    if (!endpoint) return;

    router.replace(`/${endpoint}?${params.toString()}`);
  }, [sorting, pageSize, pageIndex, endpoint]);

  const genericSearch = () => {
    params.set('page', String(1));
    params.set('limit', String(pageSize));
    const globalFilter = table.getState().globalFilter;
    const columnsWithFilterEnabled = table.getAllColumns().filter(column => column.getCanFilter());

    let qFilter = '';
    const fqFilters: string[] = [];
    for (const filter of columnsWithFilterEnabled) {
      if (filter.columnDef.meta?.isBoolean) {
        if (globalFilter?.trim().toLowerCase().includes('active')) {
          globalFilter?.trim().replace('Active', '');
          const expression = `isActive:eq:${globalFilter?.trim().toLowerCase().includes('inactive') ? false : true}`;
          if (expression) fqFilters.push(expression);
        } else continue;
      } else {
        const isNumaric = blaclistForLikeQuery.includes(
          filter?.columnDef?.meta?.filterType?.toLowerCase() as string,
        );
        const pureTextFilter =
          filter?.columnDef?.meta?.filterType === 'num'
            ? globalFilter?.replace(/[^\d]/g, '')
            : globalFilter?.replace(/\bactive|inactive\b/gi, '');
        qFilter = pureTextFilter.trim()
          ? `${qFilter && `${qFilter};`}${filter.columnDef.meta?.optKey ? filter.columnDef.meta?.optKey : filter.id}:${isNumaric ? 'eq' : 'like'}:${pureTextFilter}`
          : qFilter;
      }
    }

    if (qFilter) {
      params.set('q', qFilter);
    } else {
      params.delete('q');
    }

    if (fqFilters.length > 0) {
      params.set('fq', fqFilters.join(';'));
    } else {
      params.delete('fq');
    }
    if (!endpoint) return;
    pageRef.current = 0;
    setPageIndex(0);
    router.replace(`/${endpoint}?${params.toString()}`);
  };

  function extractGlobalSearchFromParams() {
    const fq = searchParams.get('fq') || '';
    const q = searchParams.get('q') || '';
    let resultQ: string | undefined = '';

    q.split(';').forEach(part => {
      resultQ = resultQ?.includes(`${part.split(':').pop()}`) ? resultQ : part.split(':').pop();
    });
    const resultFQ = fq
      .split(';')
      .map(part =>
        part?.split(':').pop() === 'true' ? 'Active' : part?.split(':').pop() && 'InActive',
      )
      .join('');
    return `${resultQ}${resultFQ && `${resultFQ}`}`;
  }

  const nextPage = (newPageIndex: number, newPageSize: number) => {
    setPageIndex(newPageIndex);
    setPageSize(newPageSize);
  };

  const onSortChange = (columnId: string) => {
    setSorting(prevSorting => {
      const currentSort = prevSorting.find(sort => sort.id === columnId);
      let newSorting: SortingState;

      if (!currentSort) {
        // No sorting for this column, set to ascending
        newSorting = [{ id: columnId, desc: false }];
      } else if (currentSort.desc === false) {
        // Currently ascending, set to descending
        newSorting = [{ id: columnId, desc: true }];
      } else {
        // Currently descending, reset to none
        newSorting = [];
      }

      return newSorting;
    });
  };

  const table = useReactTable({
    data,
    columns,
    state: {
      globalFilter,
      pagination: { pageIndex, pageSize },
      sorting,
      rowSelection,
    },
    enableMultiSort: false,
    onGlobalFilterChange: setGlobalFilter,
    manualSorting: true,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,

    filterFns: { fuzzy: fuzzyFilter },
    manualFiltering: true,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    onPaginationChange: updater => {
      if (typeof updater === 'function') {
        const newPagination = updater({
          pageIndex,
          pageSize,
        });
        nextPage(newPagination.pageIndex, newPagination.pageSize);
      } else {
        nextPage(updater.pageIndex, updater.pageSize);
      }
    },
  });
  return (
    <Card>
      <Stack display={'flex'} justifyContent={'space-between'} flexDirection="row" className="p-3">
        {enableFilters && (
          <IconButton
            onClick={() => {
              setFilterModal(true);
            }}
            color="primary"
          >
            <Badge invisible={filterCount === 0} badgeContent={filterCount} color="secondary">
              <FilterAltIcon />
            </Badge>
          </IconButton>
        )}
        {enableBulkUpdate && (
          <Button
            disabled={table.getSelectedRowModel().rows.length === 0}
            sx={{ justifySelf: 'flex-end' }}
            variant="contained"
            onClick={() => {
              setBulkUpdateModal(true);
            }}
          >
            Bulk Update
          </Button>
        )}
      </Stack>

      {enableFilters && (
        <Box marginBlock={3} marginInline={5} display="flex" flexWrap="wrap">
          <FilterModal
            columnsConfig={filterFields}
            open={filterModal}
            onClose={() => {
              setFilterModal(false);
            }}
            title={title || 'Filter Orders'}
            storeName={storeName}
            applyFilters={onApplyFilters}
          />
        </Box>
      )}
      {enableBulkUpdate && (
        <BulkUpdateModal
          open={bulkUpdateModal}
          onClose={() => setBulkUpdateModal(false)}
          key={'test'}
          attributes={bulkDataFields}
          onApply={(attributes, resetValues) => {
            if (!onBulkApply) return;
            const rowIds: string[] = table
              .getSelectedRowModel()
              .rows.map((row: Row<any>) => row.original.id);
            onBulkApply({ ids: rowIds, attributes }).then(({ data }) => {
              setBulkUpdateModal(false);
              toast.success(data.status ?? 'Sucessfully Updated!');
              resetValues();
              if (typeof reloadData === 'function') reloadData();
              setRowSelection({});
            });
          }}
        />
      )}
      {globalSearch && (
        <CardContent sx={{ display: 'flex', gap: 10 }}>
          <TextField
            fullWidth
            label={searchPlaceholder || 'Search'}
            variant="outlined"
            size="small"
            value={globalFilter}
            className="w-1/4"
            onChange={e => {
              setGlobalFilter(e.target.value);
            }}
          />
        </CardContent>
      )}

      <div className="overflow-x-auto">
        <table className={tableStyles.table}>
          <thead>
            {table?.getHeaderGroups()?.map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th key={header.id}>
                    {header.isPlaceholder || !header.column.getCanSort() ? (
                      flexRender(header.column.columnDef.header, header.getContext())
                    ) : (
                      <Tooltip
                        title={
                          header.column.getIsSorted()
                            ? header.column.getIsSorted() === 'asc'
                              ? 'Sorted ascending (click for descending)'
                              : 'Sorted descending (click to reset)'
                            : 'Click to sort ascending'
                        }
                        arrow
                      >
                        <div
                          className="inline-flex items-center gap-1 cursor-pointer select-none"
                          onClick={() => onSortChange(header.column.id)}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {header.column.getCanSort() &&
                            header.column.columnDef.meta?.showSortingIcon !== false && (
                              <span className="text-sm">
                                {{
                                  asc: '↑',
                                  desc: '↓',
                                  none: '↕️',
                                }[header.column.getIsSorted() as string] || '↕️'}
                              </span>
                            )}
                        </div>
                      </Tooltip>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns?.length} className="text-center py-4">
                  <CircularProgress />
                </td>
              </tr>
            ) : (table?.getFilteredRowModel()?.rows || [])?.length === 0 ? (
              <tr>
                <td colSpan={columns?.length} className="text-center">
                  No data available
                </td>
              </tr>
            ) : (
              table.getRowModel().rows.map(row => (
                <tr
                  key={row.id}
                  onClick={() => onRowClick?.(row.original)}
                  className={classnames({ 'cursor-pointer': onRowClick })}
                >
                  {row.getVisibleCells().map(cell => (
                    <td key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <TablePagination
        rowsPerPageOptions={[25, 50, 100, 500]}
        component="div"
        count={totalCount}
        rowsPerPage={table.getState().pagination.pageSize}
        page={table.getState().pagination.pageIndex}
        onPageChange={(_, page) => {
          table.setPageIndex(page);
          if (onPageChange) onPageChange(page);
          onPaginationUpdate?.(page, limit || table.getState().pagination.pageSize);
        }}
        onRowsPerPageChange={e => {
          const newLimit = Number(e.target.value);
          table.setPageSize(newLimit);
          onPaginationUpdate?.(
            page || table.getState().pagination.pageIndex,
            Number(e.target.value),
          );
          if (typeof onLimitChange === 'function') onLimitChange(newLimit);
        }}
      />
    </Card>
  );
}
