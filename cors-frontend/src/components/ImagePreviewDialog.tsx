import React, { useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  Dialog<PERSON>ontent,
  IconButton,
  Typography,
  Box,
  Slide,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { TransitionProps } from '@mui/material/transitions';

// Slide transition for the image preview dialog
const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

interface ImagePreviewDialogProps {
  open: boolean;
  onClose: () => void;
  imageUrl: string | null;
  title?: string;
}

const ImagePreviewDialog = ({
  open,
  onClose,
  imageUrl,
  title = 'Image Preview',
}: ImagePreviewDialogProps) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);

  // Handle mouse move for zoom effect
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!imageRef.current) return;

    const { left, top, width, height } = imageRef.current.getBoundingClientRect();
    const x = (e.clientX - left) / width;
    const y = (e.clientY - top) / height;

    setMousePosition({ x, y });
  };

  // Handle mouse enter to increase zoom
  const handleMouseEnter = () => {
    setZoomLevel(1.5); // Zoom in when mouse enters
  };

  // Handle mouse leave to reset zoom
  const handleMouseLeave = () => {
    setZoomLevel(1); // Reset zoom when mouse leaves
  };

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          zIndex: theme => theme.zIndex.modal + 1,
        },
        zIndex: theme => theme.zIndex.modal + 1,
      }}
    >
      <DialogTitle
        sx={{ m: 0, p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <Typography variant="h6" component="div">
          {title}
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: theme => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          p: 0,
          overflow: 'hidden',
          height: '70vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'rgba(0,0,0,0.03)',
        }}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {imageUrl && (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
            }}
          >
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Preview"
              style={{
                maxWidth: '90%',
                maxHeight: '90%',
                transform: `scale(${zoomLevel})`,
                transformOrigin: `${mousePosition.x * 100}% ${mousePosition.y * 100}%`,
                transition: 'transform 0.1s ease',
              }}
            />
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ImagePreviewDialog;
