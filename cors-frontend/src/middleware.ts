import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname
  const protectedRoutes = ['/home', '/dashboard']
  const authRoutes = ['/login', '/register', '/forgot-password']
  const publicRoutes = ['/order-status']
  const session = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET ?? process.env.AUTH_SECRET
  })

  // Allow access to public routes without authentication
  if (publicRoutes.some(route => path.startsWith(route))) {
    return NextResponse.next()
  }

  // Redirect logic
  if (!session && protectedRoutes.some(route => path.startsWith(route))) {
    // Redirect to login if accessing protected route without session
    return NextResponse.redirect(new URL('/login', req.url))
  }

  if (session && authRoutes.includes(path)) {
    // Redirect to home if already logged in but trying to access auth pages
    return NextResponse.redirect(new URL('/home', req.url))
  }

  return NextResponse.next()
}

// Configure which routes middleware should run on
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|images|favicon.ico).*)']
}
