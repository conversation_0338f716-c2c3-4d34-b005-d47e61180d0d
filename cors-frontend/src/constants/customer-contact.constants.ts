export interface IssueCategory {
  value: string;
  label: string;
  defaultMessage: string;
}

// Issue categories for customer contact dropdown
export const ISSUE_CATEGORIES: IssueCategory[] = [
  { 
    value: 'image_quality', 
    label: 'Image Quality Issue', 
    defaultMessage: 'We noticed the image you provided for your order has quality issues. Could you please provide a higher resolution image?' 
  },
  { 
    value: 'missing_image', 
    label: 'Missing Image', 
    defaultMessage: 'We noticed your order is missing an image that we need to complete your product. Could you please provide the required image?' 
  },
  { 
    value: 'artwork_approval', 
    label: 'Artwork Approval Needed', 
    defaultMessage: 'We\'ve completed the artwork for your order and need your approval before we proceed with production. Please review and let us know if you approve or need any changes.' 
  },
  { 
    value: 'customization_details', 
    label: 'Customization Details Needed', 
    defaultMessage: 'We need additional details about the customization you requested for your order. Could you please provide more information?' 
  },
  { 
    value: 'order_clarification', 
    label: 'Order Clarification', 
    defaultMessage: 'We have a question about your order that we need clarified before we can proceed. Could you please provide more information?' 
  },
];