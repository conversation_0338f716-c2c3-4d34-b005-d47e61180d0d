export interface RevisionPhoto {
  id: string;
  fileName: string;
  fileUrl: string;
  thumbnailUrl: string;
  uploadedAt: string;
  uploadedBy: string;
}

export interface ArtworkRequest {
  id: string;
  message: string;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  photos: RevisionPhoto[];
}

export interface ArtworkRequestsSectionProps {
  itemId: string;
  requests: ArtworkRequest[];
  onAddRequest: (message: string, photos: File[]) => void;
}