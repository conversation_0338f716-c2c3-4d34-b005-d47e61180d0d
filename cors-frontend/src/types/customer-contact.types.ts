export interface CustomerImage {
  id: string;
  fileName: string;
  thumbnailUrl: string;
}

export interface ArtFile {
  id: string;
  fileName: string;
  thumbnailUrl: string;
}

export interface Question {
  id: string;
  issueCategory: string;
  selectedImages: string[];
  referenceImages: File[];
  message: string;
}

export interface CustomerContactDialogProps {
  open: boolean;
  onClose: () => void;
  onSend: (questions: Question[]) => void;
  customerImages: CustomerImage[];
  artFiles: ArtFile[];
  itemId: string;
}