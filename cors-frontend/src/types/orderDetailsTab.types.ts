export interface OrderDetailsTabProps {
    orderData: {
      id: string;
      shopifyOrderNumber: string;
      orderDate: string;
      orderStatus: string;
      priorities: string[];
      itemCount: number;
      customerFirstName: string;
      customerLastName: string;
      customerEmail: string;
      lineItems: Array<{
        id: string;
        itemNumber: string;
        productSku: { sku: string };
        quantity: number;
        status: string;
        isRemake?: boolean;
        priority?: string;
        flagged?: boolean;
        flagReason?: string;
        metadata?: Record<string, any>;
        artworkRequired?: boolean;
        artworkRequests?: any[];
        type?: string;
        addonType?: string;
        remakeReason?: string[];
        detailedRemakeReason?: string[];
      }>;
    };
  }
  