export interface PublicOrderStatus {
  shopifyOrderNumber: string;
  orderDate: string;
  orderStatus: string;
  statusUpdatedAt: string;
  customerFirstName: string;
  customerLastName: string;
  customerEmail: string;
  itemCount: number;
  lineItems: PublicLineItem[];
  shippingAddress?: {
    city: string;
    country: string;
    address1: string;
    address2?: string | null;
    province: string;
    country_code: string;
    province_code: string;
  };
}

export interface PublicLineItem {
  id: string;
  itemNumber: string;
  priority: string;
  quantity: number;
  status: string;
  currentStatus?: string;
  lastUpdatedAt?: string;
  rejectedImage?: {
    url: string;
    rejectionReason?: string;
  };
  selectedImage?: {
    url: string;
    message?: string;
  };
}

export interface RequestNewImageData {
  orderNumber: string;
  lineItemId: string;
  reason: string;
}

export type FormType = 'newImageRequest' | 'customerContactNeeded' | 'customerApproval';

export interface NewImageRequestForm {
  lineItemId: string;
  newImage?: File;
  dontHaveImage: boolean;
  customerText: string;
}

export interface CustomerContactForm {
  lineItemId: string;
  newImage?: File;
  customerText: string;
}

export interface CustomerApprovalForm {
  lineItemId: string;
  approved: boolean;
  revisionText?: string;
}

export type OrderStatusType = 'unfulfilled' | 'partially fulfilled' | 'fulfilled';

export const ORDER_STATUS_LABELS: Record<OrderStatusType, string> = {
  'unfulfilled': 'Processing',
  'partially fulfilled': 'Partially Shipped',
  'fulfilled': 'Completed'
};

export const ORDER_STATUS_COLORS: Record<OrderStatusType, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  'unfulfilled': 'warning',
  'partially fulfilled': 'info',
  'fulfilled': 'success'
};

export const LINE_ITEM_STATUS_LABELS: Record<string, string> = {
  'pending': 'Pending',
  'in_progress': 'In Progress',
  'completed': 'Completed',
  'shipped': 'Shipped',
  'cancelled': 'Cancelled'
};

export const LINE_ITEM_STATUS_COLORS: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  'pending': 'default',
  'in_progress': 'info',
  'completed': 'success',
  'shipped': 'primary',
  'cancelled': 'error'
};
