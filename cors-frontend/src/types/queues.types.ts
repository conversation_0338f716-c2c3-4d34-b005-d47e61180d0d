export interface TabQueues {
  id: string;
  name: string;
  maxItemsToAssign?: number;
  item_count?: number;
  disabled?: boolean;
  content?: React.ReactNode;
  items_count?: number;
}

export interface SingleQueueRow {
  priority?: string;
  sku?: string;
  filename?: string;
  attachment_url?: string;
  attachments?: Array<{
    attachment_url: string;
    name: string;
    cutout_pro_url?: string;
    completed_art_file_url?: string;
  }>;
  cropType?: string;
  order_number?: string;
  order_date?: string;
  line_item_status?: string;
  assigned_to?: string;
  cutout_pro_url?: string;
  id?: string;
}
export interface SingleQueueListData {
  attachments: Array<SingleQueueRow>;
  lineItems: Array<SingleQueueRow>;
  id: string;
  name: string;
}

export interface SingleQueueItem {
  attachments: SingleQueueRow;
  lineItems: SingleQueueRow;
  id: string;
  name: string;
}
