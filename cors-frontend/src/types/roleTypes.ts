import type { ThemeColor } from '@core/types'
import { Actions } from '@/libs/casl/ability'

export type UserStatusType = {
  [key: string]: ThemeColor
}

export type RolesData = {
  id?: number
  name: string
  isActive: boolean
  rolePermissions: RolePermission[]
  permissionCount?:string
}

export type RolePermission = {
  resource: string
  actions: string[]
}

export type PermissionData = {
  resource: string
  actions: string[] 
}

export type EnumPermissionData = {
  resource: string
  actions: Actions[]
  subResource?: {
    name: string
    actions: Actions[]
  }
  subResources?: {
    name: string
    actions: Actions[]
  }[]
}

export type RoleFormData = {
  id?: number
  name: string
  status: string
  rolePermissions: RolePermission[]
}
